package com.ruijing.store.statistic.api.search.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description: 有效购买量信息体
 * @DateTime: 2022/5/26 9:55
 */
@RpcModel("订单统计——有效购买量信息体")
public class ValidPurchaseCountDTO implements Serializable {

    private static final long serialVersionUID = 5098608673611856326L;

    @RpcModelProperty("采购人id")
    private Integer buyerId;

    @RpcModelProperty("商品id")
    private Long productId;

    @RpcModelProperty("搜索条件内的所有数量（包括取消和退货，不包括拆单母单）")
    private Integer allQuantity;

    @RpcModelProperty("搜索条件内取消的商品数量")
    private Integer cancelQuantity;

    @RpcModelProperty("有效成交数量：allQuantity - cancelQuantity")
    private Integer validQuantity;

    public Integer getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getAllQuantity() {
        return allQuantity;
    }

    public void setAllQuantity(Integer allQuantity) {
        this.allQuantity = allQuantity;
    }

    public Integer getCancelQuantity() {
        return cancelQuantity;
    }

    public void setCancelQuantity(Integer cancelQuantity) {
        this.cancelQuantity = cancelQuantity;
    }

    public Integer getValidQuantity() {
        return validQuantity;
    }

    public void setValidQuantity(Integer validQuantity) {
        this.validQuantity = validQuantity;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ValidPurchaseCountDTO{");
        sb.append("buyerId=").append(buyerId);
        sb.append(", productId=").append(productId);
        sb.append(", allQuantity=").append(allQuantity);
        sb.append(", cancelQuantity=").append(cancelQuantity);
        sb.append(", validQuantity=").append(validQuantity);
        sb.append('}');
        return sb.toString();
    }
}
