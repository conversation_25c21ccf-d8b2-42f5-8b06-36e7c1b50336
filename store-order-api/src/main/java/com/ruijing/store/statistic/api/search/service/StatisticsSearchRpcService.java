package com.ruijing.store.statistic.api.search.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.search.dto.GoodsReturnAggResDTO;
import com.ruijing.store.order.api.search.dto.OrderAggregationResultDTO;
import com.ruijing.store.order.api.search.dto.OrderDateHistogramResultDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerParamDTO;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggDTO;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggRequest;
import com.ruijing.store.statistic.api.search.dto.ValidPurchaseCountDTO;

import java.util.Collections;
import java.util.List;
import java.util.Map;

// TODO:要缓存一个品牌名称和id的对应关系给到前端，可以每半小时刷新，或者手动触发（可以写后门也可以提请产品需求，必须先保证流程）

/**
 * 给统计业务使用的rpc方法
 * @Date: 2021/04/28 15:10
 * @Author: Zeng Yanru
 */
@RpcApi(value = "平台统计——订单统计")
public interface StatisticsSearchRpcService {

    /**
     * 需要排序和聚合，按照单位、供应商、品牌、时间、订单状态（需要保证这些聚合维度都是互斥的，筛选条件可以混合）
     * @param paramDTO
     * @return
     */
    @RpcMethod("按照单位、供应商、品牌、时间、订单状态（需要保证这些聚合维度都是互斥的，筛选条件可以混合）")
    RemoteResponse<List<OrderAggregationResultDTO>> aggAmountAndQuantityByEntities(StatisticsManagerParamDTO paramDTO);

    /**
     * 趋势分析，按照年、月、趋势类型（还有单位、线上下单的筛选条件）， 返回对应年份月份数据
     * @param paramDTO
     * @return
     */
    @RpcMethod("按照年、月、趋势类型（还有单位、线上下单的筛选条件）， 返回对应年份月份数据")
    RemoteResponse<Map<String, List<OrderDateHistogramResultDTO>>> aggAmountAndQuantityByTrendType(StatisticsManagerParamDTO paramDTO);

    /**
     * 根据条件返回 成功退货金额总和 的聚合结果
     * @param paramDTO
     * @return
     */
    @RpcMethod("根据条件返回 成功退货金额总和 的聚合结果")
    default RemoteResponse<List<GoodsReturnAggResDTO>> aggReturnAmountByEntities(StatisticsManagerParamDTO paramDTO) {
        return RemoteResponse.<List<GoodsReturnAggResDTO>>custom().setData(Collections.emptyList()).setSuccess();
    }

    /**
     * 按搜索条件，返回购买的商品数量信息
     * @param paramDTO
     * @return
     */
    @RpcMethod("按搜索条件，返回购买的商品数量信息")
    RemoteResponse<List<ValidPurchaseCountDTO>> aggPurchaseQuantity(StatisticsManagerParamDTO paramDTO);

    @RpcMethod("广告投放订单聚合，按照条件聚合下单买家数，下单数")
    RemoteResponse<List<AdvertisementOrderAggDTO>> aggAdvertisementOrder(AdvertisementOrderAggRequest request);

    @RpcMethod("按指定字段+指定时间区间返回这两个条件的聚合结果")
    RemoteResponse<List<OrderDateHistogramResultDTO>> aggAmountAndQuantityByEntitiesDateHistogram(StatisticsManagerParamDTO paramDTO);
}
