package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2024-01-16 10:47
 * @description:
 **/
public class OrderBatchesPrintDTO implements Serializable {

    private static final long serialVersionUID = 6777815746470670564L;

    @RpcModelProperty("关联的detailId")
    private Integer detailId;

    @RpcModelProperty("批号")
    private String batches;

    @RpcModelProperty("有效期")
    private String expiration;

    @RpcModelProperty("生产厂家")
    private String manufacturer;

    @RpcModelProperty("生产日期")
    private String productionDate;


    @RpcModelProperty("数量")
    private Integer total;

    public Integer getDetailId() {
        return detailId;
    }

    public OrderBatchesPrintDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public String getBatches() {
        return batches;
    }

    public OrderBatchesPrintDTO setBatches(String batches) {
        this.batches = batches;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public OrderBatchesPrintDTO setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public OrderBatchesPrintDTO setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public OrderBatchesPrintDTO setProductionDate(String productionDate) {
        this.productionDate = productionDate;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public OrderBatchesPrintDTO setTotal(Integer total) {
        this.total = total;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderBatchesPrintDTO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("batches='" + batches + "'")
                .add("expiration='" + expiration + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("total=" + total)
                .toString();
    }
}
