package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 订单备注DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/18 9:28
 **/
public class OrderRemarkDTO implements Serializable {

    private static final long serialVersionUID = 5077894723607795466L;
    /**
     * 采购单id
     */
    private Integer ftbuyappid;

    /**
     * 供应商id
     */
    private Integer fsuppid;

    /**
     * 备注
     */
    private String remark;

    private Date creationTime;

    private Date updateTime;

    public Integer getFtbuyappid() {
        return ftbuyappid;
    }

    public void setFtbuyappid(Integer ftbuyappid) {
        this.ftbuyappid = ftbuyappid;
    }

    public Integer getFsuppid() {
        return fsuppid;
    }

    public void setFsuppid(Integer fsuppid) {
        this.fsuppid = fsuppid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderRemarkDTO{");
        sb.append("ftbuyappid=").append(ftbuyappid);
        sb.append(", fsuppid=").append(fsuppid);
        sb.append(", remark='").append(remark).append('\'');
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }
}
