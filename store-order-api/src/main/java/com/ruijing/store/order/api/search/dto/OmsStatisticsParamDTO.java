package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: OMS统计入参
 * @author: zhuk
 * @create: 2019-09-19 09:43
 **/
public class OmsStatisticsParamDTO implements Serializable {

    private static final long serialVersionUID = -3907138561937778117L;

    /**
     * 不包含的组织列表
     */
    private List<String> noOrgIdList;

    /**
     * 起始时间 例：2019-09-19 00:00:00
     */
    private String startTime;

    /**
     * 结束时间 例：2019-09-19 23:59:59
     */
    private String endTime;

    /**
     * 订单状态list
     */
    private List<Integer> statusList;

    /**
     * 不包含的供应商
     */
    private List<String> noSuppIdList;

    public List<String> getNoOrgIdList() {
        return noOrgIdList;
    }

    public void setNoOrgIdList(List<String> noOrgIdList) {
        this.noOrgIdList = noOrgIdList;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public List<String> getNoSuppIdList() {
        return noSuppIdList;
    }

    public void setNoSuppIdList(List<String> noSuppIdList) {
        this.noSuppIdList = noSuppIdList;
    }

    @Override
    public String toString() {
        return "OmsStatisticsParamDTO{" +
                "noOrgIdList=" + noOrgIdList +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", statusList=" + statusList +
                ", noSuppIdList=" + noSuppIdList +
                '}';
    }
}
