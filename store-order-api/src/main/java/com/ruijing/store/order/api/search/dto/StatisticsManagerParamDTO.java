package com.ruijing.store.order.api.search.dto;

import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.enums.OrderNestedEnum;
import com.ruijing.store.order.api.search.enums.OrderAggregationSortFieldEnum;
import com.ruijing.store.order.api.search.enums.OrderMetricFieldEnum;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @description: 订单统计管理入参
 * @author: zhuk
 * @create: 2019-09-10 19:36
 **/
public class StatisticsManagerParamDTO implements Serializable {

    private static final long serialVersionUID = 6476136747564917598L;

    /**
     * 起始时间
     */
    private String startTime;

    /**
     * 截止时间
     */
    private String endTime;

    /**
     * 范围查询集合
     */
    private List<FieldRangeDTO> fieldRangeList;

    /**
     * 课题组名称
     */
    private String departmentName;

    /**
     * 课题组id
     */
    private List<Integer> departmentIds;

    /**
     * 学院id
     */
    private List<Integer> departmentParentId;

    /**
     * 订单状态列表
     */
    private List<Integer> statusList;

    /**
     * 流程种类 0:正常, 1:线下
     */
    private Integer species;

    /**
     * 订单来源列表
     * {@link com.ruijing.store.order.api.base.enums.OrderTypeEnum}
     */
    private List<Integer> orderTypeList;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 采购人id
     */
    private Integer buyerId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品货号
     */
    private String productCode;

    /**
     * 供应商id
     */
    private List<Integer> suppIdList;

    /**
     * 商品id数组
     */
    private List<Long> productIdList;

    /**
     * 排除的订单状态
     */
    private List<Integer> notStatusList;

    /**
     * 一级分类id
     */
    private Integer firstCategoryId;

    /**
     * 聚合排序时的前 多少名 默认50
     */
    private Integer topSize = 50;

    /**
     * 聚合字段
     */
    private OrderSearchFieldEnum aggField;

    /**
     * 排序条目
     */
    private OrderAggregationSortFieldEnum sortItem;

    /**
     * 聚合间隔时间
     */
    private IntervalDTO intervalDate;

    /**
     * 聚合排序规则
     */
    private SortOrderEnum sortOrderEnum;

    /**
     * 搜索关键字
     */
    private String searchKey;

    /**
     * orderMaster全文检索字段
     */
    private Set<String> fullTextMasterFields = new HashSet<>();

    /**
     * orderDetail全文检索字段
     */
    private Set<String> fullTextDetailFields = new HashSet<>();

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页条目数
     */
    private Integer pageSize;

    /**
     * 单位id列表
     */
    private List<Integer> orgIdList;

    /**
     * 去除的单位id列表
     */
    private List<Integer> excludeOrgIdList;

    /**
     * 去除的供应商id列表
     */
    private List<Integer> excludeSuppIdList;

    /**
     * 去除的用户数量
     */
    private List<Integer> excludeUserIdList;

    /**
     * 需要统计的金额字段（原价、成交价、退货金额）
     */
    private OrderMetricFieldEnum orderAmountField;

    /**
     * 需要统计的数量字段（商品数量，退货数量）
     */
    private OrderMetricFieldEnum orderQuantityField;

    /**
     * 聚合结果截取(对应bucket_sort.from)
     */
    private Integer aggResFrom;

    /**
     * 聚合结果截取(对应bucket_sort.size)
     */
    private Integer aggResSize;

    /**
     * 只返回匹配文档数量至少为 min_doc_count 的时间段，0-没有文档匹配的时间段也返回
     * 不传搜索里默认1
     */
    private Integer minDocCount;

    public Integer getMinDocCount() {
        return minDocCount;
    }

    public StatisticsManagerParamDTO setMinDocCount(Integer minDocCount) {
        this.minDocCount = minDocCount;
        return this;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<FieldRangeDTO> getFieldRangeList() {
        return fieldRangeList;
    }

    public void setFieldRangeList(List<FieldRangeDTO> fieldRangeList) {
        this.fieldRangeList = fieldRangeList;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<Integer> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Integer> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public List<Integer> getDepartmentParentId() {
        return departmentParentId;
    }

    public void setDepartmentParentId(List<Integer> departmentParentId) {
        this.departmentParentId = departmentParentId;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public List<Integer> getSuppIdList() {
        return suppIdList;
    }

    public void setSuppIdList(List<Integer> suppIdList) {
        this.suppIdList = suppIdList;
    }

    public List<Integer> getNotStatusList() {
        return notStatusList;
    }

    public void setNotStatusList(List<Integer> notStatusList) {
        this.notStatusList = notStatusList;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public Integer getTopSize() {
        return topSize;
    }

    public void setTopSize(Integer topSize) {
        this.topSize = topSize;
    }

    public OrderSearchFieldEnum getAggField() {
        return aggField;
    }

    public void setAggField(OrderSearchFieldEnum aggField) {
        this.aggField = aggField;
    }

    public OrderAggregationSortFieldEnum getSortItem() {
        return sortItem;
    }

    public void setSortItem(OrderAggregationSortFieldEnum sortItem) {
        this.sortItem = sortItem;
    }

    public IntervalDTO getIntervalDate() {
        return intervalDate;
    }

    public void setIntervalDate(IntervalDTO intervalDate) {
        this.intervalDate = intervalDate;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public Set<String> getFullTextMasterFields() {
        return fullTextMasterFields;
    }

    public void addFullTextMasterFields(String orderMasterField) {
        this.fullTextMasterFields.add(orderMasterField);
    }

    public Set<String> getFullTextDetailFields() {
        return fullTextDetailFields;
    }

    public void addFullTextDetailFields(String orderDetailField) {
        this.fullTextDetailFields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName() + "." + orderDetailField);
    }

    public List<Long> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Long> productIdList) {
        this.productIdList = productIdList;
    }

    public SortOrderEnum getSortOrderEnum() {
        return sortOrderEnum;
    }

    public void setSortOrderEnum(SortOrderEnum sortOrderEnum) {
        this.sortOrderEnum = sortOrderEnum;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<Integer> orgIdList) {
        this.orgIdList = orgIdList;
    }

    public List<Integer> getExcludeOrgIdList() {
        return excludeOrgIdList;
    }

    public void setExcludeOrgIdList(List<Integer> excludeOrgIdList) {
        this.excludeOrgIdList = excludeOrgIdList;
    }

    public List<Integer> getExcludeSuppIdList() {
        return excludeSuppIdList;
    }

    public void setExcludeSuppIdList(List<Integer> excludeSuppIdList) {
        this.excludeSuppIdList = excludeSuppIdList;
    }

    public List<Integer> getExcludeUserIdList() {
        return excludeUserIdList;
    }

    public StatisticsManagerParamDTO setExcludeUserIdList(List<Integer> excludeUserIdList) {
        this.excludeUserIdList = excludeUserIdList;
        return this;
    }

    public List<Integer> getOrderTypeList() {
        return orderTypeList;
    }

    public StatisticsManagerParamDTO setOrderTypeList(List<Integer> orderTypeList) {
        this.orderTypeList = orderTypeList;
        return this;
    }

    public StatisticsManagerParamDTO setFullTextMasterFields(Set<String> fullTextMasterFields) {
        this.fullTextMasterFields = fullTextMasterFields;
        return this;
    }

    public StatisticsManagerParamDTO setFullTextDetailFields(Set<String> fullTextDetailFields) {
        this.fullTextDetailFields = fullTextDetailFields;
        return this;
    }

    public OrderMetricFieldEnum getOrderAmountField() {
        return orderAmountField;
    }

    public void setOrderAmountField(OrderMetricFieldEnum orderAmountField) {
        this.orderAmountField = orderAmountField;
    }

    public OrderMetricFieldEnum getOrderQuantityField() {
        return orderQuantityField;
    }

    public void setOrderQuantityField(OrderMetricFieldEnum orderQuantityField) {
        this.orderQuantityField = orderQuantityField;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Integer getAggResFrom() {
        return aggResFrom;
    }

    public void setAggResFrom(Integer aggResFrom) {
        this.aggResFrom = aggResFrom;
    }

    public Integer getAggResSize() {
        return aggResSize;
    }

    public void setAggResSize(Integer aggResSize) {
        this.aggResSize = aggResSize;
    }

    @Override
    public String toString() {
        return "StatisticsManagerParamDTO{" +
                "startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", fieldRangeList=" + fieldRangeList +
                ", departmentName='" + departmentName + '\'' +
                ", departmentIds=" + departmentIds +
                ", departmentParentId=" + departmentParentId +
                ", statusList=" + statusList +
                ", species=" + species +
                ", orderTypeList=" + orderTypeList +
                ", orgId=" + orgId +
                ", buyerId=" + buyerId +
                ", brandName='" + brandName + '\'' +
                ", productCode='" + productCode + '\'' +
                ", suppIdList=" + suppIdList +
                ", productIdList=" + productIdList +
                ", notStatusList=" + notStatusList +
                ", firstCategoryId=" + firstCategoryId +
                ", topSize=" + topSize +
                ", aggField=" + aggField +
                ", sortItem=" + sortItem +
                ", intervalDate=" + intervalDate +
                ", sortOrderEnum=" + sortOrderEnum +
                ", searchKey='" + searchKey + '\'' +
                ", fullTextMasterFields=" + fullTextMasterFields +
                ", fullTextDetailFields=" + fullTextDetailFields +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", orgIdList=" + orgIdList +
                ", excludeOrgIdList=" + excludeOrgIdList +
                ", excludeSuppIdList=" + excludeSuppIdList +
                ", excludeUserIdList=" + excludeUserIdList +
                ", orderAmountField=" + orderAmountField +
                ", orderQuantityField=" + orderQuantityField +
                ", aggResFrom=" + aggResFrom +
                ", aggResSize=" + aggResSize +
                ", minDocCount=" + minDocCount +
                '}';
    }
}
