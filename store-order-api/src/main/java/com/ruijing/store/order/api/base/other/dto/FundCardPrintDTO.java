package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2024-01-18 16:35
 * @description:
 **/
public class FundCardPrintDTO implements Serializable {

    private static final long serialVersionUID = 3279375656380789383L;

    /**
     * 子经费卡
     */
    private List<FundCardPrintDTO> subCardList;

    /**
     * 经费卡号
     */
    private String code;

    /**
     * 预算金额
     */
    private BigDecimal budgetAmount;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    public List<FundCardPrintDTO> getSubCardList() {
        return subCardList;
    }

    public FundCardPrintDTO setSubCardList(List<FundCardPrintDTO> subCardList) {
        this.subCardList = subCardList;
        return this;
    }

    public String getCode() {
        return code;
    }

    public FundCardPrintDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public BigDecimal getBudgetAmount() {
        return budgetAmount;
    }

    public FundCardPrintDTO setBudgetAmount(BigDecimal budgetAmount) {
        this.budgetAmount = budgetAmount;
        return this;
    }

    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    public FundCardPrintDTO setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", FundCardPrintDTO.class.getSimpleName() + "[", "]")
                .add("subCardList=" + subCardList)
                .add("code='" + code + "'")
                .add("budgetAmount=" + budgetAmount)
                .add("freezeAmount=" + freezeAmount)
                .toString();
    }
}
