package com.ruijing.store.order.api.base.goodsreturn.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/4 13:07
 **/
public class GoodsReturnRequestDTO implements Serializable {

    private static final long serialVersionUID = -5060956186984856661L;

    /**
     * detailId 数组，长度不可超100
     */
    @RpcModelProperty("detailId 数组，长度不可超100")
    private List<Integer> detailIdList;

    /**
     * orderId 数组，长度不可超100
     */
    @RpcModelProperty("orderId 数组，长度不可超100")
    private List<Integer> orderIdList;

    /**
     * returnNo 数组，长度不可超100
     */
    @RpcModelProperty("returnNo 数组，长度不可超100")
    private List<String> returnNoList;

    /**
     * id 数组，长度不可超100
     */
    @RpcModelProperty("id 数组，长度不可超100")
    private List<Integer> idList;

    /**
     * 退货状态数组
     * {@link com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum}
     */
    @RpcModelProperty("退货状态 数组")
    private List<Integer> goodsReturnStatusList;

    /**
     * 退货单id
     */
    @RpcModelProperty("退货单id")
    private Integer returnId;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public List<Integer> getDetailIdList() {
        return detailIdList;
    }

    public void setDetailIdList(List<Integer> detailIdList) {
        this.detailIdList = detailIdList;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }


    public List<Integer> getGoodsReturnStatusList() {
        return goodsReturnStatusList;
    }

    public void setGoodsReturnStatusList(List<Integer> goodsReturnStatusList) {
        this.goodsReturnStatusList = goodsReturnStatusList;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }

    public List<String> getReturnNoList() {
        return returnNoList;
    }

    public void setReturnNoList(List<String> returnNoList) {
        this.returnNoList = returnNoList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnRequestDTO{");
        sb.append("detailIdList=").append(detailIdList);
        sb.append(", idList=").append(idList);
        sb.append(", orderIdList=").append(orderIdList);
        sb.append(", goodsReturnStatusEnumList=").append(goodsReturnStatusList);
        sb.append('}');
        return sb.toString();
    }
}
