package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @Date 2020/11/18 17:12
 * @Description
 **/
public enum OrderConfirmShowPrivEnum {

    CONFIRM_FOR_THE_RECORD_WAIT(1,"待备案"),
    LIST_TYPE_APPROVAL(1,"我的已审批"),
    SHOW_ALL_1(1,"pi"),
    SHOW_ALL_2(2,"采购人或经费卡被授权人");

    public final Integer value;

    public final String name;

    OrderConfirmShowPrivEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return name;
    }

    public static final OrderConfirmShowPrivEnum getByName(String name) {
        for (OrderConfirmShowPrivEnum orderConfirmShowPrivEnum : OrderConfirmShowPrivEnum.values()) {
            if (orderConfirmShowPrivEnum.name.equals(name)){
                return orderConfirmShowPrivEnum;
            }
        }
        return null;
    }

    public static final OrderConfirmShowPrivEnum getByValue(Integer value) {
        for (OrderConfirmShowPrivEnum orderConfirmShowPrivEnum : OrderConfirmShowPrivEnum.values()) {
            if (orderConfirmShowPrivEnum.value.equals(value)){
                return orderConfirmShowPrivEnum;
            }
        }
        return null;
    }
}
