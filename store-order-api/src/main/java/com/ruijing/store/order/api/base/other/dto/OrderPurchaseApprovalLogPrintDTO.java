package com.ruijing.store.order.api.base.other.dto;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/11/9 16:27
 **/
public class OrderPurchaseApprovalLogPrintDTO implements Serializable {

    private static final long serialVersionUID = -2608480912242204584L;

    /**
     * 审批时间
     */
    @RpcModelProperty("审批时间")
    private String date;

    /**
     * 审批人名称
     */
    @RpcModelProperty("审批人名称")
    private String approver;

    /**
     * 操作类型
     */
    @RpcModelProperty("操作类型")
    private String operate;

    /**
     * 操作内容
     */
    @RpcModelProperty("操作内容")
    private String operateComment;

    /**
     * 审批等级 默认0
     */
    @RpcModelProperty("审批等级 默认0")
    private Integer approveLevel;

    /**
     * 审批结果
     */
    @RpcModelProperty("审批结果")
    private String result;

    /**
     * 审批电子签名图片url
     */
    @RpcModelProperty("审批电子签名图片url")
    private String approvePhotoUrl;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public String getOperateComment() {
        return operateComment;
    }

    public void setOperateComment(String operateComment) {
        this.operateComment = operateComment;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getApprovePhotoUrl() {
        return approvePhotoUrl;
    }

    public void setApprovePhotoUrl(String approvePhotoUrl) {
        this.approvePhotoUrl = approvePhotoUrl;
    }
}
