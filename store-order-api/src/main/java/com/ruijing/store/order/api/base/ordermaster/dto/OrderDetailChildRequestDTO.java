package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * 拆单-孩子订单明细
 */
@RpcModel("拆单-孩子订单明细")
public class OrderDetailChildRequestDTO implements Serializable {

    private static final long serialVersionUID = 6990289895830260798L;

    @RpcModelProperty("订单明细id")
    private Integer detailId;

    @RpcModelProperty("拆分的数量")
    private Integer total;

    public Integer getDetailId() {
        return detailId;
    }

    public OrderDetailChildRequestDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public OrderDetailChildRequestDTO setTotal(Integer total) {
        this.total = total;
        return this;
    }
}
