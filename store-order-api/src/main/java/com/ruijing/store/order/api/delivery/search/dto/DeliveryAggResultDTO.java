package com.ruijing.store.order.api.delivery.search.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-03-31 15:22
 * @description:
 **/
public class DeliveryAggResultDTO implements Serializable {

    private static final long serialVersionUID = -3909519797177615890L;

    /**
     * 聚合的桶中聚合对象的key
     */
    private String aggResultKey;

    /**
     * 金额
     */
    private Double amount;

    /**
     * 数量
     */
    private Double quantity;

    public String getAggResultKey() {
        return aggResultKey;
    }

    public void setAggResultKey(String aggResultKey) {
        this.aggResultKey = aggResultKey;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DeliveryAggResultDTO.class.getSimpleName() + "[", "]")
                .add("aggResultKey='" + aggResultKey + "'")
                .add("amount=" + amount)
                .add("quantity=" + quantity)
                .toString();
    }
}
