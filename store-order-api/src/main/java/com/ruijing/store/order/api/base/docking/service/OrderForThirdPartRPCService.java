package com.ruijing.store.order.api.base.docking.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateRequestDTO;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateResponseDTO;

@RpcApi
public interface OrderForThirdPartRPCService {

    /**
     * 第三方对接单位更新我方订单信息接口
     * @param request   入参
     * @return          是否成功
     */
    @RpcMethod("批量更新订单数据，数组对象里的订单orderNo必填, 批量更新单次最多支持100单")
    RemoteResponse<ThirdPartUpdateResponseDTO<String>> thirdPartyUpdateOrder(ThirdPartUpdateRequestDTO request);
}
