package com.ruijing.store.order.api.general.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @program: store-order-service
 * @description: 搜索-订单对象
 * @author: zhuk
 * @create: 2019-06-01 10:12
 **/

public class OrderMasterSearchDTO implements Serializable {

    private static final long serialVersionUID = -5067822683091397077L;

    /**
     *  订单orderMaster id
     */
    private Integer id;


    /**
     * 订单号
     */
    private String forderno;

    /**
     * 采购部门id
     */
    private Integer fbuydepartmentid;

    /**
     * 采购部门名称
     */
    private String fbuydepartment;

    /**
     * 供应商名称
     */
    private String fsuppname;

    /**
     * 供应商id
     */
    private Integer fsuppid;

    /**
     * 订单总价格
     */
    private Double forderamounttotal;

    /**
     * order类型,0:采购的,1:竞价单
     */
    private Integer orderType;

    /**
     * 单位id
     */
    private Integer fuserid;

    /**
     * 流程种类 0:正常, 1:线下
     */
    private Integer species;

    /**
     *采购申请单id
     */
    private Integer ftbuyappid;

    /**
     *
     */
    private String fbuyapplicationno;

    /**
     * 项目编号
     */
    private String projectNumber;

    /**
     * t_docking_extra 表
     */
    private String extraInfo;

    /**
     * 采购联系人
     */
    private String fbuyercontactman;

    /**
     * 单位名称
     */
    private String fusername;


    /**
     *订单日期
     */
    private String forderdate;

    /**
     *送货地点
     */
    private String fbiderdeliveryplace;

    /**
     * 采购部门id
     */
    private Integer departmentParentId;

    /**
     * 采购部门名称
     */
    private String departmentParentName;


    /**
     * 采购人姓名
     */
    private String fbuyername;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 备案类型1易制毒2易制爆
     */
    private Integer confirmType;

    /**
     *  与tbuyapplication_master 关联关系
     */
    private String relateInfo;

    /**
     * 结算单ID
     */
    private Integer statementId;

    /**
     * 结算状态
     */
    private Integer statementStatus;

    /**
     * 采购人id
     */
    private Integer fbuyerid;

    /**
     * 是否已备案0未1已
     */
    private Integer confirm;

    /**
     * 订单确认日期
     */
    private Date fconfirmdate;

    /**
     * 发货日期
     */
    private Date fdeliverydate ;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *订单明细
     */
    private List<OrderDetailSearchDTO> orderDetail;

    /**
     * 订单日志
     */
    private List<OrderLogSearchDTO> log;

    /**
     * 订单经费卡
     */
    private List<FundCardSearchDTO> card;

    /**
     * 订单额外信息
     */
    private List<OrderExtraDTO> orderExtra;

    /**
     * 联系人电话
     */
    private String fbuyertelephone;

    /**
     * 收货时间
     */
    private Date flastreceivedate;

    /**
     * 验收人名称
     */
    private String flastreceiveman;

    /**
     * 竞价单Id
     */
    private String bidOrderId;

    /**
     * 订单实际金额
     */
    private Double actualAmount;

    /**
     * 订单撤销时间
     */
    private Date fcanceldate;

    /**
     * 经费状态
     */
    private Integer fundStatus;

    /**
     * 出入库状态
     * {@link com.ruijing.store.order.api.base.enums.InventoryStatusEnum}
     */
    private Integer InventoryStatus;

    /**
     * 运费
     */
    private Double carryFee;

    /**
     * 验收人id
     */
    private Integer flastreceivemanid;

    /**
     * 退货金额
     */
    private Double returnAmount;

    /**
     * 是否为旧订单
     */
    private Boolean oldFlag;

    /**
     * 代配送状态
     */
    private Integer deliveryStatus;

    /**
     * 分拣员
     */
    private String sortedUser;

    /**
     * 配送员
     */
    private String deliveryUser;

    private Date deliveredTime;

    /**
     * 配送类型
     */
    private Integer deliveryType;

    @RpcModelProperty("是否代结算--绿盾对接业务")
    private Boolean agentStatement;

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Date getDeliveredTime() {
        return deliveredTime;
    }

    public void setDeliveredTime(Date deliveredTime) {
        this.deliveredTime = deliveredTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getForderno() {
        return forderno;
    }

    public void setForderno(String forderno) {
        this.forderno = forderno;
    }

    public Integer getFbuydepartmentid() {
        return fbuydepartmentid;
    }

    public void setFbuydepartmentid(Integer fbuydepartmentid) {
        this.fbuydepartmentid = fbuydepartmentid;
    }

    public String getFbuydepartment() {
        return fbuydepartment;
    }

    public void setFbuydepartment(String fbuydepartment) {
        this.fbuydepartment = fbuydepartment;
    }

    public String getFsuppname() {
        return fsuppname;
    }

    public void setFsuppname(String fsuppname) {
        this.fsuppname = fsuppname;
    }

    public Integer getFsuppid() {
        return fsuppid;
    }

    public void setFsuppid(Integer fsuppid) {
        this.fsuppid = fsuppid;
    }

    public Double getForderamounttotal() {
        return forderamounttotal;
    }

    public void setForderamounttotal(Double forderamounttotal) {
        this.forderamounttotal = forderamounttotal;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getFuserid() {
        return fuserid;
    }

    public void setFuserid(Integer fuserid) {
        this.fuserid = fuserid;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public Integer getFtbuyappid() {
        return ftbuyappid;
    }

    public void setFtbuyappid(Integer ftbuyappid) {
        this.ftbuyappid = ftbuyappid;
    }

    public String getFbuyapplicationno() {
        return fbuyapplicationno;
    }

    public void setFbuyapplicationno(String fbuyapplicationno) {
        this.fbuyapplicationno = fbuyapplicationno;
    }

    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    public String getFbuyercontactman() {
        return fbuyercontactman;
    }

    public void setFbuyercontactman(String fbuyercontactman) {
        this.fbuyercontactman = fbuyercontactman;
    }

    public String getFusername() {
        return fusername;
    }

    public void setFusername(String fusername) {
        this.fusername = fusername;
    }

    public String getForderdate() {
        return forderdate;
    }

    public void setForderdate(String forderdate) {
        this.forderdate = forderdate;
    }

    public String getFbiderdeliveryplace() {
        return fbiderdeliveryplace;
    }

    public void setFbiderdeliveryplace(String fbiderdeliveryplace) {
        this.fbiderdeliveryplace = fbiderdeliveryplace;
    }

    public Integer getDepartmentParentId() {
        return departmentParentId;
    }

    public void setDepartmentParentId(Integer departmentParentId) {
        this.departmentParentId = departmentParentId;
    }

    public String getDepartmentParentName() {
        return departmentParentName;
    }

    public void setDepartmentParentName(String departmentParentName) {
        this.departmentParentName = departmentParentName;
    }

    public String getFbuyername() {
        return fbuyername;
    }

    public void setFbuyername(String fbuyername) {
        this.fbuyername = fbuyername;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getConfirmType() {
        return confirmType;
    }

    public void setConfirmType(Integer confirmType) {
        this.confirmType = confirmType;
    }

    public String getRelateInfo() {
        return relateInfo;
    }

    public void setRelateInfo(String relateInfo) {
        this.relateInfo = relateInfo;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    public Integer getStatementId() {
        return statementId;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public Integer getFbuyerid() {
        return fbuyerid;
    }

    public void setFbuyerid(Integer fbuyerid) {
        this.fbuyerid = fbuyerid;
    }

    public Integer getConfirm() {
        return confirm;
    }

    public void setConfirm(Integer confirm) {
        this.confirm = confirm;
    }

    public List<OrderDetailSearchDTO> getOrderDetail() {
        return orderDetail;
    }

    public void setOrderDetail(List<OrderDetailSearchDTO> orderDetail) {
        this.orderDetail = orderDetail;
    }

    public List<OrderLogSearchDTO> getLog() {
        return log;
    }

    public Date getFconfirmdate() {
        return fconfirmdate;
    }

    public void setFconfirmdate(Date fconfirmdate) {
        this.fconfirmdate = fconfirmdate;
    }

    public Date getFdeliverydate() {
        return fdeliverydate;
    }

    public void setFdeliverydate(Date fdeliverydate) {
        this.fdeliverydate = fdeliverydate;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setLog(List<OrderLogSearchDTO> log) {
        this.log = log;
    }

    public List<FundCardSearchDTO> getCard() {
        return card;
    }

    public void setCard(List<FundCardSearchDTO> card) {
        this.card = card;
    }

    public String getFbuyertelephone() {
        return fbuyertelephone;
    }

    public void setFbuyertelephone(String fbuyertelephone) {
        this.fbuyertelephone = fbuyertelephone;
    }

    public Date getFlastreceivedate() {
        return flastreceivedate;
    }

    public void setFlastreceivedate(Date flastreceivedate) {
        this.flastreceivedate = flastreceivedate;
    }

    public String getFlastreceiveman() {
        return flastreceiveman;
    }

    public void setFlastreceiveman(String flastreceiveman) {
        this.flastreceiveman = flastreceiveman;
    }

    public String getBidOrderId() {
        return bidOrderId;
    }

    public void setBidOrderId(String bidOrderId) {
        this.bidOrderId = bidOrderId;
    }

    public Double getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(Double actualAmount) {
        this.actualAmount = actualAmount;
    }

    public Date getFcanceldate() {
        return fcanceldate;
    }

    public void setFcanceldate(Date fcanceldate) {
        this.fcanceldate = fcanceldate;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public Integer getInventoryStatus() {
        return InventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        InventoryStatus = inventoryStatus;
    }

    public Double getCarryFee() {
        return carryFee;
    }

    public OrderMasterSearchDTO setCarryFee(Double carryFee) {
        this.carryFee = carryFee;
        return this;
    }

    public Integer getFlastreceivemanid() {
        return flastreceivemanid;
    }

    public void setFlastreceivemanid(Integer flastreceivemanid) {
        this.flastreceivemanid = flastreceivemanid;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public Boolean getOldFlag() {
        return oldFlag;
    }

    public OrderMasterSearchDTO setOldFlag(Boolean oldFlag) {
        this.oldFlag = oldFlag;
        return this;
    }

    public List<OrderExtraDTO> getOrderExtra() {
        return orderExtra;
    }

    public void setOrderExtra(List<OrderExtraDTO> orderExtra) {
        this.orderExtra = orderExtra;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getSortedUser() {
        return sortedUser;
    }

    public void setSortedUser(String sortedUser) {
        this.sortedUser = sortedUser;
    }

    public String getDeliveryUser() {
        return deliveryUser;
    }

    public void setDeliveryUser(String deliveryUser) {
        this.deliveryUser = deliveryUser;
    }

    public Boolean getAgentStatement() {
        return agentStatement;
    }

    public OrderMasterSearchDTO setAgentStatement(Boolean agentStatement) {
        this.agentStatement = agentStatement;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderMasterSearchDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("forderno='" + forderno + "'")
                .add("fbuydepartmentid=" + fbuydepartmentid)
                .add("fbuydepartment='" + fbuydepartment + "'")
                .add("fsuppname='" + fsuppname + "'")
                .add("fsuppid=" + fsuppid)
                .add("forderamounttotal=" + forderamounttotal)
                .add("orderType=" + orderType)
                .add("fuserid=" + fuserid)
                .add("species=" + species)
                .add("ftbuyappid=" + ftbuyappid)
                .add("fbuyapplicationno='" + fbuyapplicationno + "'")
                .add("projectNumber='" + projectNumber + "'")
                .add("extraInfo='" + extraInfo + "'")
                .add("fbuyercontactman='" + fbuyercontactman + "'")
                .add("fusername='" + fusername + "'")
                .add("forderdate='" + forderdate + "'")
                .add("fbiderdeliveryplace='" + fbiderdeliveryplace + "'")
                .add("departmentParentId=" + departmentParentId)
                .add("departmentParentName='" + departmentParentName + "'")
                .add("fbuyername='" + fbuyername + "'")
                .add("status=" + status)
                .add("confirmType=" + confirmType)
                .add("relateInfo='" + relateInfo + "'")
                .add("statementId=" + statementId)
                .add("statementStatus=" + statementStatus)
                .add("fbuyerid=" + fbuyerid)
                .add("confirm=" + confirm)
                .add("fconfirmdate=" + fconfirmdate)
                .add("fdeliverydate=" + fdeliverydate)
                .add("updateTime=" + updateTime)
                .add("orderDetail=" + orderDetail)
                .add("log=" + log)
                .add("card=" + card)
                .add("orderExtra=" + orderExtra)
                .add("fbuyertelephone='" + fbuyertelephone + "'")
                .add("flastreceivedate=" + flastreceivedate)
                .add("flastreceiveman='" + flastreceiveman + "'")
                .add("bidOrderId='" + bidOrderId + "'")
                .add("actualAmount=" + actualAmount)
                .add("fcanceldate=" + fcanceldate)
                .add("fundStatus=" + fundStatus)
                .add("InventoryStatus=" + InventoryStatus)
                .add("carryFee=" + carryFee)
                .add("flastreceivemanid=" + flastreceivemanid)
                .add("returnAmount=" + returnAmount)
                .add("oldFlag=" + oldFlag)
                .add("deliveryStatus=" + deliveryStatus)
                .add("sortedUser='" + sortedUser + "'")
                .add("deliveryUser='" + deliveryUser + "'")
                .add("deliveredTime=" + deliveredTime)
                .add("agentStatement=" + agentStatement)
                .toString();
    }
}
