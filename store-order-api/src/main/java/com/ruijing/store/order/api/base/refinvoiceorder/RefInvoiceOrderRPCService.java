package com.ruijing.store.order.api.base.refinvoiceorder;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.RefInvoiceOrderDTO;
import com.ruijing.store.order.api.base.refinvoiceorder.dto.RefInvoiceOrderRequestDTO;

import java.util.List;

/**
 * @description: 订单关联发票信息RPC 接口
 * @author: zhongyulei
 * @create: 2020/12/2 15:29
 **/
@RpcApi(value = "订单关联发票信息RPC 接口")
public interface RefInvoiceOrderRPCService {

    /**
     * 根据id查询 订单关联发票信息接口，idList必填，数组长度不可超过100
     * @param request
     * @return
     */
    @RpcMethod(value = "根据id查询 订单关联发票信息接口，idList必填，数组长度不可超过100")
    RemoteResponse<List<RefInvoiceOrderDTO>> findByIdList(RefInvoiceOrderRequestDTO request);

    /**
     * 根据订单id集合 查询订单关联发票信息接口，orderidList 必填，数组长度不可超过100
     * @param request
     * @return
     */
    @RpcMethod(value = "根据订单id集合 查询订单关联发票信息接口，orderidList 必填，数组长度不可超过100")
    RemoteResponse<List<RefInvoiceOrderDTO>> findByOrderIdList(RefInvoiceOrderRequestDTO request);

    /**
     * 批量插入订单关联信息，数组长度不可超过100
     * @param request
     * @return
     */
    @RpcMethod(value = "批量插入订单关联信息，数组长度不可超过100")
    RemoteResponse<Integer> insertList(List<RefInvoiceOrderDTO> request);

    /**
     * 根据id批量删除 订单关联发票信息接口，idList必填，数组长度不可超过100
     * @param request
     * @return
     */
    @RpcMethod(value = "根据id批量删除 订单关联发票信息接口，idList必填，数组长度不可超过100")
    RemoteResponse<Integer> deleteByIdList(RefInvoiceOrderRequestDTO request);

    /**
     * 根据发票id批量删除 订单关联发票信息接口，invoiceIdList必填，数组长度不可超过100
     * @param request
     * @return
     */
    @RpcMethod(value = "根据发票id批量删除 订单关联发票信息接口，invoiceIdList必填，数组长度不可超过100")
    RemoteResponse<Integer> deleteByInvoiceIdList(RefInvoiceOrderRequestDTO request);

}
