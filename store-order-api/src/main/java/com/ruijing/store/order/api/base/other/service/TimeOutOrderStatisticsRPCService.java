package com.ruijing.store.order.api.base.other.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;

@RpcApi(value = "提供超时订单RPC接口")
public interface TimeOutOrderStatisticsRPCService {

    /**
     * 更新超时订单统计数据
     * @param request   基础入参
     * @return          更新数量
     */
    @RpcMethod("更新超时订单统计数据，orgCode，orderIdList必填，orderIdList <= 100")
    RemoteResponse<Integer> updateOrderTimeOutStatistics(OrderBasicParamDTO request);
}
