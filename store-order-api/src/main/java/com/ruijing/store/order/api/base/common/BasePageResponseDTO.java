package com.ruijing.store.order.api.base.common;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 基础分页
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/5 16:12
 **/
public class BasePageResponseDTO<T> implements Serializable {

    private static final long serialVersionUID = 7185984448596165975L;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页记录数
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 数据体
     */
    private List<T> data;

    public BasePageResponseDTO( ) {
        super();
    }

    public BasePageResponseDTO(Integer pageNo, Integer pageSize, long total, List<T> data) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.total = total;
        this.data = data;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("BasePageResponseDTO{");
        sb.append("pageNo=").append(pageNo);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", data=").append(data);
        sb.append(", total=").append(total);
        sb.append('}');
        return sb.toString();
    }
}
