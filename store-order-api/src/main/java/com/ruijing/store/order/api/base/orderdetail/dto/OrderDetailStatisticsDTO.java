package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 商品销量 dto
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/1/12 17:18
 **/
public class OrderDetailStatisticsDTO implements Serializable {

    private static final long serialVersionUID = 5242478294448539918L;

    /**
     * 商品id
     */
    @RpcModelProperty("商品id")
    private Long productId;

    /**
     * 销量
     */
    @RpcModelProperty("销量")
    private BigDecimal saleTotal;


    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public BigDecimal getSaleTotal() {
        return saleTotal;
    }

    public void setSaleTotal(BigDecimal saleTotal) {
        this.saleTotal = saleTotal;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderDetailStatisticsDTO{");
        sb.append("productId=").append(productId);
        sb.append(", saleTotal=").append(saleTotal);
        sb.append('}');
        return sb.toString();
    }
}
