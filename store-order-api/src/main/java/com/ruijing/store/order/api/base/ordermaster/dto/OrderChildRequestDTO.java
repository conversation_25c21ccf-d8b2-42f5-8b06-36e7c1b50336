package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

@RpcModel("子订单")
public class OrderChildRequestDTO implements Serializable {

    private static final long serialVersionUID = -2345209542651356076L;

    @RpcModelProperty("订单明细, 数组")
    private List<OrderDetailChildRequestDTO> childrenDetailList;

    public List<OrderDetailChildRequestDTO> getChildrenDetailList() {
        return childrenDetailList;
    }

    public OrderChildRequestDTO setChildrenDetailList(List<OrderDetailChildRequestDTO> childrenDetailList) {
        this.childrenDetailList = childrenDetailList;
        return this;
    }
}
