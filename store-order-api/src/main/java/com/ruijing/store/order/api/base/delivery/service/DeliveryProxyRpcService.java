package com.ruijing.store.order.api.base.delivery.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.delivery.dto.DeliveryProxyCommonRequestDTO;
import com.ruijing.store.order.api.base.delivery.dto.DeliveryProxyChangeDTO;

/**
 * <AUTHOR>
 * @date 2023/1/29 11:01
 * @description
 */
@RpcApi(
        value = "代配送服务",
        description = "代配送相关服务，开启/关闭代配送、取消代配送等"
)
public interface DeliveryProxyRpcService {

    /**
     * 修改代配送类型来源
     * @param deliveryProxyChangeDTO 代配送修改数据
     * @return 是否成功
     */
    @RpcMethod("修改代配送类型来源")
    RemoteResponse<Boolean> changeProxySourceType(DeliveryProxyChangeDTO deliveryProxyChangeDTO);

    /**
     * 商家申请取消代配送
     * @param deliveryProxyCommonRequestDTO 代配送通用请求数据
     * @return 是否成功
     */
    @RpcMethod("商家申请取消代配送")
    RemoteResponse<Boolean> suppApplyCancelProxy(DeliveryProxyCommonRequestDTO deliveryProxyCommonRequestDTO);
}
