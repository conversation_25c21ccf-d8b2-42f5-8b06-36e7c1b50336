package com.ruijing.store.order.api.general.service;


import com.ruijing.fundamental.api.annotation.MethodDeprecated;
import com.ruijing.fundamental.api.annotation.OneWay;
import com.ruijing.fundamental.api.annotation.ServiceDeprecated;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.general.dto.*;


/**
 * orderSearch搜索接口
 * <AUTHOR>
 */

@ServiceDeprecated
public interface OrderSearchService {

    /**
     * 更新订单状态 无返回值
     * @param orderId 订单id
     * @param status    订单状态
     * @return
     */
    @OneWay
    void updateStatusSearchOneWay(Integer orderId, Integer status);

    /**
     * 更新订单状态 有返回值
     * @param orderId 订单id
     * @param status    订单状态
     * @return
     */
    RemoteResponse<String> updateStatusSearch(Integer orderId, Integer status);

    /**
     * 更新搜索订单索引 订单状态
     * @param updateOrderStatusSearchParamDTO 订单id，订单状态
     * @return RemoteResponse
     */
    @OneWay
    void updateOrderSearch(UpdateOrderSearchParamDTO updateOrderStatusSearchParamDTO);

    /**
     *  根据提供的字段查询相应的订单数据
     * @param searchFields 需要进行搜索的字段
     */
    RemoteResponse<OrderSearchResultDTO> searchDefaultResult(OrderSearchFieldsDTO searchFields);

    /**
     *
     * @param orderForStaementReqDTO
     * @param result
     * @return
     */
    @MethodDeprecated(value = "替换使用：com.ruijing.store.order.api.search.service.OrderSearchRpcService.searchOrderByStatementIdList",expireDate = "2020/12/31")
    RemoteResponse<OrderSearchResultDTO> searchOrderByStatementIds(OrderStatementReqDTO orderForStaementReqDTO, OrderSearchExtraResultDTO result);

    /**
     *  根据提供的字段查询相应的订单，有额外的字段返回
     * @param searchFields 需要进行搜索的字段
     * @param extraResults 需要返回的额外字段信息
     * @return RemoteResponse
     */
    @MethodDeprecated(value = "替换使用：com.ruijing.store.order.api.search.service.OrderSearchRpcService.commonSearch",expireDate = "2020/12/31")
    RemoteResponse<OrderSearchResultDTO> search(OrderSearchFieldsDTO searchFields, OrderSearchExtraResultDTO extraResults);
}
