package com.ruijing.store.order.api.cancelorder.dto;

import java.io.Serializable;

/**
 * @description: 华农取消订单请求参数
 * @author: zhuk
 * @create: 2019-07-10 16:03
 **/
public class HuaNongCancelOrderReqDTO implements Serializable {

    private static final long serialVersionUID = 3574591953332851058L;
    /**
     * 订单id
     */
    private Integer orderMasterId;

    public Integer getOrderMasterId() {
        return orderMasterId;
    }

    public void setOrderMasterId(Integer orderMasterId) {
        this.orderMasterId = orderMasterId;
    }

    @Override
    public String toString() {
        return "HuaNongCancelOrderReqDTO{" +
                "orderMasterId=" + orderMasterId +
                '}';
    }
}
