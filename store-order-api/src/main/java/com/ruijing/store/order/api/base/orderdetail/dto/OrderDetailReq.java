package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 订单详情 请求入参
 * @author: zhuk
 * @create: 2019-06-28 17:37
 **/
@RpcModel("订单详情-RPC请求入参")
public class OrderDetailReq implements Serializable {

    private static final long serialVersionUID = 2195313702979078924L;

    /**
     * 订单主表 id
     */
    private Integer orderMasterId;

    /**
     * 订单详情id
     */
    private Integer orderDetailId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 操作人id
     */
    private Integer userId;

    /**
     * 操作人名称
     */
    private String userName;

    /**
     * 订单主表 id List
     */
    private List<Integer> orderMasterIdList;

    /**
     * 订单明细id 数组
     */
    @RpcModelProperty("订单明细id 数组")
    private List<Integer> orderDetailIdList;

    /**
     * 条形码
     */
    @RpcModelProperty("条形码")
    private String barCode;

    @RpcModelProperty("需要排除的退货状态")
    private List<Integer> excludeReturnStatusList;

    @RpcModelProperty("是否一并返回绑定的气瓶")
    private Boolean returnBindGasBottle;

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public Integer getOrderMasterId() {
        return orderMasterId;
    }

    public void setOrderMasterId(Integer orderMasterId) {
        this.orderMasterId = orderMasterId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<Integer> getOrderMasterIdList() {
        return orderMasterIdList;
    }

    public void setOrderMasterIdList(List<Integer> orderMasterIdList) {
        this.orderMasterIdList = orderMasterIdList;
    }

    public List<Integer> getOrderDetailIdList() {
        return orderDetailIdList;
    }

    public void setOrderDetailIdList(List<Integer> orderDetailIdList) {
        this.orderDetailIdList = orderDetailIdList;
    }

    public String getBarCode() {
        return barCode;
    }

    public OrderDetailReq setBarCode(String barCode) {
        this.barCode = barCode;
        return this;
    }

    public List<Integer> getExcludeReturnStatusList() {
        return excludeReturnStatusList;
    }

    public OrderDetailReq setExcludeReturnStatusList(List<Integer> excludeReturnStatusList) {
        this.excludeReturnStatusList = excludeReturnStatusList;
        return this;
    }

    public Boolean getReturnBindGasBottle() {
        return returnBindGasBottle;
    }

    public OrderDetailReq setReturnBindGasBottle(Boolean returnBindGasBottle) {
        this.returnBindGasBottle = returnBindGasBottle;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailReq.class.getSimpleName() + "[", "]")
                .add("orderMasterId=" + orderMasterId)
                .add("orderDetailId=" + orderDetailId)
                .add("productName='" + productName + "'")
                .add("userId=" + userId)
                .add("userName='" + userName + "'")
                .add("orderMasterIdList=" + orderMasterIdList)
                .add("orderDetailIdList=" + orderDetailIdList)
                .add("barCode='" + barCode + "'")
                .add("excludeReturnStatusList=" + excludeReturnStatusList)
                .add("returnBindGasBottle=" + returnBindGasBottle)
                .toString();
    }
}
