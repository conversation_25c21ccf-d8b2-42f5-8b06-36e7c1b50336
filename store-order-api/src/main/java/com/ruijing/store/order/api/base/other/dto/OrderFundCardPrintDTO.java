package com.ruijing.store.order.api.base.other.dto;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 订单经费卡打印模型
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/16 18:57
 **/
public class OrderFundCardPrintDTO implements Serializable {

    private static final long serialVersionUID = 5985570963590401884L;

    /**
     * 经费卡号
     */
    @RpcModelProperty("经费卡号")
    private String cardNo;

    /**
     * 项目编号
     */
    @RpcModelProperty("项目编号")
    private String projectCode;

    /**
     * 项目名称
     */
    @RpcModelProperty("项目名称")
    private String projectName;

    @RpcModelProperty("二级经费卡科目名")
    private String subjectName;

    @RpcModelProperty("二级经费卡科目编码")
    private String subjectCode;

    /**
     * 经费负责人
     */
    @RpcModelProperty("经费负责人")
    private String fundManager;

    @RpcModelProperty("经费负责人电话")
    private String fundManagerPhone;

    @RpcModelProperty("经费类型")
    private String fundType;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public String getFundManager() {
        return fundManager;
    }

    public void setFundManager(String fundManager) {
        this.fundManager = fundManager;
    }

    public String getFundManagerPhone() {
        return fundManagerPhone;
    }

    public OrderFundCardPrintDTO setFundManagerPhone(String fundManagerPhone) {
        this.fundManagerPhone = fundManagerPhone;
        return this;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderFundCardPrintDTO.class.getSimpleName() + "[", "]")
                .add("cardNo='" + cardNo + "'")
                .add("projectCode='" + projectCode + "'")
                .add("projectName='" + projectName + "'")
                .add("subjectName='" + subjectName + "'")
                .add("subjectCode='" + subjectCode + "'")
                .add("fundManager='" + fundManager + "'")
                .add("fundManagerPhone='" + fundManagerPhone + "'")
                .add("fundType='" + fundType + "'")
                .toString();
    }
}
