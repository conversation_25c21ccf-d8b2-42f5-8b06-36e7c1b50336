package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @author: zhu<PERSON>
 * @date : 2019/12/9 3:56 下午
 * @description: 用户验收订单DTO
 */
@RpcModel("订单管理-确认验收请求体")
public class OrderReceiptParamDTO implements Serializable {

    private static final long serialVersionUID = 7941129425083358199L;

    /**
     * 订单Id
     */
    @RpcModelProperty("订单Id")
    private Integer orderId;

    @RpcModelProperty("验收图片地址链接 列表")
    private List<String> pictureUrlList;

    /**
     * 验收附件URL
     */
    @RpcModelProperty(value = "验收附件 列表")
    private List<AttachmentDTO> attachmentList;

    /**
     * 验收视频url
     */
    @RpcModelProperty(value = "验收视频url 列表")
    private List<AttachmentDTO> orderVideoAttachmentList;

    /**
     * 用户id
     */
    @RpcModelProperty("用户id")
    private Integer userId;

    /**
     * 用户全局唯一id
     */
    @RpcModelProperty("用户全局唯一id")
    private String userGuid;

    /**
     * 用户姓名
     */
    @RpcModelProperty("用户姓名")
    private String userName;

    /**
     * 用户所在部门列表
     */
    @RpcModelProperty("用户所在部门列表")
    @Deprecated
    private List<Integer> departmentIdList;

    /**
     * 用户所在组织Id
     */
    @RpcModelProperty("用户所在组织Id")
    private Integer orgId;

    /**
     * 用户所在组织编码
     */
    @RpcModelProperty("用户所在组织编码")
    private String orgCode;

    /**
     * 易爆品数组
     */
    @RpcModelProperty("易爆品数组")
    private List<OrderExplosivesGoodsDTO> explosivesGoodsList;

    /**
     * 存储仓库id
     */
    @RpcModelProperty("存储仓库id")
    private String storeHouseId;

    /**
     * 存储仓库名
     */
    @RpcModelProperty("存储仓库名")
    private String storeHouseName;

    /**
     * 合法用途盖章文件url
     */
    @RpcModelProperty("合法用途盖章文件url")
    List<String> legalUseUrlList;

    /**
     * 交易凭证url
     */
    @RpcModelProperty("交易凭证url")
    List<String> tradeUrlList;

    /**
     * 是否强制验收
     */
    @RpcModelProperty("是否强制验收")
    Boolean compulsoryAccept = false;

    /**
     * 验收评价id列表
     */
    @RpcModelProperty("验收评价id列表")
    List<Integer> acceptCommentTagList;

    /**
     * 订单验收明细入参
     */
    List<OrderAcceptDetailDTO> orderAcceptDetailList;

    /**
     * 第二验收人姓名（当前需要此字段的单位：中山人民医院）
     */
    String secondReceiverName;

    /**
     * 电子签名密码
     */
    @RpcModelProperty("电子签名密码(是否使用电子签名为true是否免密为false时必填)")
    private String password;

    /**
     * 订单快照信息
     */
    private OrderMasterDTO orderSnapshot;

    /**
     * 验收备注
     */
    private String acceptReason;

    /**
     * {@link com.ruijing.store.order.api.base.enums.InventoryStatusEnum}
     */
    @RpcModelProperty("入库状态, 先入库后收货")
    private Integer inventoryStatus;

    @RpcModelProperty("是否手动验收, 默认手动")
    private boolean isManual = true;

    @RpcModelProperty("出库科室")
    private String exitDept;

    @RpcModelProperty(value = "实验数据网盘链接", description = "江西中医附院定制")
    private String experimentDataUrl;

    @RpcModelProperty("验收图片链接-关联订单详情 列表")
    private List<AcceptPictureDTO> detailPictureDTOList;

    @RpcModelProperty(value = "验收附件-关联订单详情 列表")
    private List<AcceptAttachmentDTO> detailAttachmentDTOList;

    @RpcModelProperty("付款记录")
    private List<AttachmentDTO> paymentRecordList;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<String> getPictureUrlList() {
        return pictureUrlList;
    }

    public void setPictureUrlList(List<String> pictureUrlList) {
        this.pictureUrlList = pictureUrlList;
    }

    public void setAttachmentList(List<AttachmentDTO> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public List<AcceptPictureDTO> getDetailPictureDTOList() {
        return detailPictureDTOList;
    }

    public void setDetailPictureDTOList(List<AcceptPictureDTO> detailPictureDTOList) {
        this.detailPictureDTOList = detailPictureDTOList;
    }

    public List<AcceptAttachmentDTO> getDetailAttachmentDTOList() {
        return detailAttachmentDTOList;
    }

    public void setDetailAttachmentDTOList(List<AcceptAttachmentDTO> detailAttachmentDTOList) {
        this.detailAttachmentDTOList = detailAttachmentDTOList;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<Integer> getDepartmentIdList() {
        return departmentIdList;
    }

    public void setDepartmentIdList(List<Integer> departmentIdList) {
        this.departmentIdList = departmentIdList;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<OrderExplosivesGoodsDTO> getExplosivesGoodsList() {
        return explosivesGoodsList;
    }

    public void setExplosivesGoodsList(List<OrderExplosivesGoodsDTO> explosivesGoodsList) {
        this.explosivesGoodsList = explosivesGoodsList;
    }

    public String getStoreHouseId() {
        return storeHouseId;
    }

    public void setStoreHouseId(String storeHouseId) {
        this.storeHouseId = storeHouseId;
    }

    public String getStoreHouseName() {
        return storeHouseName;
    }

    public void setStoreHouseName(String storeHouseName) {
        this.storeHouseName = storeHouseName;
    }

    public List<String> getLegalUseUrlList() {
        return legalUseUrlList;
    }

    public void setLegalUseUrlList(List<String> legalUseUrlList) {
        this.legalUseUrlList = legalUseUrlList;
    }

    public List<String> getTradeUrlList() {
        return tradeUrlList;
    }

    public void setTradeUrlList(List<String> tradeUrlList) {
        this.tradeUrlList = tradeUrlList;
    }

    public Boolean getCompulsoryAccept() {
        return compulsoryAccept;
    }

    public void setCompulsoryAccept(Boolean compulsoryAccept) {
        this.compulsoryAccept = compulsoryAccept;
    }

    public List<Integer> getAcceptCommentTagList() {
        return acceptCommentTagList;
    }

    public OrderReceiptParamDTO setAcceptCommentTagList(List<Integer> acceptCommentTagList) {
        this.acceptCommentTagList = acceptCommentTagList;
        return this;
    }

    public List<OrderAcceptDetailDTO> getOrderAcceptDetailList() {
        return orderAcceptDetailList;
    }

    public void setOrderAcceptDetailList(List<OrderAcceptDetailDTO> orderAcceptDetailList) {
        this.orderAcceptDetailList = orderAcceptDetailList;
    }

    public String getSecondReceiverName() {
        return secondReceiverName;
    }

    public OrderReceiptParamDTO setSecondReceiverName(String secondReceiverName) {
        this.secondReceiverName = secondReceiverName;
        return this;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public OrderMasterDTO getOrderSnapshot() {
        return orderSnapshot;
    }

    public OrderReceiptParamDTO setOrderSnapshot(OrderMasterDTO orderSnapshot) {
        this.orderSnapshot = orderSnapshot;
        return this;
    }

    public String getAcceptReason() {
        return acceptReason;
    }

    public OrderReceiptParamDTO setAcceptReason(String acceptReason) {
        this.acceptReason = acceptReason;
        return this;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public OrderReceiptParamDTO setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
        return this;
    }

    public boolean isManual() {
        return isManual;
    }

    public void setManual(boolean manual) {
        isManual = manual;
    }

    public List<AttachmentDTO> getAttachmentList() {
        return attachmentList;
    }

    public String getExitDept() {
        return exitDept;
    }

    public OrderReceiptParamDTO setExitDept(String exitDept) {
        this.exitDept = exitDept;
        return this;
    }

    public List<AttachmentDTO> getOrderVideoAttachmentList() {
        return orderVideoAttachmentList;
    }

    public OrderReceiptParamDTO setOrderVideoAttachmentList(List<AttachmentDTO> orderVideoAttachmentList) {
        this.orderVideoAttachmentList = orderVideoAttachmentList;
        return this;
    }

    public List<AttachmentDTO> getPaymentRecordList() {
        return paymentRecordList;
    }

    public void setPaymentRecordList(List<AttachmentDTO> paymentRecordList) {
        this.paymentRecordList = paymentRecordList;
    }

    public String getExperimentDataUrl() {
        return experimentDataUrl;
    }

    public void setExperimentDataUrl(String experimentDataUrl) {
        this.experimentDataUrl = experimentDataUrl;
    }

    @Override
    public String toString() {
        return "OrderReceiptParamDTO{" +
                "orderId=" + orderId +
                ", pictureUrlList=" + pictureUrlList +
                ", attachmentList=" + attachmentList +
                ", orderVideoAttachmentList=" + orderVideoAttachmentList +
                ", userId=" + userId +
                ", userGuid='" + userGuid + '\'' +
                ", userName='" + userName + '\'' +
                ", departmentIdList=" + departmentIdList +
                ", orgId=" + orgId +
                ", orgCode='" + orgCode + '\'' +
                ", explosivesGoodsList=" + explosivesGoodsList +
                ", storeHouseId='" + storeHouseId + '\'' +
                ", storeHouseName='" + storeHouseName + '\'' +
                ", legalUseUrlList=" + legalUseUrlList +
                ", tradeUrlList=" + tradeUrlList +
                ", compulsoryAccept=" + compulsoryAccept +
                ", acceptCommentTagList=" + acceptCommentTagList +
                ", orderAcceptDetailList=" + orderAcceptDetailList +
                ", secondReceiverName='" + secondReceiverName + '\'' +
                ", password='" + password + '\'' +
                ", orderSnapshot=" + orderSnapshot +
                ", acceptReason='" + acceptReason + '\'' +
                ", inventoryStatus=" + inventoryStatus +
                ", isManual=" + isManual +
                ", exitDept='" + exitDept + '\'' +
                ", experimentDataUrl='" + experimentDataUrl + '\'' +
                ", detailPictureDTOList=" + detailPictureDTOList +
                ", detailAttachmentDTOList=" + detailAttachmentDTOList +
                ", paymentRecordList=" + paymentRecordList +
                '}';
    }
}
