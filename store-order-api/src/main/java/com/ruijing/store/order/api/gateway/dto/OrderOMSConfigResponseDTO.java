package com.ruijing.store.order.api.gateway.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description: 查询订单OMS配置的 出参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/11/23 17:30
 **/
public class OrderOMSConfigResponseDTO implements Serializable {

    private static final long serialVersionUID = -2425679939109167493L;

    /**
     * 是否使用库房
     */
    @RpcModelProperty("是否使用库房, 0否/1是")
    private String useWarehouseSystem;

    /**
     * 入库方式
     */
    @RpcModelProperty("入库方式, 0无需入库/1手动入库/2自动入库")
    private String receiptStoreConfig;

    public String getUseWarehouseSystem() {
        return useWarehouseSystem;
    }

    public void setUseWarehouseSystem(String useWarehouseSystem) {
        this.useWarehouseSystem = useWarehouseSystem;
    }

    public String getReceiptStoreConfig() {
        return receiptStoreConfig;
    }

    public void setReceiptStoreConfig(String receiptStoreConfig) {
        this.receiptStoreConfig = receiptStoreConfig;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOMSConfigResponseDTO{");
        sb.append("useWarehouseSystem='").append(useWarehouseSystem).append('\'');
        sb.append(", receiptStoreConfig='").append(receiptStoreConfig).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
