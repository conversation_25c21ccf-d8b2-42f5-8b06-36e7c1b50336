// ==UserScript==
// @name         钉钉多维表格自动筛选-黄有望(简化版)
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动在钉钉多维表格中筛选任务人员为"黄有望"
// <AUTHOR>
// @match        https://alidocs.dingtalk.com/i/nodes/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 钉钉多维表格自动筛选脚本已加载 - 筛选用户: 黄有望');
    
    // 等待页面完全加载
    function waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }
    
    // 等待iframe加载
    function waitForIframe() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 20;
            
            const checkIframe = () => {
                attempts++;
                const iframe = document.querySelector('#wiki-notable-iframe');
                
                if (iframe && iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                    console.log('✅ iframe已加载');
                    resolve(iframe.contentDocument);
                    return;
                }
                
                if (attempts >= maxAttempts) {
                    reject(new Error('iframe加载超时'));
                    return;
                }
                
                setTimeout(checkIframe, 500);
            };
            
            checkIframe();
        });
    }
    
    // 模拟点击
    function simulateClick(element) {
        if (!element) return false;
        
        // 尝试多种点击方式
        try {
            element.click();
            return true;
        } catch (e) {
            console.log('普通点击失败，尝试事件模拟');
        }
        
        try {
            const event = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
            return true;
        } catch (e) {
            console.log('事件模拟失败');
            return false;
        }
    }
    
    // 在iframe中查找包含特定文本的元素
    function findElementByText(iframeDoc, text) {
        // 方法1: 使用XPath
        try {
            const xpath = `//*[contains(text(), "${text}")]`;
            const result = iframeDoc.evaluate(
                xpath,
                iframeDoc,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            );
            if (result.singleNodeValue) {
                return result.singleNodeValue;
            }
        } catch (e) {
            console.log('XPath查找失败:', e);
        }
        
        // 方法2: 遍历所有元素
        try {
            const allElements = iframeDoc.querySelectorAll('*');
            for (let element of allElements) {
                if (element.textContent && element.textContent.includes(text)) {
                    return element;
                }
            }
        } catch (e) {
            console.log('遍历查找失败:', e);
        }
        
        return null;
    }
    
    // 主要的筛选函数
    async function performFilter() {
        try {
            console.log('🔄 开始执行筛选...');
            
            // 等待页面加载
            await waitForPageLoad();
            console.log('✅ 页面已加载');
            
            // 等待一段时间确保所有内容都加载完成
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 等待iframe加载
            const iframeDoc = await waitForIframe();
            console.log('✅ iframe已准备就绪');
            
            // 再等待一段时间确保iframe内容完全加载
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 步骤1: 查找"添加条件"按钮
            console.log('🔍 查找"添加条件"按钮...');
            let addConditionBtn = findElementByText(iframeDoc, '添加条件');
            
            if (!addConditionBtn) {
                // 尝试查找其他可能的文本
                const possibleTexts = ['筛选', '过滤', '条件', 'Filter', '添加'];
                for (let text of possibleTexts) {
                    addConditionBtn = findElementByText(iframeDoc, text);
                    if (addConditionBtn) {
                        console.log(`✅ 找到按钮: ${text}`);
                        break;
                    }
                }
            }
            
            if (!addConditionBtn) {
                throw new Error('未找到"添加条件"按钮');
            }
            
            console.log('✅ 找到"添加条件"按钮，准备点击');
            simulateClick(addConditionBtn);
            
            // 等待下拉菜单出现
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 步骤2: 查找"任务人员"选项
            console.log('🔍 查找"任务人员"选项...');
            const taskPersonOption = findElementByText(iframeDoc, '任务人员');
            
            if (!taskPersonOption) {
                throw new Error('未找到"任务人员"选项');
            }
            
            console.log('✅ 找到"任务人员"选项，准备点击');
            simulateClick(taskPersonOption);
            
            // 等待条件设置界面出现
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 步骤3: 查找值选择下拉框
            console.log('🔍 查找值选择下拉框...');
            let selectDropdown = findElementByText(iframeDoc, '请选择');
            
            if (!selectDropdown) {
                // 尝试查找其他可能的选择器
                const possibleSelectors = [
                    'select',
                    '[role="combobox"]',
                    '.ant-select',
                    '[class*="select"]',
                    '[class*="dropdown"]'
                ];
                
                for (let selector of possibleSelectors) {
                    const elements = iframeDoc.querySelectorAll(selector);
                    if (elements.length > 0) {
                        selectDropdown = elements[elements.length - 1]; // 取最后一个
                        console.log(`✅ 找到选择器: ${selector}`);
                        break;
                    }
                }
            }
            
            if (!selectDropdown) {
                throw new Error('未找到值选择下拉框');
            }
            
            console.log('✅ 找到值选择下拉框，准备点击');
            simulateClick(selectDropdown);
            
            // 等待选项出现
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 步骤4: 查找"黄有望"选项
            console.log('🔍 查找"黄有望"选项...');
            const userOption = findElementByText(iframeDoc, '黄有望');
            
            if (!userOption) {
                throw new Error('未找到"黄有望"选项');
            }
            
            console.log('✅ 找到"黄有望"选项，准备点击');
            simulateClick(userOption);
            
            // 等待筛选生效
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            console.log('🎉 筛选完成！');
            return true;
            
        } catch (error) {
            console.error('❌ 筛选失败:', error.message);
            return false;
        }
    }
    
    // 检查是否是目标页面
    function isTargetPage() {
        const url = window.location.href;
        return url.includes('alidocs.dingtalk.com/i/nodes/');
    }
    
    // 初始化
    async function init() {
        if (!isTargetPage()) {
            console.log('❌ 不是目标页面，跳过执行');
            return;
        }
        
        console.log('🎯 检测到钉钉多维表格页面，准备执行筛选');
        
        // 延迟执行，确保页面完全加载
        setTimeout(async () => {
            const success = await performFilter();
            if (success) {
                console.log('✅ 自动筛选成功完成');
            } else {
                console.log('❌ 自动筛选失败');
            }
        }, 3000);
    }
    
    // 启动脚本
    init();
    
})();
