package com.ruijing.store.goodsreturn.service.impl;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnApplyResponseVO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.*;
import com.ruijing.store.order.rpc.client.OrderUniqueBarCodeRPCClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class BarCodeGoodsReturnServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private BarCodeGoodsReturnServiceImpl barCodeGoodsReturnService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Mock
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Test
    public void applyOrderReturn() {
        OrderMasterDO order = new OrderMasterDO();
        order.setForderno("test");
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(order);

        OrderDetailDO d = new OrderDetailDO();
        d.setId(1);
        d.setFgoodname("test");
        d.setProductSn(1L);
        d.setFbidprice(BigDecimal.ONE);

        Mockito.when(orderDetailMapper.findByIdIn(Mockito.any())).thenReturn(Arrays.asList(d));
        GoodsReturnApplyResponseVO vo = new GoodsReturnApplyResponseVO();
        vo.setReturnNo("test");
        Mockito.when(buyerGoodsReturnService.applyGoodsReturn(Mockito.any(), Mockito.any())).thenReturn(vo);
        Mockito.when(orderUniqueBarCodeRPCClient.modifyOrderBarCodeBatches(Mockito.any())).thenReturn(true);

        GoodsReturnBarCodeRequest request = new GoodsReturnBarCodeRequest();

        GoodsReturnBarCodeDetailRequest g = new GoodsReturnBarCodeDetailRequest();
        g.setOrderDetailId(1);
        g.setPrice(BigDecimal.ONE);
        g.setOrderNo("test");
        request.setOrderUniqueBarCodeList(Arrays.asList(g));

        barCodeGoodsReturnService.applyOrderReturnForBarCode(new RjSessionInfo(), request);
    }

    @Test
    public void applyOrderReturnByBarCodeAssemble() {
        OrderMasterDO order = new OrderMasterDO();
        order.setForderno("test");
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.anyList())).thenReturn(Collections.singletonList(order));

        OrderDetailDO d = new OrderDetailDO();
        d.setId(1);
        d.setFgoodname("test");
        d.setProductSn(1L);
        d.setFbidprice(BigDecimal.ONE);
        Mockito.when(orderDetailMapper.findByIdIn(Mockito.any())).thenReturn(Arrays.asList(d));
        Mockito.when(buyerGoodsReturnService.applyGoodsReturn(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(orderUniqueBarCodeRPCClient.modifyOrderBarCodeBatches(Mockito.any())).thenReturn(true);

        GoodsReturnBarCodeRequest request = new GoodsReturnBarCodeRequest();
        GoodsReturnBarCodeDetailRequest g = new GoodsReturnBarCodeDetailRequest();
        g.setOrderDetailId(1);
        g.setPrice(BigDecimal.ONE);
        g.setOrderNo("test");
        g.setReason("test");

        request.setOrderUniqueBarCodeList(Arrays.asList(g));
        // List<GoodsReturnBarCodeDetailRequest> response = barCodeGoodsReturnService.applyOrderReturnByBarCodeAssemble(new RjSessionInfo(), request);
        Assert.assertTrue(response.size() > 0);
    }

    @Test
    public void applyOrderReturnByBarCodeAssemble_Failure() {
        // 模拟订单不存在的情况
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.anyList())).thenReturn(Collections.emptyList());

        // 构造请求参数
        BarCodeGoodsReturnRequest request = new BarCodeGoodsReturnRequest();
        List<OrderBarCodeReturnRequest> orderList = new ArrayList<>();
        
        // 构造第一个订单的退货请求
        OrderBarCodeReturnRequest order1 = new OrderBarCodeReturnRequest();
        order1.setOrderNo("DC202502271790701");
        order1.setReturnReason("质量问题");
        order1.setRemark("商品有破损");
        
        List<GoodsReturnDetailRequest> goodsList1 = new ArrayList<>();
        GoodsReturnDetailRequest goods1 = new GoodsReturnDetailRequest();
        goods1.setBarCode("12345678");
        goods1.setOrderDetailId(1);
        goods1.setReturnReason("商品破损");
        goods1.setRemark("包装破损");
        goodsList1.add(goods1);
        order1.setOrderUniqueBarCodeList(goodsList1);
        orderList.add(order1);
        
        // 构造第二个订单的退货请求
        OrderBarCodeReturnRequest order2 = new OrderBarCodeReturnRequest();
        order2.setOrderNo("DC202502191114201");
        order2.setReturnReason("商品错发");
        order2.setRemark("收到的商品与订单不符");
        
        List<GoodsReturnDetailRequest> goodsList2 = new ArrayList<>();
        GoodsReturnDetailRequest goods2 = new GoodsReturnDetailRequest();
        goods2.setBarCode("87654321");
        goods2.setOrderDetailId(2);
        goods2.setReturnReason("商品型号不对");
        goods2.setRemark("型号不符");
        goodsList2.add(goods2);
        order2.setOrderUniqueBarCodeList(goodsList2);
        orderList.add(order2);
        
        request.setGoodsReturnOrderList(orderList);

        // 执行测试
        // List<OrderBarCodeReturnRequest> failureList = barCodeGoodsReturnService.applyOrderReturnByBarCodeAssemble(new RjSessionInfo(), request);
        //
        // // 验证结果
        // Assert.assertNotNull("失败列表不应为空", failureList);
        // Assert.assertEquals("应该有两个失败的订单", 2, failureList.size());
        // Assert.assertEquals("第一个失败订单号不匹配", "test_order_1", failureList.get(0).getOrderNo());
        // Assert.assertEquals("第二个失败订单号不匹配", "test_order_2", failureList.get(1).getOrderNo());
    }

    @Test
    public void cancelOrderReturnForBarCode() {
        OrderUniqueBarCodeDTO item = new OrderUniqueBarCodeDTO();
        item.setUniBarCode(1L);
        Mockito.when(orderUniqueBarCodeRPCClient.findByReturnNo(Mockito.any())).thenReturn(Arrays.asList(item));
        Mockito.when(orderUniqueBarCodeRPCClient.modifyOrderBarCodeBatches(Mockito.any())).thenReturn(true);

        GoodsReturn request = new GoodsReturn();
        request.setOrgId(94);
        boolean b = barCodeGoodsReturnService.updateOrderReturnForBarCode(request, 1);
        Assert.assertTrue(b);

    }
}