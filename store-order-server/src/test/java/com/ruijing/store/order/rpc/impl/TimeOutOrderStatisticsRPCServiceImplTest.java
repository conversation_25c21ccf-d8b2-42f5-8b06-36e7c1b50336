package com.ruijing.store.order.rpc.impl;

import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.util.TimeUtil;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;


public class TimeOutOrderStatisticsRPCServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private TimeOutOrderStatisticsRPCServiceImpl timeOutOrderStatisticsRPCService;

    @Mock
    private TimeoutStatisticsService timeoutStatisticsService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private SysConfigClient sysConfigClient;

    @Test
    public void updateOrderTimeOutStatistics() {
        OrderMasterDO o = new OrderMasterDO();
        o.setFlastreceivedate(TimeUtil.getSettingDate(-40L));
        o.setFbuydepartmentid(1);
        o.setFuserid(1);

        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyList())).thenReturn(Arrays.asList(o));
        Mockito.when(sysConfigClient.getConfigByOrgCodeAndConfigCode(Mockito.anyString(), Mockito.anyString())).thenReturn("20");
        Mockito.doNothing().when(timeoutStatisticsService).executeTimeOutStatisticsDecrease(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any());
        Mockito.when(timeoutStatisticsService.findByDepartmentIdAndType(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new TimeoutStatisticsDTO());
        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrgCode("test");
        request.setOrderIdList(Arrays.asList(1));
        timeOutOrderStatisticsRPCService.updateOrderTimeOutStatistics(request);
    }
}