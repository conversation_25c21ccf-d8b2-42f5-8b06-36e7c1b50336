package com.ruijing.store.order.business.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.SyncStatementStatusDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.ResearchStatementClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import org.apache.ibatis.annotations.Param;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.util.Collection;

public class OrderStatementServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderStatementServiceImpl orderStatementService;

    @org.mockito.Mock
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private WaitingStatementService waitingStatementService;

    @org.mockito.Mock
    private ResearchStatementClient researchStatementClient;


    public static class Mock {
        @MockMethod(targetClass = OrderMasterMapper.class)
        int batchUpdateStatementStatus(@Param("idCollection") Collection<SyncStatementStatusDTO> syncStatementStatusDTOS) {
            return 1;
        }
    }

    @Test
    public void batchUpdateStatementStatus() {
        orderStatementService.batchUpdateStatementStatus(New.list(new SyncStatementStatusDTO()));
    }
    
}