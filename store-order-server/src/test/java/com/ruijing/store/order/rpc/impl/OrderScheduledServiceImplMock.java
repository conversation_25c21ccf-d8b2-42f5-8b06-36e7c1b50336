package com.ruijing.store.order.rpc.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderStatusReqDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.baseconfig.dto.OrganizationConfigDTO;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: zhong<PERSON>lei
 * @create: 2021/5/24 16:37
 **/
public class OrderScheduledServiceImplMock {

    @MockMethod(targetClass = OrderScheduledServiceImpl.class)
    private List<OrganizationConfigDTO> getConfigByType(TimeOutConfigType configType, List<OrganizationConfigDTO> timeOutConfigs, Set<Integer> ids) {
        return Arrays.asList(new OrganizationConfigDTO());
    }

    @MockMethod(targetClass = OrderScheduledServiceImpl.class)
    private List<OrderMasterSearchDTO> getGuangGongWaitingForConfirmOrderList() {
        OrderMasterSearchDTO orderMasterSearchDTO = new OrderMasterSearchDTO();
        orderMasterSearchDTO.setStatus(0);
        return Arrays.asList(orderMasterSearchDTO);
    }
}
