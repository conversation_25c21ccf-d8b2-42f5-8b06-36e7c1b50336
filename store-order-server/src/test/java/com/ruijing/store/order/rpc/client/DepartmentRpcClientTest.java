package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.user.api.dto.department.DepartmentThirdPartyDTO;
import com.ruijing.store.user.api.response.PageRemoteResponse;
import com.ruijing.store.user.api.service.DepartmentThirdPartyRpcService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class DepartmentRpcClientTest extends MockBaseTestCase {

    @InjectMocks
    private DepartmentRpcClient departmentRpcClient;

    @Mock
    private DepartmentThirdPartyRpcService departmentThirdPartyRpcService;

    @Test
    public void findByUserIdAndOrgIdAndDepName() {
        PageRemoteResponse<List<DepartmentThirdPartyDTO>> pageRemoteResponse
                = new PageRemoteResponse();
        pageRemoteResponse.setData(New.list(new DepartmentThirdPartyDTO()));
        pageRemoteResponse.setCode(200);
        Mockito.when(departmentThirdPartyRpcService
                .findByUserIdAndOrgIdAndDeptName(Mockito.anyString(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(pageRemoteResponse);
        departmentRpcClient.findByUserIdAndOrgIdAndDepName("userId","orgId","departmentName");
    }

}