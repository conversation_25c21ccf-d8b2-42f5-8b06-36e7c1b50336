package com.ruijing.store.order.cache;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.fundamental.cache.api.CacheKey;
import com.ruijing.fundamental.cache.redis.client.RedisCacheClient;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Test;
import org.mockito.InjectMocks;

/**
 * @auther: <PERSON><PERSON> Tse
 * @Date: 2021/3/17 11:33
 * @Description:
 */
public class CacheClientTest extends MockBaseTestCase {

    @InjectMocks
    private CacheClient cacheClient;

    public static class Mock {
        @MockMethod(targetClass = RedisCacheClient.class)
        public Boolean exists(CacheKey cacheKey) {
            return true;
        }
    }

    @Test
    public void exists() {
        cacheClient.exists("我是一个key！");
    }

}