package com.ruijing.store.order.business.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.model.MockScope;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.tpi.tpiclient.message.req.order.HNOrderBySelfBuyingReq;
import com.reagent.tpi.tpiclient.message.resp.BaseResp;
import com.reagent.tpi.tpiclient.message.resp.order.OrderResp;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderAcceptDetailDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.mapper.OrderContractMapper;
import com.ruijing.store.order.base.minor.mapper.OrderRemarkMapper;
import com.ruijing.store.order.base.minor.mapper.ProductDescriptionSnapshotMapper;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.base.minor.model.OrderContract;
import com.ruijing.store.order.base.minor.model.OrderRemark;
import com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.service.BizBaseService;
import com.ruijing.store.order.service.ResearchBaseService;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.department.DepartmentThirdPartyDTO;
import com.ruijing.store.utils.MyMockUtils;
import org.apache.ibatis.annotations.Param;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

public class OrderAcceptServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderAcceptServiceImpl orderAcceptServiceImpl;

    @org.mockito.Mock
    private GoodsReturnMapper goodsReturnMapper;

    @org.mockito.Mock
    private CategoryServiceClient categoryServiceClient;

    @org.mockito.Mock
    private WmsRuleRpcServiceClient wmsRuleRpcServiceClient;

    public static class Mock {
        @MockMethod(targetClass = OrderMasterMapper.class, scope = MockScope.ASSOCIATED)
        public OrderMasterDO selectByPrimaryKey(Integer id) {
            OrderMasterDO masterDO = new OrderMasterDO();
            masterDO.setFuserid(1);
            masterDO.setId(1);
            masterDO.setStatus(5);
            masterDO.setFbuydepartmentid(1);
            masterDO.setSpecies(Byte.valueOf("1"));
            masterDO.setFuserid(94);
            return masterDO;
        }

        @MockMethod(targetClass = OrderAcceptServiceImpl.class, scope = MockScope.ASSOCIATED)
        private Map<String, String> getReceiptConfigMap(String orgCode) {
            Map<String,String> hashMap = new HashMap<>();
            hashMap.put(ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, "1");
            return hashMap;
        }

        @MockMethod(targetClass = OrderAcceptServiceImpl.class, scope = MockScope.ASSOCIATED)
        private List<OrderDetailDO> checkedAcceptCondition(Integer orderId, OrderMasterDO orderMasterDO, Integer orgId,Integer userId, List<Integer> departmentIdList, List<String> picUrlList, Map<String, String> receiptConfigMap, Boolean isManual){
            return New.list(new OrderDetailDO());
        }

        @MockMethod(targetClass = BizBaseService.class, scope = MockScope.ASSOCIATED)
        public void saveAcceptElectronicSign(OrderReceiptParamDTO orderReceiptParamDTO, OrderMasterDO orderMasterDO){}

        @MockMethod(targetClass = OrderAcceptServiceImpl.class)
        public ReceiptOrderResponseDO acceptCore(Integer orgId, Integer operatorId, String operatorName, OrderMasterDO orderMasterDO,
                                                 Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList, String urls, String reason){
            return new ReceiptOrderResponseDO();
        }

        @MockMethod(targetClass = OrderAcceptServiceImpl.class, scope = MockScope.ASSOCIATED)
        private void saveOrderPic(List<String> picUrlList, OrderMasterDO orderMasterDO){}

        @MockMethod(targetClass = OrderAcceptServiceImpl.class, scope = MockScope.ASSOCIATED)
        private void saveOrderCommentStrategy(Integer orgId, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, List<Integer> acceptCommentTagList){}

        @MockMethod(targetClass = OrderAcceptServiceImpl.class, scope = MockScope.ASSOCIATED)
        private void sendReceiptEmailStrategy(String orgCode, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList){}

        @MockMethod(targetClass = OrderAcceptServiceImpl.class, scope = MockScope.ASSOCIATED)
        private void pushReceiptInfoStrategy(OrderReceiptParamDTO orderReceiptParamDTO, Integer orgId, Integer userId, OrderMasterDO orderMasterDO){}

        @MockMethod(targetClass = OrderManageService.class, scope = MockScope.ASSOCIATED)
        public void saveOrderExtraInfo(Integer orgId, OrderReceiptParamDTO orderReceiptParamDTO, Integer orderId, String orderNo){}

        @MockMethod(targetClass = OrderAcceptServiceImpl.class, scope = MockScope.ASSOCIATED)
        private void pushReceiptInfoStrategy(OrderReceiptParamDTO orderReceiptParamDTO, Integer orgId, Integer userId, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList){

        }

        @MockMethod(targetClass = UserClient.class, scope = MockScope.ASSOCIATED)
        public Boolean getUserReportStatus(Integer orgId, Integer deptId, Integer userId) {
            return true;
        }

        @MockMethod(targetClass = OrderSearchBoostService.class, scope = MockScope.ASSOCIATED)
        public List<OrderMasterSearchDTO>  searchOrderById(Integer orgId) {
            return Collections.emptyList();
        }

        @MockMethod(targetClass = ProductDescriptionSnapshotMapper.class)
        public List<ProductDescriptionSnapshotDO> findProductDescByIds(String businessId, Integer businessType, Long productId){
            ProductDescriptionSnapshotDO productDescriptionSnapshotDO = new ProductDescriptionSnapshotDO();
            return New.list(productDescriptionSnapshotDO);
        }

        @MockMethod(targetClass = DangerousTagDOMapper.class)
        public List<DangerousTagDO> selectByBusinessIdInAndBusinessType(Collection<String> businessIdCollection, Integer businessType){
            DangerousTagDO dangerousTagDO = new DangerousTagDO();
            dangerousTagDO.setDangerousType(1);
            return New.list(dangerousTagDO);
        }

        @MockMethod(targetClass = OrderContractMapper.class)
        public List<OrderContract> findByOrderId(Integer orderId){
            OrderContract orderContract = new OrderContract();
            orderContract.setContractLocation("test");
            return New.list(orderContract);
        }

        @MockMethod(targetClass = DepartmentRpcClient.class)
        public List<DepartmentThirdPartyDTO> findByUserIdAndOrgIdAndDepName(String orgCode, String jobNum, String departmentName){
            DepartmentThirdPartyDTO departmentThirdPartyDTO = new DepartmentThirdPartyDTO();
            departmentThirdPartyDTO.setDepartmentId(1);
            return New.list(departmentThirdPartyDTO);
        }

        @MockMethod(targetClass = OrderRemarkMapper.class)
        public List<OrderRemark> selectByFtbuyappidIn(Collection<Integer> ftbuyappidCollection){
            OrderRemark orderRemark = new OrderRemark();
            orderRemark.setRemark("test");
            return New.list(orderRemark);
        }

        @MockMethod(targetClass = HuaNongServiceClient.class)
        public BaseResp<OrderResp> saveOrder(HNOrderBySelfBuyingReq req, String forderno){
            OrderResp orderResp = new OrderResp();
            BaseResp baseResp = new BaseResp();
            baseResp.setData(orderResp);
            return baseResp;
        }

        @MockMethod(targetClass = DockingExtraMapper.class)
        public int insertSelective(DockingExtra record){
            return 1;
        }

        @MockMethod(targetClass = OrderDetailMapper.class)
        List<OrderDetailDO> findByFmasterid(@Param("fmasterid")Integer fmasterid){
            OrderDetailDO orderDetailDO = new OrderDetailDO();
            orderDetailDO.setFbidprice(new BigDecimal(11));
            orderDetailDO.setFquantity(new BigDecimal(1));
            return New.list(orderDetailDO);
        }

        @MockMethod(targetClass = OrderAcceptServiceImpl.class)
        private HNOrderBySelfBuyingReq.Data.item formatItem(OrderMasterDO orderMasterDO, OrderDetailDO detail){
            return new HNOrderBySelfBuyingReq.Data.item();
        }
        @MockMethod(targetClass = DepartmentRpcClient.class, scope = MockScope.ASSOCIATED)
        public DepartmentDTO getDepartmentParentInfo(Integer orgId) {
            return new DepartmentDTO();
        }

        @MockMethod(targetClass = BizWareHouseClient.class, scope = MockScope.ASSOCIATED)
        public Boolean autoInbound(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, UserBaseInfoDTO userInfo, String picURL, Integer parentDepartmentId) {
            return true;
        }

        @MockMethod(targetClass = UserClient.class, scope = MockScope.ASSOCIATED)
        public UserBaseInfoDTO getUserInfo(Integer userId, Integer orgId) {
            return new UserBaseInfoDTO();
        }

        @MockMethod(targetClass = UserClient.class, scope = MockScope.ASSOCIATED)
        public Boolean findUserHasAccess(@NotNull Integer orgId, @NotNull Integer userId, Integer deptId, @NotNull String accessCode){
            return true;
        }

        @MockMethod(targetClass = OrderUniqueBarCodeRPCClient.class, scope = MockScope.ASSOCIATED)
        public List<OrderUniqueBarCodeDTO> findByDetailIdList(List<Integer> detailIdList) {
            OrderUniqueBarCodeDTO item = new OrderUniqueBarCodeDTO();
            item.setOrderDetailId(1);
            return Collections.singletonList(item);
        }

        @MockMethod(targetClass = OrderUniqueBarCodeRPCClient.class, scope = MockScope.ASSOCIATED)
        public boolean modifyOrderBarCodeBatches(List<OrderUniqueBarCodeDTO> request) {
            return true;
        }

        @MockMethod(targetClass = ResearchBaseService.class, scope = MockScope.ASSOCIATED)
        public Boolean isPlatformFound(OrderMasterDO orderMasterDO) {
            return false;
        }

        @MockMethod(targetClass = OrderEmailHandler.class, scope = MockScope.ASSOCIATED)
        public void noticeWXApprover(String operatorName, OrderMasterDO orderMasterDO) {

        }

        @MockMethod(targetClass = TimeoutStatisticsService.class, scope = MockScope.ASSOCIATED)
        public void executeTimeOutStatisticsDecrease(Integer count, Integer orgId, String orgCode, Integer departmentId, TimeOutBusinessType timeOutType) {

        }

        @MockMethod(targetClass = OrderMasterMapper.class, scope = MockScope.ASSOCIATED)
        int updateOrderById(UpdateOrderParamDTO updateOrderParamDTO) {
            return 1;
        }

        @MockMethod(targetClass = OrderApprovalLogMapper.class, scope = MockScope.ASSOCIATED)
        int insertSelective(OrderApprovalLog record) {
            return 1;
        }
    }

    @Test
    public void userAcceptOrder() {
        MyMockUtils.setThreadLocalField(orderAcceptServiceImpl, "standingBookOrgIdList", Arrays.asList(1));
        OrderReceiptParamDTO orderReceiptParamDTO = new OrderReceiptParamDTO();
        orderReceiptParamDTO.setOrgId(1);
        orderReceiptParamDTO.setUserId(1);
        orderAcceptServiceImpl.userAcceptOrder(orderReceiptParamDTO);

        orderReceiptParamDTO.setOrgId(94);
        OrderAcceptDetailDTO detailDTO = new OrderAcceptDetailDTO();
        detailDTO.setDetailId(1);
        detailDTO.setExterior(0);
        orderReceiptParamDTO.setOrderAcceptDetailList(Collections.singletonList(detailDTO));

        orderAcceptServiceImpl.userAcceptOrder(orderReceiptParamDTO);
    }

    @Test
    public void sendOrderInfo() throws Exception {
        Class<OrderAcceptServiceImpl> orderAcceptServiceImplClass = (Class<OrderAcceptServiceImpl>) orderAcceptServiceImpl.getClass();
        Method sendOrderInfo = orderAcceptServiceImplClass.getDeclaredMethod("sendOrderInfo",  OrderMasterDO.class);
        sendOrderInfo.setAccessible(true);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setForderdate(new Date());
        sendOrderInfo.invoke(orderAcceptServiceImpl, orderMasterDO);
    }

    @Test
    public void formatItem() throws Exception {
        Class<OrderAcceptServiceImpl> orderAcceptServiceImplClass = (Class<OrderAcceptServiceImpl>) orderAcceptServiceImpl.getClass();
        Method sendOrderInfo = orderAcceptServiceImplClass.getDeclaredMethod("formatItem",  OrderMasterDO.class, OrderDetailDO.class);
        sendOrderInfo.setAccessible(true);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        // 采购单
        orderMasterDO.setOrderType(OrderTypeEnum.PURCHASE_ORDER.getCode());
        orderMasterDO.setForderdate(new Date());
        orderMasterDO.setFtbuyappid(1);
        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setFquantity(new BigDecimal(1));
        orderDetailDO.setFbidprice(new BigDecimal(1));
        orderDetailDO.setId(1);
        orderDetailDO.setCategoryid(1);
        sendOrderInfo.invoke(orderAcceptServiceImpl, orderMasterDO, orderDetailDO);

        // 其他
        // 路径不为空
        orderMasterDO.setOrderType(OrderTypeEnum.BID_ORDER.getCode());
        CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setPath("1");
        categoryDTO.setId(1l);
        Mockito.when(categoryServiceClient.queryListByCache(Arrays.asList(1l))).thenReturn(New.list(categoryDTO));
        sendOrderInfo.invoke(orderAcceptServiceImpl, orderMasterDO, orderDetailDO);

        // 路径为空
        categoryDTO.setPath("51");
        sendOrderInfo.invoke(orderAcceptServiceImpl, orderMasterDO, orderDetailDO);

        categoryDTO.setPath("518");
        sendOrderInfo.invoke(orderAcceptServiceImpl, orderMasterDO, orderDetailDO);

        categoryDTO.setPath("");
        sendOrderInfo.invoke(orderAcceptServiceImpl, orderMasterDO, orderDetailDO);
    }


}
