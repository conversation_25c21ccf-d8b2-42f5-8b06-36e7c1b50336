package com.ruijing.store.order.rpc.callback;

import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.financial.docking.dto.order.OrderCallbackResult;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.BusinessDockingRPCClient;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import com.ruijing.store.order.rpc.client.PurchaseApprovalLogClient;
import org.apache.kafka.common.errors.IllegalGenerationException;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class PushOrderCallbackServiceImplTest extends MockBaseTestCase {

    @Mock
    private OrderOtherLogClient orderOtherLogClient;

    @Mock
    private DockingExtraMapper dockingExtraMapper;

    @Mock
    private DockingExtraService dockingExtraService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private BusinessDockingRPCClient businessDockingRPCClient;

    @Mock
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Mock
    private ApplicationBaseClient applicationBaseClient;

    @InjectMocks
    private PushOrderCallbackServiceImpl pushOrderCallbackService;

    @Test
    public void handlePushOrderResult() {
        Mockito.when(dockingExtraMapper.countByInfo(Mockito.anyString())).thenReturn(0L);
        Mockito.when(dockingExtraMapper.insertSelective(Mockito.any())).thenReturn(0);
        Mockito.when(dockingExtraMapper.updateByInfo(Mockito.any())).thenReturn(0);
        Mockito.when(dockingExtraService.saveOrUpdateDockingExtra(Mockito.any())).thenReturn(1);
        Mockito.when(businessDockingRPCClient.saveBusinessOrders(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.when(businessDockingRPCClient.saveBusinessOrders(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any());

        Mockito.when(purchaseApprovalLogClient.addApprovalLog(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        Mockito.when(applicationBaseClient.saveApplicationMaster(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setOrderType(1);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(orderMasterDO);
        Mockito.doThrow(new IllegalGenerationException("error")).when(orderOtherLogClient).createOrderDockingLog("test", "test", "test", "test", "test", "test");

        CallbackRequest<OrderCallbackResult> request = new CallbackRequest<>();
        OrderCallbackResult r = new OrderCallbackResult();
        r.setOrderNo("test");
        request.setData(r);
        request.setCode(500);
        request.setMsg("批量提交订单出现异常，原因：请求网络异常,状态码为：502，原始请求报文：--BORXKmIY7wE4U4HEq_yuFk4tJioZ9QU\\r\\nContent-Disposition: form-data; name=\\\"data\\\"\\r\\nContent-Type: application/x-www-form-urlencoded; charset=UTF-8\\r\\nContent-Transfer-Encoding: 8bit\\r\\n\\r\\n{\\\"orderNo\\\":\\\"DC202103113222801\\\",\\\"orderTime\\\":1615394971000,\\\"username\\\":\\\"2111806143\\\",\\\"mobile\\\":\\\"15350285739\\\",\\\"amount\\\":27.0000,\\\"supplier\\\":\\\"萨恩化学技术(上海)有限公司\\\",\\\"isBid\\\":false,\\\"");
        request.setOrgCode(DockingConstant.GUANG_ZHOU_YI_KE_DA_XUE);
        RemoteResponse<Boolean> response = pushOrderCallbackService.handlePushOrderResult(request);
        Assert.assertTrue(!response.getData());

        request.setCode(200);
        response = pushOrderCallbackService.handlePushOrderResult(request);
        Assert.assertTrue(response.getData());
    }
}
