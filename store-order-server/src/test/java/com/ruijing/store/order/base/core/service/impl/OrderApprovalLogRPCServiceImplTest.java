package com.ruijing.store.order.base.core.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;

public class OrderApprovalLogRPCServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderApprovalLogRPCServiceImpl orderApprovalLogRPCService;

    @Mock
    private OrderApprovalLogService orderApprovalLogService;

    @Test
    public void insertOrderApproval() {
        RemoteResponse<Boolean> response = RemoteResponse.<Boolean>custom().setSuccess().setData(true);
        Mockito.when(orderApprovalLogService.insertOrderApprovalLog(Mockito.any())).thenReturn(response);
        OrderApprovalLogDTO request = new OrderApprovalLogDTO();
        RemoteResponse<Boolean> response1 = orderApprovalLogRPCService.insertOrderApproval(request);
        Assert.assertTrue("error", response1.isSuccess());
        RemoteResponse<Boolean> response2 = orderApprovalLogRPCService.insertOrderApproval(null);
        Assert.assertTrue("error", !response2.isSuccess());

    }

    @Test
    public void insertOrderApprovalLogList() {
        Mockito.doNothing().when(orderApprovalLogService).insertOrderApprovalLogList(Mockito.anyList());
        List<OrderApprovalLogDTO> request = new ArrayList<>();
        request.add(new OrderApprovalLogDTO());
        orderApprovalLogRPCService.insertOrderApprovalLogList(request);
        orderApprovalLogRPCService.insertOrderApprovalLogList(null);
    }

}
