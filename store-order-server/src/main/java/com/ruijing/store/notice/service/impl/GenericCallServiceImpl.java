package com.ruijing.store.notice.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.fundamental.remoting.msharp.service.GenericException;
import com.ruijing.fundamental.remoting.msharp.service.GenericService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.notice.enums.NoticeEventEnum;
import com.ruijing.store.notice.service.GenericCallService;
import com.ruijing.store.notice.util.GetAppKeyToNoticeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2022/5/20 9:27
 * @description
 */
@Service
@ServiceLog(description = "泛化调用服务", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
public class GenericCallServiceImpl implements GenericCallService {

    private static final String CLASS_NAME = GenericCallServiceImpl.class.getName();

    private final Logger logger = LoggerFactory.getLogger(CLASS_NAME);

    @Resource
    private GenericService genericService;

    @Override
    public <T, R> RemoteResponse<R> callMethod(String appKey, T param, String classNameToCall, String methodNameToCall, int timeout, String orderNo) {
        final String methodName = "callMethod";
        Transaction catTransaction = Cat.newTransaction(this.getClass().getSimpleName(), "callMethod", true);
        logger.info("进入{}.{} 方法,泛化调用appKey={}的{}.{}方法, 入参: {}", CLASS_NAME, methodName, appKey,
                classNameToCall, methodNameToCall, JsonUtils.toJsonIgnoreNull(param));
        try {
            // 设置超时时间
            RpcContext.getCallContext().setTimeout(timeout);
            // 泛化调用
            RemoteResponse<R> response = genericService.$invoke(appKey, classNameToCall, methodNameToCall,
                    new String[]{param.getClass().getTypeName()}, new Object[]{param});
            logger.info("结束{}.{} 方法,结束泛化调用appKey={}的{}.{}方法,出参: {}", CLASS_NAME, methodName, appKey,
                    classNameToCall, methodNameToCall, JsonUtils.toJsonIgnoreNull(param));

            catTransaction.setSuccessStatus();
            return response;
        } catch (GenericException e) {
            logger.error("泛化调用appKey={}的{}.{}方法操作失败: {}: {}", appKey,
                    classNameToCall, methodNameToCall, "callMethod异常", e);
            catTransaction.setStatus(e);
            catTransaction.addData(CatUtils.buildStackInfo("订单事件通知接口出错", e));
            catTransaction.addData(JsonUtils.toJson(param), e);
            throw e;
        } finally {
            catTransaction.complete();
        }
    }

    /**
     * 泛化调用
     *
     * @param appKeyList       外部服务的appKey列表
     * @param param            调用时的入参
     * @param classNameToCall  要进行泛化调用的类名
     * @param methodNameToCall 要进行泛化调用的方法名
     * @param timeout          超时时间
     * @param orderNo          发起调用的关联订单号
     * @return 远程返回的参数
     */
    @Override
    public <T> RemoteResponse<Boolean> callMethodToApps(List<String> appKeyList, T param, String classNameToCall, String methodNameToCall, int timeout, String orderNo) throws ExecutionException, InterruptedException {
        if (CollectionUtils.isEmpty(appKeyList)) {
            return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
        }
        CompletableFuture[] taskArr = new CompletableFuture[appKeyList.size()];
        for (int i = 0; i < appKeyList.size(); i++) {
            String appKey = appKeyList.get(i);
            CompletableFuture<RemoteResponse<Boolean>> task = AsyncExecutor.listenableCallAsync(
                    () -> this.<T, Boolean>callMethod(appKey, param, classNameToCall, methodNameToCall, timeout, orderNo)
            ).completable();
            taskArr[i] = task;
        }
        // 并行执行任务
        CompletableFuture.allOf(taskArr).join();
        boolean isSuccess = true;
        StringBuilder errMsgBuilder = new StringBuilder();
        for (CompletableFuture task : taskArr) {
            RemoteResponse<Boolean> result = (RemoteResponse<Boolean>) task.get();
            isSuccess = result.isSuccess() && result.getData() && isSuccess;
            // 返回非成功，则获取失败原因，拼接
            if (!(result.isSuccess() && result.getData())) {
                errMsgBuilder.append(result.getMsg());
            }
        }
        String errorMsg = errMsgBuilder.length() == 0 ? null : errMsgBuilder.toString();
        if(!isSuccess){
            return RemoteResponse.<Boolean>custom().setData(false).setFailure(errorMsg);
        }
        return RemoteResponse.<Boolean>custom().setSuccess();
    }

    /**
     * 泛化调用
     *
     * @param noticeEventEnum  通知事件类型
     * @param orgCode          单位代码
     * @param param            调用时的入参
     * @param timeout          超时时间
     * @param orderNo          发起调用的关联订单号
     * @return 远程返回的参数
     */
    @Override
    public <T> RemoteResponse<Boolean> callMethodToApps(NoticeEventEnum noticeEventEnum, String orgCode, T param, int timeout, String orderNo) throws ExecutionException, InterruptedException {
        // 根据通知事件类型获取appKey
        List<String> appKeyList = GetAppKeyToNoticeUtils.getAppKeyByOrgCodeAndEventType(orgCode, noticeEventEnum);
        return this.callMethodToApps(appKeyList, param, noticeEventEnum.getClassNameToCall(), noticeEventEnum.getMethodNameToCall(), timeout, orderNo);
    }
}
