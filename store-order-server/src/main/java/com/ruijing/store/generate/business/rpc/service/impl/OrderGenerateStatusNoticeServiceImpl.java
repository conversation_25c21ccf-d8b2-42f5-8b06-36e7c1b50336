package com.ruijing.store.generate.business.rpc.service.impl;

import com.reagent.order.api.outer.buyer.OrderGenerateStatusNoticeService;
import com.reagent.order.dto.outer.buyer.OuterBuyerCommonProcessDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.generate.business.service.OrderGenerateEventService;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.rpc.client.DockingConfigServiceClient;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：Created in 2022-06-06 14:24
 */
@MSharpService
public class OrderGenerateStatusNoticeServiceImpl implements OrderGenerateStatusNoticeService {

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderGenerateEventService orderGenerateEventService;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private DockingConfigServiceClient dockingConfigServiceClient;

    @Override
    @ServiceLog(description = "管理平台审批通过",operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> approveSuccess(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        // 查询订单信息
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        if(orderMasterDO == null){
            // 兼容预订单，无订单则不处理，有订单则无视配置做处理（因为已经限定了订单状态了）
            return RemoteResponse.success();
        }

        // 校验状态
        Integer currentStatus = orderMasterDO.getStatus();
        // 被关掉的订单做一下校验，不允许审批通过(该类订单已经操作过审批不通过了）
        BusinessErrUtil.isTrue(!OrderStatusEnum.Close.getValue().equals(currentStatus),
                ExecptionMessageEnum.UPDATE_ORDER_STATUS_FAILED, Objects.requireNonNull(OrderStatusEnum.get(currentStatus)).getName());

        // 校验状态,不是这两个状态的就是审批已经通过了 不做任何处理
        if(!OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(currentStatus)
                && !OrderStatusEnum.DeckingFail.getValue().equals(currentStatus)){
            // 非待管理平台审核/推送失败，不处理
            return RemoteResponse.success(true);
        }
        
        // 审批通过逻辑
        orderGenerateEventService.approveSuccess(orderMasterDO, outerBuyerCommonProcessDTO.getReason());

        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "管理平台审批驳回",operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> approveFail(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        // 查询订单信息
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        if(orderMasterDO == null){
            // 兼容预订单，无订单则不处理，有订单则无视配置做处理（因为已经限定了订单状态了）
            return RemoteResponse.success();
        }

        // 校验状态，非推送成功待审批/推送失败/订单关闭这三个状态之一，不允许执行驳回
        Integer currentStatus = orderMasterDO.getStatus();
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(currentStatus)
                || OrderStatusEnum.DeckingFail.getValue().equals(currentStatus)
                || OrderStatusEnum.Close.getValue().equals(currentStatus), ExecptionMessageEnum.UPDATE_ORDER_STATUS_FAILED, Objects.requireNonNull(OrderStatusEnum.get(currentStatus)).getName());
        if(!OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(currentStatus)
                && !OrderStatusEnum.DeckingFail.getValue().equals(currentStatus)){
            // 非待管理平台审核/推送失败，不处理
            return RemoteResponse.success(true);
        }
        // 审批失败逻辑
        orderGenerateEventService.approveFail(orderMasterDO, outerBuyerCommonProcessDTO.getReason());

        return RemoteResponse.success();
    }
}
