package com.ruijing.store.goodsreturn.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnDetailBaseRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnPageRequestDTO;
import com.ruijing.store.goodsreturn.vo.*;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum;
import com.ruijing.store.order.api.base.goodsreturn.dto.GoodsReturnCancelRequstDTO;

import java.util.List;

/**
 * 采购人中心业务相关的退货接口
 */
public interface BuyerGoodsReturnService {

    /**
     * 分页获取退货单信息
     * @param request   查询请求
     * @return          分页结果
     */
    BasePageResponseDTO<GoodsReturnPageVO> getPageGoodsReturn(GoodsReturnPageRequestDTO request, RjSessionInfo rjSessionInfo);

    /**
     * 订单申请退货
     * @param request   退货请求
     * @return          是否成功
     */
    GoodsReturnApplyResponseVO applyGoodsReturn(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo);

    /**
     * 退货单数量统计
     * @return          统计结果
     */
    GoodsReturnStatisticsVO getGoodsReturnStatistics(RjSessionInfo rjSessionInfo);

    /**
     * 获取退货单详情
     * @param request   退货单id
     * @return          退货单详情
     */
    GoodsReturnInfoVO getGoodsReturnInfo(GoodsReturnBaseRequestDTO request);

    /**
     * 取消/撤销退货, 支持批量取消
     * @param request   入参
     * @return          是否成功
     */
    boolean cancelGoodsReturn(GoodsReturnBaseRequestDTO request, RjSessionInfo rjSessionInfo);

    /**
     * 取消/撤销退货, 支持批量取消
     * @param goodsReturnCancelRequstDTO   入参
     * @return          是否成功
     */
    boolean forceCancelGoodsReturn(GoodsReturnCancelRequstDTO goodsReturnCancelRequstDTO);

    /**
     * 取消/撤销退货, 支持批量取消,由thunder发起
     * @param request   入参
     * @return          是否成功
     */
    boolean cancelGoodsReturnByThunder(GoodsReturnBaseRequestDTO request);


    /**
     * 取消退货单，使用系统的名义取消
     * @param orderIdList 订单id
     * @param goodsReturnOperationTypeEnum 退货操作枚举
     * @param reason 取消原因
     * @return 是否成功
     */
    boolean systemCancelGoodsReturn(List<Integer> orderIdList, GoodsReturnOperationTypeEnum goodsReturnOperationTypeEnum, String reason);

    /**
     * 更新退货单
     * @param request   入参
     * @return          是否成功
     */
    boolean updateGoodsReturn(GoodsReturnBaseRequestDTO request, RjSessionInfo rjSessionInfo);

    /**
     * 查询退货单操作日志
     * @param request   入参
     * @return          操作日志
     */
    List<GoodsReturnLogVO> getGoodsReturnLog(GoodsReturnBaseRequestDTO request);

    /**
     * 根据明细id查询退货明细的退货数量
     * @param request
     * @return
     */
    int getReturnCountByDetailId(GoodsReturnDetailBaseRequestDTO request);

    /**
     * 获取申请退货页面数据
     *
     * @return 申请退货页面数据
     */
    ApplyGoodsReturnOrderVO getApplyGoodsReturnData(String orderNo, Integer orgId);


    /**
     * 查询选中的退货商品是否在入库中
     */
    Boolean checkGoodsReturnEntry(GoodsReturnApplyRequestDTO request);

    /**
     *  发起退货前撤销入库
     */
    void entryWithDraw(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo);

    /**
     * 编辑退货单
     * @param request   退货请求
     * @param rjSessionInfo 会话信息
     * @return          处理结果
     */
    GoodsReturnApplyResponseVO editGoodsReturn(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo);
}
