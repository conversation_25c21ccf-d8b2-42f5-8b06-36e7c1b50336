package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description: 退货单详情
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 11:15
 **/
public class GoodsReturnDetailPageVO implements Serializable {

    private static final long serialVersionUID = -7028750467053359069L;

    @RpcModelProperty("商品id")
    private String productId;

    @RpcModelProperty("订单明细商品id")
    private String detailId;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("SPU-商品货号")
    private String goodsCode;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("商品单价")
    private String price;

    @RpcModelProperty("商品数量")
    private String quantity;

    @RpcModelProperty("商品总价")
    private String amount;

    @RpcModelProperty("商品图片")
    private String goodsPicturePath;

    @RpcModelProperty("危化品标签")
    private String dangerousTag;

    @RpcModelProperty("危化品标签ID")
    private Integer dangerousType;

    @RpcModelProperty("商品平台唯一编码")
    private String productCode;

    @RpcModelProperty("单位")
    private String unit;

    @RpcModelProperty("cas号")
    private String casNo;

    @RpcModelProperty("一级分类ID")
    private Integer firstCategoryId;

    @RpcModelProperty("包装规格")
    private String packingSpec;

    @RpcModelProperty("型号")
    private String modelNumber;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    @RpcModelProperty("完成周期")
    private String completionCycle;

    @RpcModelProperty("出版社")
    private String press;

    @RpcModelProperty("纯度/浓度")
    private String purity;

    @RpcModelProperty("产品规格")
    private String productSpec;


    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public GoodsReturnDetailPageVO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public void setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public String getPackingSpec() {
        return packingSpec;
    }

    public void setPackingSpec(String packingSpec) {
        this.packingSpec = packingSpec;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public void setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public void setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
    }

    public String getCompletionCycle() {
        return completionCycle;
    }

    public void setCompletionCycle(String completionCycle) {
        this.completionCycle = completionCycle;
    }

    public String getPress() {
        return press;
    }

    public void setPress(String press) {
        this.press = press;
    }

    public String getPurity() {
        return purity;
    }

    public void setPurity(String purity) {
        this.purity = purity;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public void setProductSpec(String productSpec) {
        this.productSpec = productSpec;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    @Override
    public String toString() {
        return "GoodsReturnDetailPageVO{" +
                "productId='" + productId + '\'' +
                ", detailId='" + detailId + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", goodsCode='" + goodsCode + '\'' +
                ", specification='" + specification + '\'' +
                ", brand='" + brand + '\'' +
                ", price='" + price + '\'' +
                ", quantity='" + quantity + '\'' +
                ", amount='" + amount + '\'' +
                ", goodsPicturePath='" + goodsPicturePath + '\'' +
                ", dangerousTag='" + dangerousTag + '\'' +
                ", dangerousType=" + dangerousType +
                ", productCode='" + productCode + '\'' +
                ", unit='" + unit + '\'' +
                ", casNo='" + casNo + '\'' +
                ", firstCategoryId=" + firstCategoryId +
                ", packingSpec='" + packingSpec + '\'' +
                ", modelNumber='" + modelNumber + '\'' +
                ", medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + '\'' +
                ", completionCycle='" + completionCycle + '\'' +
                ", press='" + press + '\'' +
                ", purity='" + purity + '\'' +
                ", productSpec='" + productSpec + '\'' +
                '}';
    }
}
