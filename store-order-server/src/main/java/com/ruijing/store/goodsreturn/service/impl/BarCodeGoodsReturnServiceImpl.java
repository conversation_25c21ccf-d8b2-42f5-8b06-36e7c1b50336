package com.ruijing.store.goodsreturn.service.impl;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyDetailRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnBarcodeDataDTO;
import com.ruijing.store.goodsreturn.service.BarCodeGoodsReturnService;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnApplyResponseVO;
import com.ruijing.store.order.api.base.enums.OrderAcceptanceWayEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.*;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BarCodeGoodDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BarCodeReturnInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BarCodeReturnResultVO;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import com.ruijing.store.order.rpc.client.OrderUniqueBarCodeRPCClient;
import com.ruijing.store.order.rpc.client.OrganizationClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.util.OrderUniqueBarcodeUtils;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.ListUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BarCodeGoodsReturnServiceImpl implements BarCodeGoodsReturnService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BarCodeGoodsReturnServiceImpl.class);

    /**
     * 可以进行退货的订单状态
     */
    private final List<Integer> CAN_RETURN_ORDER_STATUS_LIST = New.list(OrderStatusEnum.WaitingForReceive.getValue(),
            OrderStatusEnum.OrderReceiveApproval.getValue(),
            OrderStatusEnum.WaitingForStatement_1.getValue(),
            OrderStatusEnum.Finish.getValue());

    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Override
    public GoodsReturnApplyResponseVO applyGoodsReturnWithRandomBarCode(RjSessionInfo rjSessionInfo, GoodsReturnApplyRequestDTO request) {
        BusinessErrUtil.notNull(request.getOrderNo(), "退货单的订单号不可空！");
        BusinessErrUtil.notEmpty(request.getReturnApplyDetailList(), "需要退货的商品不可空！");
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(request.getOrderNo());
        BusinessErrUtil.notNull(orderMasterDO, "没有匹配的订单数据！");
        List<OrderUniqueBarCodeDTO> dbDataList = orderUniqueBarCodeRPCClient.findByOrderNo(request.getOrderNo(), null);
        if(CollectionUtils.isEmpty(dbDataList)){
            // 没有批次数据，即不使用批次（厦大附一实验动物订单），直接退货
            return buyerGoodsReturnService.applyGoodsReturn(request, rjSessionInfo);
        }
        // 过滤非退货的批次
        Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdDbDataListMap = dbDataList.stream()
                .filter(item-> item.getTransactionStatus() != OrderProductTransactionStatusEnum.RETURNING.getCode() &&
                        item.getTransactionStatus() != OrderProductTransactionStatusEnum.RETURNED.getCode())
                .collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId));
        // 生成二维码退货参数
        GoodsReturnBarCodeRequest barCodeRequest = new GoodsReturnBarCodeRequest();
        List<GoodsReturnBarCodeDetailRequest> goodsReturnBarCodeDetailRequestList = New.list();
        barCodeRequest.setOrderUniqueBarCodeList(goodsReturnBarCodeDetailRequestList);
        barCodeRequest.setImages(request.getGoodsImageList());
        barCodeRequest.setReturnReason(request.getReturnReason());
        barCodeRequest.setRemark(request.getRemark());
        request.getReturnApplyDetailList().forEach(returnItem->{
            Integer detailId = returnItem.getDetailId();
            List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = detailIdDbDataListMap.get(detailId);
            BusinessErrUtil.notEmpty(orderUniqueBarCodeDTOList, "需要退货的商品没有匹配的批次信息！");
            // 遍历非退货的批次，将需要退货数量的批次数据放入需要退货的请求体中
            Iterator<OrderUniqueBarCodeDTO> iterator = orderUniqueBarCodeDTOList.iterator();
            for(int i = 0; i < returnItem.getQuantity().intValue(); i++){
                BusinessErrUtil.isTrue(iterator.hasNext(), "当前需退货的批次已经不足以退货！");
                OrderUniqueBarCodeDTO uniqueBarCodeDTO = iterator.next();
                GoodsReturnBarCodeDetailRequest barCodeReqItem = new GoodsReturnBarCodeDetailRequest();
                barCodeReqItem.setBarCode(uniqueBarCodeDTO.getUniBarCode());
                barCodeReqItem.setOrderNo(request.getOrderNo());
                barCodeReqItem.setOrderDetailId(detailId);
                barCodeReqItem.setReason(returnItem.getReturnReason());
                barCodeReqItem.setDescription(returnItem.getRemark());
                goodsReturnBarCodeDetailRequestList.add(barCodeReqItem);
            }
        });

        // 主平台申请退货
        GoodsReturnApplyRequestDTO applyRequestDTO = wrapperGoodsReturnRequest(barCodeRequest, orderMasterDO);
        // 回写气瓶数据
        Map<Integer, List<String>> returnDetailIdGasBottlesMap = request.getReturnApplyDetailList().stream().filter(item->item.getReturnGasBottleBarcodes() != null)
                .collect(Collectors.toMap(GoodsReturnApplyDetailRequestDTO::getDetailId, GoodsReturnApplyDetailRequestDTO::getReturnGasBottleBarcodes, (o, n)->n));
        applyRequestDTO.getReturnApplyDetailList().forEach(detail-> detail.setReturnGasBottleBarcodes(returnDetailIdGasBottlesMap.get(detail.getDetailId())));
        GoodsReturnApplyResponseVO goodsReturnVO = buyerGoodsReturnService.applyGoodsReturn(applyRequestDTO, rjSessionInfo);
        // 更新二维码商品的信息
        List<OrderUniqueBarCodeDTO> barCodeDTOList = wrapperBarCodeBatches(barCodeRequest.getOrderUniqueBarCodeList(), goodsReturnVO);
        this.initBarcodeDataForApplyReturn(barCodeDTOList, goodsReturnVO.getReturnNo());
        return goodsReturnVO;
    }

    @Override
    public boolean applyOrderReturnForBarCode(RjSessionInfo rjSessionInfo, GoodsReturnBarCodeRequest request) {
        // 校验条形码商品的状态，只有未入库的商品可以退货
        validateBarCodeStatus(request);
        // 主平台申请退货
        GoodsReturnApplyRequestDTO applyRequestDTO = wrapperGoodsReturnRequest(request, orderMasterMapper.findByForderno(request.getOrderUniqueBarCodeList().get(0).getOrderNo()));
        GoodsReturnApplyResponseVO goodsReturnVO = buyerGoodsReturnService.applyGoodsReturn(applyRequestDTO, rjSessionInfo);
        // 更新二维码商品的信息
        List<OrderUniqueBarCodeDTO> barCodeDTOList = wrapperBarCodeBatches(request.getOrderUniqueBarCodeList(), goodsReturnVO);
        this.initBarcodeDataForApplyReturn(barCodeDTOList, goodsReturnVO.getReturnNo());
        return true;
    }

    private void validateBarCodeStatus(GoodsReturnBarCodeRequest request) {
        List<GoodsReturnBarCodeDetailRequest> orderUniqueBarCodeList = request.getOrderUniqueBarCodeList();
        Preconditions.notEmpty(orderUniqueBarCodeList, "orderUniqueBarCodeList must not be empty");
        String orderNo = orderUniqueBarCodeList.get(0).getOrderNo();
        Preconditions.notNull(orderNo, "orderNo must not be null");
        boolean hasNullCode = orderUniqueBarCodeList.stream().anyMatch(it -> it.getBarCode() == null);
        Preconditions.isTrue(!hasNullCode, "barCode must not be null");
        List<String> barCodeList = ListUtils.toList(orderUniqueBarCodeList, GoodsReturnBarCodeDetailRequest::getBarCode);

        List<OrderUniqueBarCodeDTO> items = orderUniqueBarCodeRPCClient.findByBarCode(barCodeList);
        if (request.getReturnId() == null) {
            items.forEach(it -> Preconditions.isTrue(OrderProductTransactionStatusEnum.RECEIVED.getCode() == it.getTransactionStatus()
                            || OrderProductTransactionStatusEnum.WAITING_FOR_RECEIVE.getCode() == it.getTransactionStatus(),
                    "商品" + it.getProductName() + ", 码：" + it.getUniBarCode() + "无法退货，当前状态为:" + OrderProductTransactionStatusEnum.getByCode(it.getTransactionStatus()).getDesc()));
        } else {
            items.forEach(it -> Preconditions.isTrue(OrderProductTransactionStatusEnum.RECEIVED.getCode() == it.getTransactionStatus()
                            || OrderProductTransactionStatusEnum.WAITING_FOR_RECEIVE.getCode() == it.getTransactionStatus()
                            || OrderProductTransactionStatusEnum.RETURNING.getCode() == it.getTransactionStatus(),
                    "商品" + it.getProductName() + ", 码：" + it.getUniBarCode() + "无法退货，当前状态为:" + OrderProductTransactionStatusEnum.getByCode(it.getTransactionStatus()).getDesc()));
        }

    }

    private List<OrderUniqueBarCodeDTO> wrapperBarCodeBatches(List<GoodsReturnBarCodeDetailRequest> barCodeList, GoodsReturnApplyResponseVO goodsReturnVO) {
        return barCodeList.stream().map(it -> {
            OrderUniqueBarCodeDTO dto = new OrderUniqueBarCodeDTO();
            dto.setUniBarCode(it.getBarCode());
            dto.setTransactionStatus(OrderProductTransactionStatusEnum.RETURNING.getCode());
            dto.setReturnReason(it.getReason());
            dto.setReturnDescription(it.getDescription());
            dto.setReturnNo(goodsReturnVO.getReturnNo());
            return dto;
        }).collect(Collectors.toList());
    }

    private GoodsReturnApplyRequestDTO wrapperGoodsReturnRequest(GoodsReturnBarCodeRequest request, OrderMasterDO order) {
        Preconditions.notNull(order, "order do not exist");
        GoodsReturnApplyRequestDTO params = new GoodsReturnApplyRequestDTO();
        params.setOrderNo(order.getForderno());
        params.setDepartmentId(order.getFbuydepartmentid());
        params.setDepartmentName(order.getFbuydepartment());
        params.setBuyerName(order.getFbuyername());
        params.setSupplierId(order.getFsuppid());
        params.setSupplierName(order.getFsuppname());
        params.setGoodsImageList(request.getImages());
        params.setId(request.getReturnId());
        params.setReturnReason(request.getReturnReason());
        params.setRemark(request.getRemark());

        params.setReturnApplyDetailList(wrapperReturnApplyDetailList(request.getOrderUniqueBarCodeList()));

        return params;
    }

    private List<GoodsReturnApplyDetailRequestDTO> wrapperReturnApplyDetailList(List<GoodsReturnBarCodeDetailRequest> orderUniqueBarCodeList) {
        List<Integer> detailIdList = orderUniqueBarCodeList.stream().map(GoodsReturnBarCodeDetailRequest::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailDO> detailList = orderDetailMapper.findByIdIn(detailIdList);
        Map<Integer, OrderDetailDO> detailIdIdentityMap = DictionaryUtils.toMap(detailList, OrderDetailDO::getId, Function.identity());
        Map<Integer, List<GoodsReturnBarCodeDetailRequest>> detailIdListMap = orderUniqueBarCodeList.stream().collect(Collectors.groupingBy(GoodsReturnBarCodeDetailRequest::getOrderDetailId));

        List<GoodsReturnApplyDetailRequestDTO> array = new ArrayList<>(detailIdListMap.size());
        detailIdListMap.forEach((detailId, list) -> {
            OrderDetailDO orderDetail = detailIdIdentityMap.get(detailId);
            if (orderDetail == null) {
                return;
            }

            GoodsReturnApplyDetailRequestDTO result = new GoodsReturnApplyDetailRequestDTO();
            result.setGoodsName(orderDetail.getFgoodname());
            result.setGoodsCode(orderDetail.getFgoodcode());
            result.setSpecification(orderDetail.getFspec());
            result.setBrand(orderDetail.getFbrand());
            result.setPrice(orderDetail.getFbidprice());
            result.setQuantity(BigDecimal.valueOf(list.size()));
            result.setAmount(BigDecimal.valueOf(list.size()).multiply(orderDetail.getFbidprice()));
            result.setReturnReason(list.get(0).getReason());
            result.setRemark(list.get(0).getDescription());
            result.setGoodsReturnBarcodeDataDTOList(list.stream().map(barcode->{
                GoodsReturnBarcodeDataDTO goodsReturnBarcodeDataDTO = new GoodsReturnBarcodeDataDTO();
                goodsReturnBarcodeDataDTO.setBarcode(barcode.getBarCode());
                goodsReturnBarcodeDataDTO.setReturnReason(barcode.getReason());
                goodsReturnBarcodeDataDTO.setReturnDescription(barcode.getDescription());
                return goodsReturnBarcodeDataDTO;
            }).collect(Collectors.toList()));

            result.setGoodsPicturePath(Optional.ofNullable(orderDetail.getFpicpath()).orElse(StringUtils.EMPTY));
            result.setDangerousTag(Optional.ofNullable(orderDetail.getDangerousTypeName()).orElse(DangerousTypeEnum.UN_DANGEROUS.getName()));

            result.setDetailId(detailId);
            result.setProductId(Optional.ofNullable(orderDetail.getProductSn()).map(Object::toString).orElse("0"));
            result.setUnit(Optional.ofNullable(orderDetail.getFunit()).orElse(StringUtils.EMPTY));
            array.add(result);
        });

        return array;
    }

    /**
     * 扫码提交退货申请
     * @param rjSessionInfo 用户会话信息
     * @param request 包含多个订单维度的退货请求
     * @return 退货结果，包含成功和失败的信息
     */
    @Override
    public BarCodeReturnResultVO applyOrderReturnByBarCodeAssemble(RjSessionInfo rjSessionInfo, 
                                                                BarCodeGoodsReturnRequest request) {
        // 初始化返回结果
        BarCodeReturnResultVO resultVO = new BarCodeReturnResultVO();
        resultVO.setSuccessList(New.list());
        resultVO.setFailureList(New.list());
        resultVO.setAllFailure(false);
        resultVO.setAllSuccess(false);
        
        // 初始化失败订单集合
        List<OrderBarCodeReturnRequest> failureOrders = New.list();
        List<OrderBarCodeReturnRequest> orderReturnList = request.getGoodsReturnOrderList();
        BusinessErrUtil.notEmpty(orderReturnList, ExecptionMessageEnum.PARAMETER_CANNOT_BE_EMPTY);

        List<String> orderNos = orderReturnList.stream()
            .map(OrderBarCodeReturnRequest::getOrderNo)
            .collect(Collectors.toList());

        // 批量查询订单
        List<OrderMasterDO> orderList = orderMasterMapper.findByFordernoIn(orderNos);
        if(CollectionUtils.isEmpty(orderList)){
            return resultVO;
        }

        // 统一校验订单退货权限
        this.returnOrderValidate(rjSessionInfo, orderList);
        // 构建订单号与订单对象的映射关系
        Map<String, OrderMasterDO> orderNoIdentityMap = DictionaryUtils.toMap(orderList, OrderMasterDO::getForderno, Function.identity());

        // 遍历处理每个订单维度的退货请求
        orderReturnList.forEach(orderReturn -> {
            String orderNo = orderReturn.getOrderNo();
            List<GoodsReturnDetailRequest> originalDetails = orderReturn.getOrderUniqueBarCodeList();
            BusinessErrUtil.notEmpty(originalDetails, ExecptionMessageEnum.RETURN_INFO_CANNOT_BE_EMPTY);
            OrderMasterDO matchOrder = orderNoIdentityMap.get(orderNo);

            // 初始化失败订单对象
            OrderBarCodeReturnRequest failureOrder = new OrderBarCodeReturnRequest();
            failureOrder.setOrderNo(orderNo);
            failureOrder.setReturnReason(orderReturn.getReturnReason());
            failureOrder.setRemark(orderReturn.getRemark());
            List<GoodsReturnDetailRequest> failureDetails = New.list();

            // 创建退货信息对象
            BarCodeReturnInfoVO barCodeReturnInfoVO = new BarCodeReturnInfoVO();
            barCodeReturnInfoVO.setOrderNo(orderNo);
            barCodeReturnInfoVO.setGoodsList(New.list());

            // 校验订单是否存在
            if (Objects.isNull(matchOrder)) {
                originalDetails.forEach(d -> {
                    GoodsReturnDetailRequest fd = copyDetailWithReason(d, "订单不存在");
                    failureDetails.add(fd);
                    
                    BarCodeGoodDetailVO goodsInfo = new BarCodeGoodDetailVO();
                    goodsInfo.setOrderDetailId(d.getOrderDetailId());
                    goodsInfo.setBarCode(d.getBarCode());
                    goodsInfo.setFailureReason("订单不存在");
                    barCodeReturnInfoVO.getGoodsList().add(goodsInfo);
                });
                failureOrder.setOrderUniqueBarCodeList(failureDetails);
                failureOrders.add(failureOrder);
                resultVO.getFailureList().add(barCodeReturnInfoVO);
                return;
            }

            // 校验订单状态是否允许退货
            if(!CAN_RETURN_ORDER_STATUS_LIST.contains(matchOrder.getStatus())){
                String reason = "当前订单状态非待收货/待验收审批/待结算/已完成，不可进行退货";
                originalDetails.forEach(d -> {
                    GoodsReturnDetailRequest fd = copyDetailWithReason(d, reason);
                    failureDetails.add(fd);
                    
                    BarCodeGoodDetailVO goodsInfo = new BarCodeGoodDetailVO();
                    goodsInfo.setOrderDetailId(d.getOrderDetailId());
                    goodsInfo.setBarCode(d.getBarCode());
                    goodsInfo.setFailureReason(reason);
                    barCodeReturnInfoVO.getGoodsList().add(goodsInfo);
                });
                failureOrder.setOrderUniqueBarCodeList(failureDetails);
                failureOrders.add(failureOrder);
                resultVO.getFailureList().add(barCodeReturnInfoVO);
                return;
            }

            // 校验订单类型必须为线上订单
            if(OrderSpeciesEnum.OFFLINE.getValue().intValue() == matchOrder.getSpecies()){
                String reason = "只能扫线上订单的商品的二维码";
                originalDetails.forEach(d -> {
                    GoodsReturnDetailRequest fd = copyDetailWithReason(d, reason);
                    failureDetails.add(fd);
                    
                    BarCodeGoodDetailVO goodsInfo = new BarCodeGoodDetailVO();
                    goodsInfo.setOrderDetailId(d.getOrderDetailId());
                    goodsInfo.setBarCode(d.getBarCode());
                    goodsInfo.setFailureReason(reason);
                    barCodeReturnInfoVO.getGoodsList().add(goodsInfo);
                });
                failureOrder.setOrderUniqueBarCodeList(failureDetails);
                failureOrders.add(failureOrder);
                resultVO.getFailureList().add(barCodeReturnInfoVO);
                return;
            }

            try {
                // 构建商品维度的退货请求参数
                GoodsReturnBarCodeRequest params = buildBarCodeRequest(orderReturn, originalDetails);
                // 校验条码状态是否允许退货
                validateBarCodeStatus(params);
                GoodsReturnApplyRequestDTO applyRequestDTO = wrapperGoodsReturnRequest(params, matchOrder);
                // 调用退货服务
                GoodsReturnApplyResponseVO applyResponseVO = buyerGoodsReturnService.applyGoodsReturn(applyRequestDTO, rjSessionInfo);

                // 更新条码状态为退货中
                List<OrderUniqueBarCodeDTO> barCodeDTOList = wrapperBarCodeBatches(params.getOrderUniqueBarCodeList(), applyResponseVO);
                if (CollectionUtils.isNotEmpty(barCodeDTOList)) {
                    this.initBarcodeDataForApplyReturn(barCodeDTOList, applyResponseVO.getReturnNo());
                }
                
                // 记录成功退货信息
                barCodeReturnInfoVO.setReturnNo(applyResponseVO.getReturnNo());
                barCodeReturnInfoVO.setReturnId(applyResponseVO.getReturnId());
                barCodeReturnInfoVO.setApplyName(applyResponseVO.getApplyName());
                
                // 添加成功退货的商品信息
                originalDetails.forEach(d -> {
                    BarCodeGoodDetailVO goodsInfo = new BarCodeGoodDetailVO();
                    goodsInfo.setOrderDetailId(d.getOrderDetailId());
                    goodsInfo.setBarCode(d.getBarCode());
                    barCodeReturnInfoVO.getGoodsList().add(goodsInfo);
                });
                
                resultVO.getSuccessList().add(barCodeReturnInfoVO);
                
            } catch (Exception e) {
                // 记录错误日志并收集失败明细
                LOGGER.error("扫码退货失败, 订单号:{}", orderNo, e);
                String errorMsg = e.getMessage();
                originalDetails.forEach(d -> {
                    GoodsReturnDetailRequest fd = copyDetailWithReason(d, errorMsg);
                    failureDetails.add(fd);
                    
                    BarCodeGoodDetailVO goodsInfo = new BarCodeGoodDetailVO();
                    goodsInfo.setOrderDetailId(d.getOrderDetailId());
                    goodsInfo.setBarCode(d.getBarCode());
                    goodsInfo.setFailureReason(errorMsg);
                    barCodeReturnInfoVO.getGoodsList().add(goodsInfo);
                });
                failureOrder.setOrderUniqueBarCodeList(failureDetails);
                failureOrders.add(failureOrder);
                resultVO.getFailureList().add(barCodeReturnInfoVO);
            }
        });

        // 设置是否全部成功/全部失败 标识
        resultVO.setAllSuccess(CollectionUtils.isEmpty(resultVO.getFailureList()));
        resultVO.setAllFailure(CollectionUtils.isEmpty(resultVO.getSuccessList()));
        
        return resultVO;
    }

    /**
     * 构建条码退货请求参数
     * @param orderReturn 订单维度退货请求
     * @param details 商品明细列表
     * @return 商品维度退货请求
     */
    private GoodsReturnBarCodeRequest buildBarCodeRequest(OrderBarCodeReturnRequest orderReturn, 
                                                         List<GoodsReturnDetailRequest> details) {
        GoodsReturnBarCodeRequest params = new GoodsReturnBarCodeRequest();
        List<GoodsReturnBarCodeDetailRequest> detailRequests = details.stream()
            .map(d -> convertToBarCodeDetail(d, orderReturn))
            .collect(Collectors.toList());
        params.setOrderUniqueBarCodeList(detailRequests);
        params.setReturnReason(orderReturn.getReturnReason());
        params.setRemark(orderReturn.getRemark());
        return params;
    }

    /**
     * 复制商品明细并附加失败原因
     * @param original 原始商品明细
     * @param reason 失败原因
     * @return 带失败原因的新明细对象
     */
    private GoodsReturnDetailRequest copyDetailWithReason(GoodsReturnDetailRequest original, String reason) {
        GoodsReturnDetailRequest detail = new GoodsReturnDetailRequest();
        detail.setBarCode(original.getBarCode());
        detail.setOrderDetailId(original.getOrderDetailId());
        detail.setReturnReason(StringUtils.isNotBlank(reason) ? reason : original.getReturnReason());
        detail.setRemark(original.getRemark());
        return detail;
    }

    /**
     * 转换商品明细到条码退货明细
     * @param detail 商品维度明细
     * @param orderReturn 订单维度请求
     * @return 条码退货明细
     */
    private GoodsReturnBarCodeDetailRequest convertToBarCodeDetail(GoodsReturnDetailRequest detail, 
                                                                  OrderBarCodeReturnRequest orderReturn) {
        GoodsReturnBarCodeDetailRequest req = new GoodsReturnBarCodeDetailRequest();
        req.setBarCode(detail.getBarCode());
        req.setOrderNo(orderReturn.getOrderNo());
        req.setOrderDetailId(detail.getOrderDetailId());
        req.setReason(StringUtils.isNotBlank(detail.getReturnReason()) ? detail.getReturnReason() : orderReturn.getReturnReason());
        req.setDescription(StringUtils.isNotBlank(detail.getRemark()) ? detail.getRemark() : orderReturn.getRemark());
        return req;
    }

    @Override
    public boolean updateOrderReturnForBarCode(GoodsReturn request, Integer status) {
        if (request == null) {
            return false;
        }
        return orderUniqueBarCodeRPCClient.updateStatusByReturnNo(request.getReturnNo(), status);
    }

    @Override
    public void resetBarcodeStatusAfterCancelReturn(GoodsReturn request){
        String returnNo = request.getReturnNo();
        Integer orderId = request.getOrderId();
        List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByReturnNo(returnNo);
        if(CollectionUtils.isEmpty(orderUniqueBarCodeDTOList)){
            return;
        }
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.ACCEPTANCE_WAY.getValue());
        boolean scanAccept = CollectionUtils.isNotEmpty(orderExtraDTOList) && OrderAcceptanceWayEnum.SCAN_BARCODE.value.toString().equals(orderExtraDTOList.get(0).getExtraValue());
        // 临床更新二维码信息（回复为未退货状态）
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        Integer transactionStatusToUpdate = OrderUniqueBarcodeUtils.orderStatus2ProductTransactionStatus(orderMasterDO.getStatus()).getCode();
        orderUniqueBarCodeDTOList.forEach(item->{
            item.setTransactionStatus(transactionStatusToUpdate);
            if(scanAccept){
                // 扫码验收，需要判断库房状态
                if(OrderProductInventoryStatusEnum.COMPLETE_INBOUND.getCode() == item.getInventoryStatus()
                        || OrderProductInventoryStatusEnum.COMPLETE_OUTBOUND.getCode() == item.getInventoryStatus()){
                    item.setTransactionStatus(OrderProductTransactionStatusEnum.RECEIVED.getCode());
                }
            }
        });
        Map<Integer, List<String>> transactionStatusBarcodeMap = DictionaryUtils.groupFieldByKey(orderUniqueBarCodeDTOList, OrderUniqueBarCodeDTO::getTransactionStatus, OrderUniqueBarCodeDTO::getUniBarCode);
        transactionStatusBarcodeMap.forEach((transactionStatus, barcodes)-> orderUniqueBarCodeRPCClient.updateStatusByBarcode(barcodes, transactionStatus, null));
    }

    /**
     * 发起退货，一物一码数据初始化
     * @param barCodeDTOList 一物一码数据
     * @param returnNo 退货单号
     */
    private void initBarcodeDataForApplyReturn(List<OrderUniqueBarCodeDTO> barCodeDTOList, String returnNo){
        orderUniqueBarCodeRPCClient.modifyOrderBarCodeBatches(barCodeDTOList);
        orderUniqueBarCodeRPCClient.updateStatusByReturnNo(returnNo, OrderProductTransactionStatusEnum.RETURNING.getCode());
    }

    /**
     * 退货校验
     * @param rjSessionInfo 用户信息
     * @param orderList 退货相关订单
     */
    private void returnOrderValidate(RjSessionInfo rjSessionInfo, List<OrderMasterDO> orderList){
        // 1.校验是否扫了别的单位的码
        BusinessErrUtil.isTrue(orderList.stream().allMatch(orderMasterDO -> rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER).equals(orderMasterDO.getFuserid())), "需要退货的商品非本单位订单下的商品！");
        // 2.校验整单退货
        boolean limitWholeReturnOnly = CommonValueUtils.parseNumberStrToBoolean(sysConfigClient.getConfigByOrgCodeAndConfigCode(orderList.get(0).getFusercode(), ConfigCodeEnum.ORDER_RETURN_ONLY_WHOLE.name()));
        BusinessErrUtil.isTrue(!limitWholeReturnOnly, "只能整单退货，无法使用扫码退货");
        // 3.校验是否订单都是一物一码
        List<Integer> orderIdList = orderList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(orderIdList, OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        long eachProductEachCodeOrderCounts = orderExtraDTOList.stream().filter(extra-> CommonValueUtils.parseNumberStrToBoolean(extra.getExtraValue())).count();
        BusinessErrUtil.isTrue(eachProductEachCodeOrderCounts == orderList.size(), "退货商品中存在非一物一码的单据，无法进行扫码退货！");
    }
}
