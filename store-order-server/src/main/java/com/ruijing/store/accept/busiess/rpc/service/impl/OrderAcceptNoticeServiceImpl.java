package com.ruijing.store.accept.busiess.rpc.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.api.outer.buyer.OrderAcceptNoticeService;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.outer.buyer.OuterBuyerCommonProcessDTO;
import com.reagent.order.dto.request.UploadFileDTO;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.OrderUploadFileRpcClient;
import com.ruijing.store.order.rpc.client.SuppClient;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/11/14 15:52
 * @description 管理平台收货通知
 */
@MSharpService
public class OrderAcceptNoticeServiceImpl implements OrderAcceptNoticeService {

    /**
     * 需要限制不能调用该接口的状态--待验收前的
     */
    private final static List<Integer> ORDER_STATUS_NEED_LIMIT_RECEIVE = New.list(
            OrderStatusEnum.WaitingForDockingConfirm.getValue(),
            OrderStatusEnum.DeckingFail.getValue(),
            OrderStatusEnum.WaitingForConfirm.getValue(),
            OrderStatusEnum.WaitingForDelivery.getValue(),
            OrderStatusEnum.PurchaseApplyToCancel.getValue(),
            OrderStatusEnum.SupplierApplyToCancel.getValue()
    );

    /**
     * 待结算的发起结算的单位
     */
    private final static List<String> ORG_LIST_STATEMENT_WHEN_WAITING_STATEMENT = New.list(OrgConst.HUA_NAN_NONG_YE_DA_XUE);

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderStatementService orderStatementService;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private SuppClient suppClient;

    @Resource
    private OrderUploadFileRpcClient orderUploadFileRpcClient;

    @Override
    @ServiceLog(description = "管理平台发起订单验收", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> acceptOrder(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        String orderNo = outerBuyerCommonProcessDTO.getOrderNo();
        String orgCode = outerBuyerCommonProcessDTO.getOrgCode();
        List<UploadFileDTO> uploadFileDTOList = outerBuyerCommonProcessDTO.getUploadFileDTOList();
        //定制化校验
        this.customizedVerification(orgCode, uploadFileDTOList);
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        Preconditions.notNull(orderMasterDO, "没有找到订单号为" + orderNo + "的订单");
        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orgCode);
        if(dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null)){
            BusinessErrUtil.isTrue(OmsDockingConfigValueEnum.ACCEPT_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncStatusAccept()), DockingConstant.CONFIG_MISMATCH_HINT);
        }else {
            BusinessErrUtil.isTrue(dockingConfigCommonService.getIfNeedDocking(orderMasterDO, New.list(OrderDockingStrategyEnum.HANDLE_RECEIVE_REQUEST)), DockingConstant.CONFIG_MISMATCH_HINT);
        }
        Integer nowStatus = orderMasterDO.getStatus();
        if(ORDER_STATUS_NEED_LIMIT_RECEIVE.contains(nowStatus)){
            Set<Integer> specialOrgSet = New.set(OrgEnum.QING_HUA_DA_XUE_GU_JI_YAN_JIU_SHENG_YUAN.getValue(), OrgEnum.BEI_JING_DA_XUE_SHEN_ZHEN_YAN_JIU_SHENG_YUAN.getValue());
            if (OrderStatusEnum.WaitingForDelivery.getValue().equals(nowStatus)
                    && specialOrgSet.contains(orderMasterDO.getFuserid())) {
                // 清华深圳研究院、北大深圳研究院，如果待发货时对方调用收货，则自动发货并验收
                suppClient.autoDelivery(orderMasterDO.getFsuppcode(), orderNo);
                nowStatus = OrderStatusEnum.WaitingForReceive.value;
            }else {
                return RemoteResponse.<Boolean>custom().setFailure("当前订单状态："+ Objects.requireNonNull(OrderStatusEnum.get(orderMasterDO.getStatus())).getName() + "，请等待发货后再验收").setData(false);
            }
        }
        if (OrderStatusEnum.WaitingForReceive.getValue().equals(nowStatus)) {
            // 订单状态在待验收，收货动作为收货
            OrderReceiptParamDTO orderReceiptParamDTO = new OrderReceiptParamDTO();
            orderReceiptParamDTO.setOrgCode(orgCode);
            orderReceiptParamDTO.setOrderId(orderMasterDO.getId());
            orderReceiptParamDTO.setOrgId(orderMasterDO.getFuserid());
            orderReceiptParamDTO.setUserId(DockingConstant.SYSTEM_OPERATOR_ID);
            orderReceiptParamDTO.setUserName(DockingConstant.SYSTEM_OPERATOR_NAME);
            orderReceiptParamDTO.setManual(false);
            orderAcceptService.userAcceptOrder(orderReceiptParamDTO);
            this.customizedBusiness(orderMasterDO.getId(), orgCode, orderNo, uploadFileDTOList);
            return RemoteResponse.success();
        }
        if (OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus()) && ORG_LIST_STATEMENT_WHEN_WAITING_STATEMENT.contains(outerBuyerCommonProcessDTO.getOrgCode())) {
            // 订单状态在待结算，收货动作变为发起结算
            orderStatementService.createStatement(orderMasterDO, DockingConstant.SYSTEM_OPERATOR_ID, DockingConstant.SYSTEM_OPERATOR_NAME, orderMasterDO.getInventoryStatus().intValue());
            return RemoteResponse.success();
        }
        return RemoteResponse.success();
    }

    /**
     * 定制化业务
     *
     * @param orgCode
     * @param orderNo
     * @param uploadFileDTOList
     */
    private void customizedBusiness(Integer orderId, String orgCode, String orderNo, List<UploadFileDTO> uploadFileDTOList){
        if(OrgEnum.XIANG_GANG_KE_JI_DA_XUE_GUANG_ZHOU.getCode().equals(orgCode)){
            List<OrderUploadFileDTO> orderUploadFileDTOList = New.list();
            for(UploadFileDTO uploadFileDTO: uploadFileDTOList){
                String fileUrl = uploadFileDTO.getFileUrl();
                Integer fileType = uploadFileDTO.getFileType();
                //上传验收单
                OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
                orderUploadFileDTO.setOrderNo(orderNo);
                orderUploadFileDTO.setOrderId(orderId);
                orderUploadFileDTO.setUrl(fileUrl);
                orderUploadFileDTO.setFileName("香港科技大学验收单" + fileUrl);
                orderUploadFileDTO.setFileBusinessType(fileType);
                orderUploadFileDTOList.add(orderUploadFileDTO);
            }
            orderUploadFileRpcClient.insertList(orderUploadFileDTOList);
        }
    }

    /**
     * 定制化校验
     *
     * @param orgCode
     * @param uploadFileDTOList
     */
    private void customizedVerification(String orgCode, List<UploadFileDTO> uploadFileDTOList){
        if(OrgEnum.XIANG_GANG_KE_JI_DA_XUE_GUANG_ZHOU.getCode().equals(orgCode)){
            BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(uploadFileDTOList), ExecptionMessageEnum.ACCEPTANCE_FORM_QUANTITY_ERROR);
            for(UploadFileDTO uploadFileDTO: uploadFileDTOList){
                Integer fileType = uploadFileDTO.getFileType();
                BusinessErrUtil.isTrue(FileBusinessTypeEnum.ACCEPTANCE_FORM.getCode().equals(fileType), ExecptionMessageEnum.ACCEPTANCE_FORM_TYPE_ERROR);
            }
        }
    }

}
