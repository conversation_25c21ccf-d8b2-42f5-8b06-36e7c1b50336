package com.ruijing.store.exception.enums;

import com.ruijing.order.constant.CommonConstant;
import com.ruijing.order.enums.IBaseTemplateEnum;

/**
 * <AUTHOR>
 * @Description 异常信息枚举
 * @Date: 2024/10/15 17:39
 * templateCode 前缀 :{@link CommonConstant#LOCALE_PREFIX_ORDER_STORE }
 * 定义参数模板的时候约定 参数占位符规定成递增数字形式， {{0}} {{1}} {{2}}...
 **/
public enum ExecptionMessageEnum implements IBaseTemplateEnum {

    // 常用
    NON_OMS_USER_CANNOT_VIEW("non.oms.user.cannot.view", "非oms用户不可查看"),
    OPERATION_FAILED_NO_PERMISSION("operation.failed.no.permission", "操作失败，无权限！"),
    LOGIN_EXPIRED_RELOGIN("login.expired.relogin", "登陆过期，重新登陆"),
    WRONG_USER_TYPE("wrong.user.type", "错误的用户类型"),
    NO_INSTITUTION_INFO("no.institution.info", "无机构信息！"),
    NO_USER_INFO("no.user.info", "无用户信息！"),
    NO_PERMISSION_TO_VIEW_UNIT_DATA("no.permission.to.view.unit.data", "您没有权限查看该单位的数据"),
    PARAMETER_CANNOT_BE_EMPTY("parameter.cannot.be.empty", "参数不能为空"),

    // 参数或配置错误相关
    CONFIG_RETRIEVAL_FAILED("config.retrieval.failed", "获取配置失败！查无结果！"),
    CONFIG_INPUT_ERROR("config.input.error", "配置项入参出错，超长或非数值，请重新配置"),
    SWELLING_CONFIG_ERROR("swelling.config.error", "中肿配置有误"),
    ACCEPTANCE_CONFIG_NOT_SET("acceptance.config.not.set", "{{0}}未配置~！请联系客服~！"),
    SYSTEM_CONFIG_READ_ERROR("system.config.read.error", "系统对接配置读取错误！配置了无效的业务范围！"),
    ORG_CODE_REQUIRED_FOR_CONFIG("org.code.required.for.config", "使用该功能生成配置必传orgCode"),
    DENSITY_NOT_SET_ERROR("density.not.set.error", "单位的液体或气体密度没设置，错误码：{{0}}"),
    QILU_UNIVERSITY_CONFIG_NOT_READ("qilu.university.config.not.read", "未读取到齐鲁工业大学的对接配置信息！"),
    UNIT_CONFIG_UNAVAILABLE("unit.config.unavailable", "无法获取单位配置，请重新登录后重试，若多次失败请联系客服"),
    END_TIME_FORMAT_ERROR("end.time.format.error", "结束时间格式错误"),
    START_TIME_FORMAT_ERROR("start.time.format.error", "起始时间格式错误"),
    UNRECOGNIZED_OPERATION_TYPE("unrecognized.operation.type", "无法识别的操作类型"),
    ORDER_NUMBER_MAX_100_CHARS("order.number.max.100.chars", "输入订单号不可超过100个字符"),
    UPLOAD_ATTACHMENT_LIMIT_EXCEEDED("upload.attachment.limit.exceeded", "上传附件数量超过{{0}}个"),
    ILLEGAL_FILE_TYPE("illegal.file.type", "不合法的文件类型！"),
    AMOUNT_CANNOT_START_WITH_DOT("amount.cannot.start.with.dot", "金额不能以.开头"),
    AMOUNT_CANNOT_START_WITH_ZERO("amount.cannot.start.with.zero", "金额不能以0开头,错误金额:{{0}}"),
    AMOUNT_EXCEEDS_LIMIT("amount.exceeds.limit", "金额不能超过千亿级别,错误金额:{{0}}"),
    ORDER_NUMBER_REQUIRED("order.number.required", "订单号必填！"),
    INVOICE_CODE_REQUIRED("invoice.code.required", "需输入10-12位的发票代码"),
    INVOICE_NUMBER_REQUIRED("invoice.number.required", "需输入20位的发票号"),
    INVOICE_NUMBER_8_DIGITS("invoice.number.8.digits", "需输入8位的发票号码"),
    TIME_SPAN_LIMIT_EXCEEDED("time.span.limit.exceeded", "时间跨度最长为{{0}}天，请重新选择时间"),
    INVOICE_AMOUNT_MISMATCH("invoice.amount.mismatch", "发票总金额和订单金额不一致，请检查发票金额是否正确"),
    QUERY_LIMIT_EXCEEDED("query.limit.exceeded", "查询个数不能大于{{0}}数据"),
    BATCH_DELETION_LIMIT_50_RECORDS("batch.deletion.limit.50.records", "批量删除不能超过50条记录"),
    QUERY_ORDER_ID_LIST_LIMIT("query.order.id.list.limit", "单次查询订单id列表长度不可超过{{0}}"),

    // 验收合同相关
    UPLOAD_CONTRACT_FAILED("upload.contract.failed", "上传合同保存合同信息失败，请重试或联系客服。"),
    UPLOAD_CONTRACT_UNIT_MISMATCH("upload.contract.unit.mismatch", "上传合同对应的订单下单单位，需要与当前登录用户的单位相同，请更换有权限的账户或选择对应的单位"),
    UPLOAD_CONTRACT_SEARCH_ERROR("upload.contract.search.error", "上传订单合同搜索订单出错，请重试或联系客服"),
    ORDER_EXCEEDS_LIMIT_NO_CONTRACT("order.exceeds.limit.no.contract", "存在订单{{0}}超过无需上传合同限制金额且未上传合同，请处理对应的单据"),
    ONLINE_ORDER_CONTRACT_REQUIRED("online.order.contract.required", "根据单位规定，线上单总价大于等于5万元时，需上传合同。"),
    OFFLINE_ORDER_CONTRACT_REQUIRED("offline.order.contract.required", "根据单位规定，线下单总价大于等于1万元时，需上传合同。"),
    OFFLINE_OR_RESEARCH_CONTRACT_REQUIRED("offline.or.research.contract.required", "根据单位规定，线下单总价大于等于1万元，或者购买科研服务类商品，需上传合同"),
    RESEARCH_SERVICE_CONTRACT_REQUIRED("research.service.contract.required", "根据单位规定，购买科研服务类商品需上传合同"),
    ORDER_EXCEEDS_LIMIT_UPLOAD_CONTRACT("order.exceeds.limit.upload.contract", "该订单金额超过{{0}}，请上传合同。"),
    SUPPLIER_CONTRACT_NOT_SIGNED("supplier.contract.not.signed", "供应商未签署合同，不能确认订单！"),
    ORDER_CONTRACT_INFO_ERROR("order.contract.info.error", "获取订单合同信息搜索订单出错，请重试或联系客服"),
    PLEASE_UPLOAD_CONTRACT("please.upload.contract", "请先上传合同！"),

    // 订单验收相关
    SELECT_ORDER_FOR_ACCEPTANCE("select.order.for.acceptance", "请选择需要验收的订单"),
    ORG_NOT_CONFIGURED_ACCEPTANCE_METHOD("org.not.configured.acceptance.method", "{{0}}未配置验收方式，需要联系客服配置验收方式"),
    ORG_CROSS_ACCEPTANCE_NOT_ALLOWED("org.cross.acceptance.not.allowed", "{{0}}配置为交叉验收，本人不可验收本人下的订单，请联系课题组内其他有验收权限的人验收此订单"),
    MOVE_TO_MANAGEMENT_PLATFORM_FOR_ACCEPTANCE("move.to.management.platform.for.acceptance", "请移至管理平台进行验收"),
    ONLY_SPECIFIC_STATUS_CAN_ADD_ACCEPTANCE_IMAGES("only.specific.status.can.add.acceptance.images", "仅 待验收审批、待结算、结算中、已完成 状态的单据可以添加验收图片，请确认您的订单状态是否在此范围内"),
    UNIT_CROSS_ACCEPTANCE_NOT_ALLOWED("unit.cross.acceptance.not.allowed", "单位配置为交叉验收，您不可以验收自己的订单，请联系其他成员验收"),
    NO_IMAGE_ACCEPTANCE_PERMISSION("no.image.acceptance.permission", "当前登录用户在此订单对应部门中没有图片验收权限，不可验收"),
    NO_PHOTO_ACCEPTANCE_REQUIRED("no.photo.acceptance.required", "无需拍照验收"),
    MAX_UPLOAD_ACCEPTANCE_PHOTOS("max.upload.acceptance.photos", "最多可上传{{0}}张验收照片"),
    UNIT_ACCEPTANCE_CONFIG_NOT_FOUND("unit.acceptance.config.not.found", "未获取到单位验收配置"),
    IMAGE_UPLOAD_CONFIG_NOT_FOUND("image.upload.config.not.found", "未获取到此单位图片上传配置，请先联系客服配置{{0}}的图片验收配置"),
    ORDER_NOT_PENDING_ACCEPTANCE("order.not.pending.acceptance", "此单状态不在待验收状态，请刷新重试"),
    ORDER_NOT_PENDING_ACCEPTANCE_CAN_NOT_SYNC_STATUS("order.not.pending.acceptance.can.not.sync.status", "此单状态不在待验收状态，不做订单同步"),
    QR_CODE_ACCEPTANCE_REQUIRED("qr.code.acceptance.required", "该订单设置了扫码验收，麻烦请到库房系统进行扫码验收！"),
    ORDER_ID_REQUIRED_FOR_IMAGES("order.id.required.for.images", "追加验收图片必须先选择订单，请传入订单id"),
    BATCH_ACCEPTANCE_NOT_ALLOWED("batch.acceptance.not.allowed", "配置图片验收后不能批量验收订单"),
    ACCEPTANCE_FORM_QUANTITY_ERROR("acceptance.form.quantity.error", "验收单数量错误"),
    ACCEPTANCE_FORM_TYPE_ERROR("acceptance.form.type.error", "验收单类型错误"),
    NON_SERVICE_GOODS_PHOTO_REQUIRED("non.service.goods.photo.required", "包含非服务类商品！除服务类的商品必须强制拍照验收！"),
    INVOICE_INFO_NOT_FILLED("invoice.info.not.filled", "未填写发票信息，暂不可确认验收。请前往PC端采购人中心/订单管理对应的订单详情页补充发票信息。"),
    HAZARDOUS_MATERIALS_PHOTO_REQUIRED("hazardous.materials.photo.required", "剧毒化学品(id:506)、易制毒化学品(id:488)、易制爆化学品(id:497)、实验气体(id:476)必须强制拍照验收!"),
    JOB_NUMBER_REQUIRED_FOR_ACCEPTANCE("job.number.required.for.acceptance", "应单位要求，请完善工号再进行验收，非有学校工号的学校教职工不能进行验收，否则会影响报销，导致报销失败"),
    ORDER_SUBMITTED_FOR_ACCEPTANCE("order.submitted.for.acceptance", "当前订单已经提交验收！请稍后再试！"),
    INSPECTION_REQUIREMENTS_UNIT("inspection.requirements.unit", "按单位要求，非动物类和非服务类，且地址标签为院内的订单，由统一验收人验收"),
    NO_MATCHING_ACCEPTANCE_MODE("no.matching.acceptance.mode", "无匹配验收模式~! 请联系客服"),
    UNAUTHORIZED_PIPE_PRODUCT_ACCEPTANCE("unauthorized.pipe.product.acceptance", "无权限验收管制品订单，请联系管制品库管验收"),
    END_OF_MONTH_CONSUMABLES_LIMIT("end.of.month.consumables.limit", "月底耗材月结限制验收，请第二天再进行验收操作"),
    NO_ACCEPTANCE_AUTHORITY("no.acceptance.authority", "没有验收权限!"),
    SPOT_WAREHOUSE_DELIVERY_NOT_APPROVED("spot.warehouse.delivery.not.approved", "现货仓-出库单未审批通过，无法验收"),
    ORDER_NEEDS_TEAM_ACCEPTANCE("order.needs.team.acceptance", "订单号：{{0}}，需要本课题组其他成员验收。"),
    FILL_INSPECTOR_NAME("fill.inspector.name", "请填写验收人姓名！勿填采购人姓名"),
    ACCEPTANCE_FAILED_NO_ORDER("acceptance.failed.no.order", "验收失败!查无订单!"),
    APPROVAL_CARD_REPLACEMENT_FAILED("approval.card.replacement.failed", "验收审批换卡失败！{{0}}"),
    PROCESS_APPROVAL_LOGIC_FAILED_NO_CARD("process.approval.logic.failed.no.card", "{{0}}处理验收审批流通过逻辑失败！订单无绑卡信息！"),
    NO_ACCEPTANCE_APPROVAL_AUTHORITY("no.acceptance.approval.authority", "没有验收审批权限!"),
    ORDER_NOT_PENDING_APPROVAL_RETRY("order.not.pending.approval.retry", "订单不是待验收审批状态，刷新后重试！"),
    ORDER_NOT_PENDING_APPROVAL_NO_OPERATION("order.not.pending.approval.no.operation", "订单不是待验收审批状态，无法进行操作！"),
    ACCEPTANCE_FAILED_NO_CARD_INFO("acceptance.failed.no.card.info", "验收失败！订单无绑卡信息！"),
    ACCEPTANCE_APPROVAL_FAILED_NO_LOGIN_INFO("acceptance.approval.failed.no.login.info", "验收审批失败！查无登录人信息"),
    NO_DELAY_ACCEPTANCE_OTHER_SUPPLIERS("no.delay.acceptance.other.suppliers", "不能延迟验收其它供应商的订单"),
    NO_REPEAT_DELAYED_ACCEPTANCE("no.repeat.delayed.acceptance", "不能重复延时验收"),
    PRECURSOR_DRUG_ORDER_UPLOAD_LIMIT("precursor.drug.order.upload.limit", "易制毒类订单备案需要上传小于或等于6张备案图片"),
    EXPLOSIVE_ORDER_NO_IMAGE_REQUIRED("explosive.order.no.image.required", "易制爆类订单备案不需要上传备案图片"),
    RECEIVING_FAILED_WRONG_STATUS("receiving.failed.wrong.status", "收货失败, 订单非待收货状态, 当前状态为:{{0}}"),
    UPLOAD_IMAGES_BEFORE_SUBMIT("upload.images.before.submit", "请上传（不超过{{0}}张）图片再提交"),
    INVOICE_REQUIRED_FOR_INSTRUMENTS("invoice.required.for.instruments", "1500元及以上仪器类线下单确认收货前须填写发票信息，请点击订单详情，填写发票信息并保存。"),
    SERVICE_GOODS_ATTACHMENTS_REQUIRED("service.goods.attachments.required", "包含服务类商品，其他附件为必传"),
    ENTER_BATCH_INFO_BEFORE_RECEIVING("enter.batch.info.before.receiving", "收货前请先录入全部商品的批次信息。"),
    NO_NEED_TO_UPLOAD_ATTACHMENTS("no.need.to.upload.attachments", "无需上传附件"),
    ORDER_AMOUNT_UPLOAD_RECEIPT_PHOTO("order.amount.upload.receipt.photo", "订单金额大于等于2000元需上传收货照片"),
    UPLOAD_ATTACHMENTS_BEFORE_SUBMITTING("upload.attachments.before.submitting", "请上传（不超过{{0}}个）附件再提交"),
    GOODS_NO_ACCEPTANCE_PHOTO("goods.no.acceptance.photo", "{{0}}未有验收图片关联，请重新确认"),
    GOODS_NO_ACCEPTANCE_ATTACHMENT("goods.no.acceptance.attachment", "{{0}}未有验收附件关联，请重新确认"),
    PLEASE_UPLOAD_PICTURES_FIRST("please.upload.pictures.first", "请先上传图片"),
    PLEASE_UPLOAD_ATTACHMENTS_FIRST("please.upload.attachments.first", "请先上传附件"),
    PLEASE_UPLOAD_PAYMENT_RECORDS_FIRST("please.upload.payment.records.first", "请先上传付款记录"),
    MAX_PAYMENT_RECORDS_UPLOAD("max.payment.records.upload", "最多上传{{0}}个付款记录"),
    ORDER_NOT_PENDING_RECEIPT_CANNOT_CANCEL_DELIVERY("order.not.pending.receipt.cannot.cancel.delivery", "订单非<待收货>状态，无法取消发货"),
    ORDER_HAS_RETURN_RECORD_CANNOT_CANCEL_DELIVERY("order.has.return.record.cannot.cancel.delivery", "订单已产生退货记录，无法取消发货"),
    BAR_CODE_HAS_INBOUND_RECORD_CANNOT_CANCEL_DELIVERY("bar.code.has.inbound.record.cannot.cancel.delivery", "{{0}}等二维码已产生入库记录，无法取消发货"),
    BAR_CODE_HAS_ACCEPT_CANNOT_CANCEL_DELIVERY("bar.code.has.accept.cannot.cancel.delivery", "{{0}}等二维码已收货，无法取消发货"),
    ORDER_RECEIVING_OR_CANCEL_DELIVERY_PROCESSING("order.receiving.or.cancel.delivery.processing", "订单收货或取消发货操作正在处理，请稍后再试"),
    




    UPLOAD_ATTACHMENT_VIDEO_LIMIT_EXCEEDED("upload.attachment.video.limit.exceeded", "上传验收视频附件数量超过{{0}}个"),

    UPLOAD_ATTACHMENTS_VIDEO_BEFORE_SUBMITTING("upload.attachments.video.before.submitting", "验收视频必须上传"),


    // 退货相关
    GOOD_RETURN_NOT_FOUND("return.order.not.found", "没有找到退货单信息"),
    RETURN_INFO_CANNOT_BE_EMPTY("return.info.cannot.be.empty", "退货商品不能为空"),
    USE_SCAN_CODE_RETURN_FUNCTION("use.scan.code.return.function", "一物一码订单请使用扫码退货功能"),
    RETURN_ALL_BOTTLE_OF_GOODS_ALREADY_RETURN("return.all.bottle.of.goods.already.return", "{{0}}商品下的气瓶已全部退货，该商品也需要全部退货，请检查"),
    ALL_BOTTLES_ALREADY_RETURNED_NO_NEED_TO_RETURN("all.bottles.already.returned.no.need.to.return", "{{0}}商品下的气瓶已经全部退货，无需再对气瓶进行退货"),
    SELECTED_NON_RETURNABLE_BOTTLES("selected.non.returnable.bottles", "{{0}}商品下选择了非可退货的气瓶，请重新选择"),
    RETURN_QUANTITY_GREATER_THAN_REFUNDABLE("return.quantity.greater.than.refundable", "{{0}}商品所填退货数量大于可退商品"),
    UNRETURNED_BOTTLES_EXIST("unreturned.bottles.exist", "{{0}}商品还存在未退货的气瓶，请检查"),
    JINAN_UNIVERSITY_FULL_RETURN_ONLY("jinan.university.full.return.only", "{{0}},暨南大学订单只支持整单退货"),
    MERCHANT_CANCELLED_CANNOT_RETURN("merchant.cancelled.cannot.return", "商家已注销，不能申请退货"),
    ADD_CYLINDER_INFO_FOR_RETURN("add.cylinder.info.for.return", "实验气体退货时，请选择添加对应的气瓶信息"),
    PENDING_APPROVAL_CANCEL_FIRST("pending.approval.cancel.first", "已选的气瓶存在待审批的入库单，请先取消后，再申请退货"),
    CANCEL_STORAGE_FIRST("cancel.storage.first", "已选的气瓶已经入库，请先撤销入库后，再申请退货"),
    NO_MATCHING_ORDER_FOR_RETURN("no.matching.order.for.return", "当前退货单没有匹配的订单！"),
    REVOCATION_FAILED_NO_RETURN_ORDER("revocation.failed.no.return.order", "撤销失败，查无退货单！"),
    CANNOT_RETURN_CURRENT_ORDER_STATUS("cannot.return.current.order.status", "无法退货，当前订单状态：{{0}}"),
    RETURN_REQUEST_FAILED_AMOUNT_ZERO("return.request.failed.amount.zero", "申请退货失败, 可退金额为0"),
    OFFLINE_ORDER_COMPLETED_CANNOT_RETURN("offline.order.completed.cannot.return", "线下单已完成无法操作退货"),
    FAILED_TO_OBTAIN_RETURN_DETAILS("failed.to.obtain.return.details", "获取退货明细退货数量失败，查无退货信息"),
    FAILED_TO_OBTAIN_RETURN_VERIFICATION("failed.to.obtain.return.verification", "获取退货校验结果失败"),
    ORDER_CONFIRMATION_PUSH_FAILED("order.confirmation.push.failed", "订单确认收货推送未成功，不能申请退货！"),
    RETURN_ITEMS_NOT_IN_ORDER("return.items.not.in.order", "退货单中存在没有在订单中没有的商品，请重新刷新后重新发起退货！"),
    RETURN_QUANTITY_EXCEEDS_REFUNDABLE("return.quantity.exceeds.refundable", "退货数目大于可退数目，请刷新后确认可退数目后重试"),
    NON_CANCELLED_RETURN_ORDER_EXISTS("non.cancelled.return.order.exists", "订单中存在非取消状态的退货单，请处理退货单后再发起退货"),
    MAX_100_RETURN_REQUESTS("max.100.return.requests", "目前一次最多仅能强制取消100个订单的退货申请"),
    UNFINISHED_RETURN_ORDER_EXISTS("unfinished.return.order.exists", "该订单存在未完成的退货单，请先完成退货后再提交入库"),
    RETURN_PENDING_GOODS("return.pending.goods", "还有以下商品未完成退货:{{0}}。请完成退货后再进行验收。"),
    INVALID_RETURN_ORDER_NUMBER("invalid.return.order.number", "无效的退货单号"),
    CANCEL_RETURN_FAILED_MISSING_ID("cancel.return.failed.missing.id", "撤销退货失败, 缺少部门/课题组id"),
    RETURN_FAILED_MISSING_DEPARTMENT_ID("return.failed.missing.department.id", "退货失败, 缺少部门/课题组id"),
    ORDER_UNFREEZE_FAILED_NOT_FULL_RETURN("order.unfreeze.failed.not.full.return", "订单{{0}}并非整单退货或取消订单导致的解冻失败，请联系客服！"),
    PRODUCTS_NOT_RETURNED_COMPLETE_INSPECTION("products.not.returned.complete.inspection", "还有以下商品未完成退货：{{0}}。请完成退货后再进行验收。"),
    CURRENT_ORDER_STATUS_NO_RETURN("current.order.status.no.return", "当前订单状态不能退货"),
    RETURN_FAILED_INVALID_ORDER_NUMBER("return.failed.invalid.order.number", "退货失败，无效的订单号：{{0}}"),
    RETURN_FAILED_INVALID_ORDER_STATUS("return.failed.invalid.order.status", "退货失败，订单状态非待收货/待结算的订单不能申请退货"),
    RETURN_FAILED_NON_PENDING_STATUS("return.failed.non.pending.status", "退货失败，订单状态非待收货的订单不能申请退货"),
    RETURN_FAILED_INVALID_STATUS_FOR_RETURN("return.failed.invalid.status.for.return", "退货失败，订单状态非结算中(待开票)/待收货/待结算的订单不能申请退货"),
    AUTO_RECEIPT_EXCEPTION_NO_INSTITUTION("auto.receipt.exception.no.institution", "退货单自动收货异常, 查无机构"),
    AUTO_RECEIPT_EXCEPTION_NO_ORDER("auto.receipt.exception.no.order", "退货单自动收货异常, 查无订单"),
    AGGREGATE_SUPPLIER_RETURN_FAILED("aggregate.supplier.return.failed", "聚合供应商退货总和失败。{{0}}"),
    RETURN_ORDER_NOT_FOUND("return.order.not.found", "没有找到与{{0}}匹配的退货单"),
    ORDER_RETURN_IN_PROGRESS_CANNOT_SELECT_CARD("order.return.in.progress.cannot.select.card", "订单号:{{0}}正在退货中, 请退货完成或撤销退货再选卡!"),
    AGREE_RETURN_GOODS_DELAY_ACCEPTANCE("agree.return.goods.delay.acceptance", "只有同意退货和退还货物状态才可延迟验收"),
    QUERY_DETAILS_FAILED_RETURN_ORDER_NOT_EXIST("query.details.failed.return.order.not.exist", "查询详情失败！该退货单不存在！"),
    TOTAL_RETURN_AMOUNT_GREATER_CANNOT_OPERATE("total.return.amount.greater.cannot.operate", "订单总退货金额大于订单金额，不能操作"),
    TOTAL_RETURN_AMOUNT_GREATER_CONTACT_SERVICE("total.return.amount.greater.contact.service", "订单总退货金额大于订单金额，不能操作，请联系客服"),
    RETURN_ORDER_STATUS_CHANGED_REFRESH("return.order.status.changed.refresh", "退货单状态已变更，请刷新再看！"),
    ORDER_NOT_SUPPLIER_CANCEL_STATUS("order.not.supplier.cancel.status", "订单不是供应商申请取消状态，不能同意取消！请刷新后重试！"),
    ORDER_NOT_CANCEL_REQUEST_STATUS("order.not.cancel.request.status", "订单不是申请取消状态，不能拒绝取消！请刷新重试"),
    ORDER_STATUS_ABNORMAL_CANNOT_CANCEL("order.status.abnormal.cannot.cancel", "订单状态异常，无法取消"),
    CONTACT_PURCHASER_TO_CANCEL("contact.purchaser.to.cancel", "请联系采购人到管理平台取消订单"),
    PURCHASER_CANCEL_OPERATION_NOT_SUPPORT_REFUSE("purchaser.cancel.operation.not.support.refuse", "采购人在本单位的化学品管控系统进行了订单取消操作，暂不支持拒绝取消此订单。"),
    NON_PURCHASER_CANNOT_CANCEL("non.purchaser.cannot.cancel", "非采购人本人不可取消订单"),
    CANNOT_CANCEL_OTHERS_ORDER("cannot.cancel.others.order", "不能取消他人订单"),
    CANNOT_AGREE_CANCEL_OTHERS_ORDER("cannot.agree.cancel.others.order", "不能同意取消他人订单"),
    CANNOT_REFUSE_CANCEL_OTHERS_ORDER("cannot.refuse.cancel.others.order", "不能拒绝取消他人订单"),
    SUPPLIER_APPLIED_CANCEL_ORDER("supplier.applied.cancel.order", "供应商已经申请取消订单。请刷新后重试"),
    HUANONG_CANCEL_ORDER_EXCEPTION("huanong.cancel.order.exception", "华农调用取消订单异常{{0}}"),
    CANCEL_FAILED_NO_ORDER("cancel.failed.no.order", "取消失败！查无订单"),
    ONLY_UNCONFIRMED_OR_UNSHIPPED_CAN_CANCEL("only.unconfirmed.or.unshipped.can.cancel", "只有未确认或者未发货的订单才能取消"),
    ONLY_PENDING_OR_REJECTED_RETURN_ORDER_CAN_EDIT("only.pending.or.rejected.return.order.can.edit", "仅支持编辑待审核和已拒绝状态的退货单"),



    // 经费相关
    CANNOT_CHANGE_CARD_FOR_DIFFERENT_SETTLEMENTS("cannot.change.card.for.different.settlements", "新旧单或集中结算和自结算不允许同时换卡, 请重新选卡!"),
    MULTIPLE_BUDGET_CARDS_FULL_RETURN_ONLY("multiple.budget.cards.full.return.only", "多选经费卡的订单，只能整单退货"),
    NO_SUPPORT_UNFREEZE_FIRST_LEVEL_CARD("no.support.unfreeze.first.level.card", "暂不支持一级经费卡重新冻结"),
    NO_THIRD_LEVEL_CARD_DATA("no.third.level.card.data", "没有三级经费卡数据"),
    ORDER_INCOMPLETE_NO_STORAGE("order.incomplete.no.storage", "该订单设置了平台运行经费但订单状态为未完成不可以提交入库"),
    FUNDING_CARD_NOT_FOUND("funding.card.not.found", "找不到经费卡:{{0}}"),
    MODIFY_FUNDING_CARD_FAILED("modify.funding.card.failed", "修改经费卡失败,缺失经费卡关键信息！"),
    SEARCH_FUND_CARD_FAILED("search.fund.card.failed", "查找课题组的经费卡失败：{{0}}"),
    FUND_CARD_NOT_EXIST("fund.card.not.exist", "经费卡{{0}}不存在，请联系管理员！"),
    FUND_UNFREEZE_FAILED("fund.unfreeze.failed", "经费解冻失败，订单退货时解冻金额必填！"),
    REFREEZE_SUPPORTS_FULL_RETURN("refreeze.supports.full.return", "重新解冻仅支持整单退货且经费解冻失败情况"),
    BATCH_RELEASE_FUNDS_1_TO_10("batch.release.funds.1.to.10", "批量释放经费处理条数为1-10条"),
    OBTAIN_FUNDING_CARD_FAILED_NO_PI_INFO("obtain.funding.card.failed.no.pi.info", "获取经费卡失败，无Pi管理员或采购人信息！"),
    OBTAIN_FUNDING_CARD_FAILED_INVALID_ORDER("obtain.funding.card.failed.invalid.order", "获取经费卡失败，订单信息无效！"),
    FUNDING_CARD_FAILURE_INVALID_TEAM("funding.card.failure.invalid.team", "获取经费卡失败，课题组无效！"),
    MULTI_ORDER_MULTI_CARD_NOT_SUPPORTED("multi.order.multi.card.not.supported", "不支持多订单选择多张经费卡，请重试！"),
    MODIFICATION_FAILED_CANNOT_CHANGE_FUNDING_TYPE("modification.failed.cannot.change.funding.type", "修改失败，以下订单不能修改为其他经费类型的经费卡。{{0}}"),
    MULTIPLE_FUNDS_CANNOT_BULK_MODIFY("multiple.funds.cannot.bulk.modify", "勾选的订单中含有绑定了多个经费的订单，不能批量修改经费卡。"),
    NO_PERMISSION_CHANGE_FUNDING_CARD("no.permission.change.funding.card", "您对订单{{0}}没有换经费卡的权限！不允许修改经费卡！"),
    DEDUCTION_FAILED_NO_FUNDING_CARD("deduction.failed.no.funding.card", "扣减失败，查无绑定经费卡"),
    PLEASE_ENTER_FUNDING_CARD_AMOUNT("please.enter.funding.card.amount", "请输入经费卡使用金额！"),
    FUNDING_CARD_INFO_NOT_EXIST_FOR_ORDER_ID("funding.card.info.not.exist.for.order.id", "订单id为{{0}}对应的经费卡信息不存在！"),
    CANNOT_VERIFY_FUNDING_CARD_LEADER_INFO("cannot.verify.funding.card.leader.info", "无法查询到课题组负责人，因此无法校验经费卡负责人信息，请完善对应信息后重试"),
    CARD_CHANGE_REASON_EMPTY("card.reason.empty", "换卡原因不能为空"),
    FINANCE_ORG_CARD_BATCH_MODIFY_UNSUPPORTED("finance.org.card.batch.modify.unsupported", "财务对接单位不支持批量修改经费卡"),


    // 出入库相关
    NO_APPLICATION_PERMISSION("no.application.permission", "当前账号没有申领权限"),
    PRODUCT_IN_OR_STORED("product.in.or.stored", "存在商品正在入库或已入库，请先撤销入库或完成出库申领。"),
    NO_VIEW_PERMISSION_IN_PURCHASER_CENTER("no.view.permission.in.purchaser.center", "用户在该单位不具有采购人中心出入库查看权限"),
    NON_SETTLEMENT_ORDER_NO_STORAGE("non.settlement.order.no.storage", "非待结算状态订单不可以提交入库"),
    NON_COMPLETED_ORDER_NO_STORAGE("non.completed.order.no.storage", "非完成状态订单不可以提交入库"),
    NO_CROSS_GROUP_STORAGE("no.cross.group.storage", "不能跨课题组提交入库, 请检查订单信息"),
    VOID_RECEIPT_FAIL("void.receipt.fail", "作废入库单失败，入库单号:{{0}}"),
    PRODUCT_CODE_MISMATCH("product.code.mismatch", "入参商品货号与已存在的入库单关联的商品货号不一致,入参商品货号:{{0}}"),
    STORAGE_SERVICE_SAVE_FAIL("storage.service.save.fail", "入库单服务保存失败"),
    PRODUCT_NO_WAREHOUSE("product.no.warehouse", "存在商品未选择库房:{{0}}"),
    WAREHOUSE_SERVICE_SAVE_FAIL("warehouse.service.save.fail", "库房服务保存失败"),
    THRESHOLD_QUERY_NO_WAREHOUSE("threshold.query.no.warehouse", "库房阈值查询时发现商品没有选择库房"),
    USER_NO_GROUP_BINDING("user.no.group.binding", "当前用户未绑定到单位内的课题组，无法查看课题组入库单"),
    USER_NO_GROUP_BINDING_QUANTITY("user.no.group.binding.quantity", "当前用户未绑定到单位内的课题组，无法查看课题组入库单数量"),
    RECEIPT_NOT_FOUND("receipt.not.found", "找不到入库单:{{0}}"),
    RECEIPT_ID_NOT_FOUND("receipt.id.not.found", "找不到入库单Id对应的入库单，入库单Id：{{0}}"),
    ORDER_DETAILS_NOT_FOUND_FOR_ITEM("order.details.not.found.for.item", "找不到入库商品货号对应的订单详情, 订单号为：{{0}}"),
    UNABLE_TO_MATCH_PRODUCT_WITH_ITEM_OR_ORDER_DETAILS("unable.to.match.product.with.item.or.order.details", "无法正确匹配商品与货号或订单详情信息，若您的单据出现这种情况又急需入库，请联系客服提供单号解决"),
    USER_LACKS_PERMISSION_FOR_WAREHOUSE_VIEW("user.lacks.permission.for.warehouse.view", "用户在该单位不具有采购人中心出入库查看权限，或此入库单包含您不具有权限查看的订单商品"),
    USER_LACKS_PERMISSION_TO_SUBMIT_INVENTORY("user.lacks.permission.to.submit.inventory", "用户在该部门不具有采购人中心提交入库权限"),
    UNABLE_TO_OBTAIN_INVENTORY_STATUS_DISTRIBUTION("unable.to.obtain.inventory.status.distribution", "获取不到当前用户的入库申请单入库状态数量分布"),
    UNABLE_TO_OBTAIN_APPROVAL_STATUS_DISTRIBUTION("unable.to.obtain.approval.status.distribution", "获取不到当前用户的入库申请单审批状态数量分布"),
    UNABLE_TO_OBTAIN_INVENTORY_APPLICATION_DISTRIBUTION("unable.to.obtain.inventory.application.distribution", "获取不到当前用户的入库申请单数量分布"),
    ORDER_NOT_PENDING_INVENTORY_STATUS("order.not.pending.inventory.status", "该订单入库状态不是待入库状态"),
    ORDER_ALREADY_HAS_WAREHOUSE_APPLICATION("order.already.has.warehouse.application", "该订单已有对应入库申请单"),
    PLEASE_SELECT_WAREHOUSE("please.select.warehouse", "请选择库房"),
    NON_PUSH_STORAGE_FAILURE_STATUS("non.push.storage.failure.status", "非推送入库失败状态，不能重新推送入库状态"),
    QUERY_STORAGE_LIST_FAILED_NO_ORDERS("query.storage.list.failed.no.orders", "查询存储仓库列表失败，查无订单"),
    ORDER_STATUS_UPDATE_FAILED_NOT_EXIST("order.status.update.failed.not.exist", "订单出入库状态更新失败！订单不存在！"),
    INBOUND_ORDER_DETAILS_NOT_FOUND("inbound.order.details.not.found", "找不到入库单商品对应的订单详情信息,商品code：{{0}}, 订单详情id：{{1}}"),
    OUTBOUND_ORDER_DETAILS_NOT_FOUND("outbound.order.details.not.found", "找不到出库单详情:{{0}}"),
    OUTBOUND_ORDER_ITEM_DETAILS_NOT_FOUND("outbound.order.item.details.not.found", "找不到出库单商品对应的订单详情信息,商品code：{{0}}, 订单详情id：{{1}}"),
    RETURN_PRODUCT_INVENTORY_FAILED("return.product.inventory.failed", "返回商品库存失败"),

    // 找不到xx相关
    NO_PURCHASE_ORDER_WITH_ID("no.purchase.order.with.id", "不存在采购单id为{{0}}的采购单"),
    CANNOT_FIND_BIDDING_TICKET("cannot.find.bidding.ticket", "找不到对应竞价单{{0}}"),
    NO_PRODUCT_DETAILS_FOR_ORDER("no.product.details.for.order", "当前订单没有商品详情！"),
    ORDER_ID_NOT_FOUND("order.id.not.found", "找不到传入订单id对应的订单"),
    ORDER_LOG_NO_RECORD("order.log.no.record", "查询订单操作日志无此订单"),
    NO_MATCHING_ORDERS_FOUND("no.matching.orders.found", "没有查到匹配的订单！"),
    ORDER_DOES_NOT_EXIST("order.does.not.exist", "订单不存在!"),
    PURCHASER_INFO_NOT_FOUND("purchaser.info.not.found", "查找不到订单采购人信息！"),
    PRODUCT_CATEGORY_INFO_NOT_FOUND("product.category.info.not.found", "找不到当前商品分类信息：{{0}}"),
    PRIMARY_CATEGORY_INFO_NOT_FOUND("primary.category.info.not.found", "找不到商品的一级分类信息，当前分类：{{0}}"),
    APPLICATION_FORM_STATUS_NOT_FOUND("application.form.status.not.found", "查不到各状态的申领单数量"),
    APPLICATION_FORM_ID_NOT_FOUND("application.form.id.not.found", "查找不到对应Id的申领单:{{0}}"),
    USER_INFO_NOT_FOUND("user.info.not.found", "找不到当前用户信息，请重新登录再试"),
    USER_INSTITUTION_ID_NOT_FOUND("user.institution.id.not.found", "找不到当前用户机构Id，请重新登录再试"),
    CATEGORY_ID_NOT_FOUND("category.id.not.found", "在所给的分类列表中查找不到分类Id对应的分类,分类列表：{{0}};分类Id：{{1}}"),
    PRODUCT_CLASSIFICATION_DETAILS_NOT_FOUND("product.classification.details.not.found", "找不到商品的分类详情"),
    CURRENT_UNIT_NOT_FOUND("current.unit.not.found", "找不到当前单位：{{0}}"),
    ORDER_INFO_NOT_FOUND_FOR_ORDER_ID("order.info.not.found.for.order.id", "找不到订单Id对应的订单信息:{{0}}"),
    ORDER_INFO_NOT_FOUND_FOR_ORDER_NO("order.info.not.found.for.order.no", "找不到订单信息:{{0}}"),
    DEPARTMENT_NOT_FOUND_FOR_ORDER("department.not.found.for.order", "找不到订单对应部门，订单号：{{0}}"),
    ORDER_DETAILS_NOT_FOUND_FOR_ORDER_ID("order.details.not.found.for.order.id", "找不到订单详情:{{0}}"),
    DELIVERY_INFO_NOT_FOUND("delivery.info.not.found", "查询不到订单的配送信息"),
    ORDER_ADDRESS_NOT_FOUND("order.address.not.found", "无法找到订单地址信息"),
    NO_CARD_FOUND("no.card.found", "查无此卡:{{0}}"),
    NO_DATA_FOUND_FOR_ORDER_ID("no.data.found.for.order.id", "没找到订单id为{{0}}的数据"),
    ORDER_DETAILS_NOT_FOUND("order.details.not.found", "找不到订单详情:{{0}}"),

    // 其他
    FAILED_TO_OBTAIN_CREATION_TIME("failed.to.obtain.creation.time", "获取单据{{0}}创建时间失败!"),
    PLEASE_INPUT_PASSWORD("please.input.password", "请输入密码！"),
    FAILED_TO_OBTAIN_SUPPLIER_QUALIFICATION("failed.to.obtain.supplier.qualification", "获取供应商id为{{0}}的供应商资质失败，请联系客服"),
    QUERY_BATCH_FAILED_NO_PERMISSION("query.batch.failed.no.permission", "查询订单明细商品的批次失败, 无权限!"),
    NO_STATISTICAL_MANAGEMENT_PERMISSION("no.statistical.management.permission", "没有统计管理权限~！"),
    UNABLE_TO_OBTAIN_LOGIN_INFO("unable.to.obtain.login.info", "获取不到登录信息~！"),
    INVOICE_USER_NOT_LOGGED_IN("invoice.user.not.logged.in", "获取发票时，用户未登录"),
    CANNOT_OPERATE_OTHERS_ORDERS("cannot.operate.others.orders", "不能操作他人订单！"),
    OFFLINE_ORDER_MANAGEMENT_SYSU("offline.order.management.sysu", "中山大学与中山大学·深圳的线下订单管理，请从右上角进入学校管理平台进行相应操作。"),
    SYSU_USERS_CANNOT_MODIFY_ADDRESS("sysu.users.cannot.modify.address", "中山大学用户暂不开放修改地址业务"),
    PROXY_DELIVERY_CANNOT_MODIFY_ADDRESS("proxy.delivery.cannot.modify.address", "代配送订单，不可修改地址"),
    MODIFICATION_FAILED_STATUS_CHANGED("modification.failed.status.changed", "修改失败，订单状态已变更，请刷新查看"),
    MODIFICATION_FAILED_SHIPPING_COST_CHANGED("modification.failed.shipping.cost.changed", "修改失败，订单运费发生了改变，建议您联系商家处理。"),
    MODIFY_ORDER_ADDRESS_FAILED("modify.order.address.failed", "修改订单地址失败，订单id：{{0}},{{1}}"),
    CHECK_ORDER_STATUS_DAYS_PASSED("check.order.status.days.passed", "判断订单某个状态后经过的z天数是否在传入天数内的方法，订单状态:{{0}}, 不是传入的状态:{{1}}"),
    ONLY_SPECIFIC_STATUS_CAN_RECEIVE_SHIPPING_REMINDERS("only.specific.status.can.receive.shipping.reminders", "只有待确认、待发货订单才可发货提醒！"),
    ONLY_WAITING_CONFIRM_STATUS_CAN_RECEIVE_CONFIRM_REMINDERS("only.waiting.confirm.status.can.receive.confirm.reminders", "只有待确认订单才可确认订单提醒！"),
    ADDRESS_MODIFIED_CONTACT_MERCHANT("address.modified.contact.merchant", "您已修改过收货地址，如需再次修改，建议请联系商家。订单号：{{0}}"),
    ORDER_ID_REQUIRED_FOR_QUERY("order.id.required.for.query", "若没有传入部门，需要传入订单id让后台查询"),
    ORDER_NOT_CANCELLABLE("order.not.cancellable", "订单不是供应商申请取消状态，不能同意取消！请稍后刷新重试！"),
    ORDER_ID_NOT_EXIST_FOR_ADDRESS("order.id.not.exist.for.address", "需要修改地址的此订单不存在，传入的订单id为：{{0}}"),
    NO_HUANONG_USER_IN_RUIJING("no.huanong.user.in.ruijing", "在锐竞系统中找不到对应的华农用户"),
    NO_ORDER_INFO_IN_RUIJING("no.order.info.in.ruijing", "在锐竞系统中找不到对应订单信息"),
    CANCELLED_ORDER_NOT_EXIST("cancelled.order.not.exist", "当前取消的订单不存在，请刷新页面后重试"),
    CANCEL_LOG_NOT_FOUND("cancel.log.not.found", "未找到订单申请取消日志！"),
    MODIFY_ORDER_DATABASE_ADDRESS_FAILED("modify.order.database.address.failed", "修改订单数据库地址，日志库失败"),
    USER_NOT_BOUND_TO_GROUP("user.not.bound.to.group", "当前用户未绑定到单位内的课题组，无法查看课题组申领单"),
    USER_NOT_BOUND_TO_GROUP_FAILED("user.not.bound.to.group.failed", "当前用户未绑定到单位内的课题组，获取课题组申领单数量失败"),
    FAILED_TO_OBTAIN_PRODUCT_CATEGORY_INFO("failed.to.obtain.product.category.info", "获取商品分类信息失败，请稍后重试"),
    ORDER_STATUS_NOT_SYNCHRONIZED("order.status.not.synchronized", "订单状态未同步，请先重新推送再操作！"),
    ORDER_NO_CARD_BINDING("order.no.card.binding", "{{0}}没有绑卡数据"),
    PUSH_ORDER_PAYMENT_FAIL_NO_CARD("push.order.payment.fail.no.card", "推送订单支付信息失败！订单无绑卡信息"),
    UNABLE_OBTAIN_COST_CLASSIFICATION("unable.obtain.cost.classification", "无法获取到中肿订单{{0}}的费用分类"),
    SEARCH_DEPARTMENT_INFO_FAIL("search.department.info.fail", "查找部门信息失败！"),
    USER_NO_DIRECT_ACCESS_PORTAL("user.no.direct.access.portal", "当前用户无法直接跳转到采购门户，请从锐竞商城首页https://www.rjmart.cn/重新登录后，再执行后续操作"),
    UNABLE_TO_QUERY_DISCOUNTED_PRICE("unable.to.query.discounted.price", "无法正确查询到商品优惠价格，请联系客服"),
    UNSUPPORTED_ORDER_TYPE("unsupported.order.type", "暂不支持的订单类型：{{0}}"),
    DEPARTMENT_INFO_NOT_FOUND_FOR_ORDER("department.info.not.found.for.order", "订单有对应的部门，但找不到该部门信息，部门：{{0}}"),
    ORDER_TYPE_ERROR("order.type.error", "订单类型错误：{{0}}"),
    ITEM_AND_ORDER_DETAILS_CANNOT_MATCH_PRODUCT("item.and.order.details.cannot.match.product", "货号与订单详情id均无法正常匹配商品。若出现此情况可联系客服并提供单号。"),
    ORDER_ID_PRODUCT_NOT_FOUND("order.id.product.not.found", "找不到订单Id对应的商品信息，订单Id：{{0}}"),
    OPERATION_FAILED_UNAUTHORIZED("operation.failed.unauthorized", "操作失败, 无权操作"),
    BARCODE_BATCH_INFO_FAILED("barcode.batch.info.failed", "获取订单条形码批次总计信息失败, 无权限!"),
    NOT_DROPSHIPPING_EXPORT_ITEM("not.dropshipping.export.item", "不是代配送导出条目，请传入正确的条目id"),
    SORTING_NOT_ALLOWED_CURRENT_STATUS("sorting.not.allowed.current.status", "当前状态不可完成分拣"),
    DELIVERY_NOT_ALLOWED_CURRENT_STATUS("delivery.not.allowed.current.status", "当前状态不可完成配送"),
    ADD_SORTING_IMAGES_NOT_ALLOWED("add.sorting.images.not.allowed", "当前状态不可追加分拣图片"),
    ADD_DELIVERY_IMAGES_NOT_ALLOWED("add.delivery.images.not.allowed", "当前状态不可追加配送图片"),
    NO_PERMISSION_VIEW_MODULE("no.permission.view.module", "当前登录用户没有查看此模块权限，请配置后再查看"),
    ORDER_NOT_FOUND_CHECK_NUMBER("order.not.found.check.number", "找不到对应的订单，请检查订单号是否正确或是否启用了代配送"),
    ORDER_NOT_FOUND_CHECK_ITEM_NUMBER("order.not.found.check.item.number", "找不到对应的订单，请检查货号是否正确或是否启用了代配送"),
    ORDER_AMOUNT_UPLOAD_PICTURE("order.amount.upload.picture", "此订单金额大于等于1万元，请上传图片再提交"),
    STATUS_CHANGED_REFRESH_RETRY("status.changed.refresh.retry", "状态已变更，订单号:{{0}}，请刷新后重试。"),
    DELETE_CLICK_HISTORY_USER_ID_REQUIRED("delete.click.history.user.id.required", "删除点击历史需要传入用户id"),
    USER_NOT_LOGGED_IN_TIMEOUT_ORDERS("user.not.logged.in.timeout.orders", "用户未登录无法获取超时结算订单情况，请登录后重试"),
    CLOSURE_REASON_REQUIRED("closure.reason.required", "需要填写关闭原因"),
    QUERY_FAILED_NO_ORDER_IN_ID("query.failed.no.order.in.id", "查询失败，id查无订单!"),
    PRECURSOR_DRUG_ORDER_ADDITIONAL_UPLOAD("precursor.drug.order.additional.upload", "只有易制毒类订单可追加上传一次备案图片"),
    FILING_CONFIRMED_NO_NEED_AGAIN("filing.confirmed.no.need.again", "已确认备案，无需再次备案"),
    SOME_ORDERS_NO_FILING_REQUIRED("some.orders.no.filing.required", "有些订单订单无需备案，请重新选择"),
    NO_PERMISSION_TO_FILE_CONTACT_SUPPORT("no.permission.to.file.contact.support", "没有权限备案，请联系客服处理"),
    NO_PERMISSION_ADD_IMAGE_CONTACT_SUPPORT("no.permission.add.image.contact.support", "没有权限添加备案图片，请联系客服处理"),
    ORDER_NOT_FILED_PLEASE_FILE_FIRST("order.not.filed.please.file.first", "订单还未备案，请先备案"),
    BACKEND_CANNOT_FIND_ORDER_ID("backend.cannot.find.order.id", "后台搜索找不到此订单id，请检查此订单是否属于您当前所在的具有权限的课题组。您当前所在课题组是：{{0}}"),
    NO_PURCHASED_ITEMS_IN_ORDER("no.purchased.items.in.order", "当前订单无采购商品，请检查订单内商品数或联系客服"),
    CANNOT_FIND_USER_RESEARCH_GROUP("cannot.find.user.research.group", "找不到当前用户所属课题组"),
    CANNOT_FIND_SUPPLIER_FOR_ORDER("cannot.find.supplier.for.order", "找不到此单对应的供应商，orderDetailId={{0}}"),
    CANNOT_FIND_ORDER_PLEASE_RETRY("cannot.find.order.please.retry", "找不到此订单，请重试"),
    CANNOT_FIND_SUPPLIER_FOR_DETAILS("cannot.find.supplier.for.details", "无法找到订单详情信息对应供应商，请重试或联系客服"),
    NO_PRODUCT_INFO_FOR_ORDER("no.product.info.for.order", "未找到此订单对应商品的信息，请刷新重试或联系客服"),
    NO_SOURCE_TYPE_FOR_ORDER("no.source.type.for.order", "未找到此订单的来源类型，请检查订单信息重试或联系客服"),
    INVOICE_INFO_INTERFACE_FAILED("invoice.info.interface.failed", "获取发票信息接口未能获取订单id或订单号入参"),
    CANNOT_FIND_SUPPLIER_INFO("cannot.find.supplier.info", "获取订单详情时没能找到供应商信息"),
    ABNORMAL_ORDER_DATA("abnormal.order.data", "订单数据异常"),
    ORDER_CANNOT_FIND_UNIT("order.cannot.find.unit", "订单未找到所属单位"),
    ENTER_ORDER_NUMBER_OR_ID("enter.order.number.or.id", "请输入订单号或订单id查询详情"),
    ORDER_NO_RECORDED_NUMBER("order.no.recorded.number", "此订单未记录订单号，请联系客服"),
    ORDER_LOG_INTERFACE_ERROR("order.log.interface.error", "获取订单操作日志记录接口未能获取订单id或订单号入参"),
    DO_NOT_PROCESS_PENDING_ORDERS("do.not.process.pending.orders", "不处理该单位的待结算订单完成操作"),
    SUPPLIER_NOT_REGISTERED_FINANCE("supplier.not.registered.finance", "{{0}}供应商未在财务注册"),
    ORDER_SUBMITTED_SETTLEMENT_NO_UNFREEZE("order.submitted.settlement.no.unfreeze", "当前订单已经提交结算，不可发起解冻！"),
    UPDATE_ORDER_STATUS_FAILED("update.order.status.failed", "更新锐竞订单状态失败！状态不一致！status：{{0}}"),
    ONE_INVOICE_AT_A_TIME("one.invoice.at.a.time", "一次只能保存一个订单的发票！"),
    NO_PERMISSION_MODIFY_INVOICE("no.permission.modify.invoice", "当前用户没有修改发票权限"),
    ONLINE_ORDER_INCOMPLETE("online.order.incomplete", "线上单处于非完成状态，不能修改发票"),
    OFFLINE_ORDER_INCOMPLETE_OR_PENDING("offline.order.incomplete.or.pending", "线下单处于非完成状态或待收货状态，不能修改发票"),
    ADD_TO_WALLET_QUEUE_FAILED("add.to.wallet.queue.failed", "加入待扣费钱包订单队列接口失败！查无订单！"),
    NO_ORDER_INFO_FOUND("no.order.info.found", "失败！查无订单信息"),
    PUSH_FAILED_NO_ORDER_INFO("push.failed.no.order.info", "推送失败！根据采购单id查无订单信息"),
    PUSH_PENDING_SETTLEMENT_FAILED("push.pending.settlement.failed", "推送待结算单失败！无效订单"),
    PUSH_PENDING_SETTLEMENT_NO_ORDER("push.pending.settlement.no.order", "推送待结算单失败！无订单号"),
    ORDER_DEPARTMENT_NOT_FOUND("order.department.not.found", "查询不到当前订单对应的部门，订单异常，订单号：{{0}}，请联系客服"),
    QUERY_PURCHASE_REQUISITION_FAILED("query.purchase.requisition.failed", "查询采购申请单失败，申请单idList={{0}}"),
    DEPARTMENT_MANAGER_NOT_ENTERED("department.manager.not.entered", "此部门管理者未录入，请录入后重试，部门：{{0}}，订单号：{{1}}"),
    REGULATED_PRODUCT_DELIVERY_FAILED("regulated.product.delivery.failed", "管制品发货通知失败！查无管制品用户！"),
    REGULATED_PRODUCT_DELIVERY_NO_ORDER("regulated.product.delivery.no.order", "管制品发货通知失败！查无订单！"),
    PRINT_DATA_INVALID_ORDER("print.data.invalid.order", "获取打印数据失败，无效订单！"),
    NO_UNFREEZE_PERMISSION_FOR_ORDER("no.unfreeze.permission.for.order", "您所在的课题组:{{0}}对订单:{{1}}无解冻权限"),
    INSERT_ADDRESS_TABLE_FAILED("insert.address.table.failed", "插入地址表失败{{0}}"),
    NO_DATA_TO_EXPORT("no.data.to.export", "暂无可导出的数据~！"),
    UNABLE_TO_FIND_PRODUCT_INFO("unable.to.find.product.info", "查不到商品信息"),
    UNABLE_TO_RETRIEVE_CARD_REPLACEMENT("unable.to.retrieve.card.replacement", "查询不到换卡信息！"),
    NO_PERMISSION_TO_EVALUATE_ORDER("no.permission.to.evaluate.order", "没权限评价此订单~！"),
    UPDATE_ORDER_FAILED_INVALID_QUERY("update.order.failed.invalid.query", "更新订单失败， 查询订单信息无效"),
    UPDATE_ORDER_FAILED_INVALID_ORDER_NUMBER("update.order.failed.invalid.order.number", "更新订单失败，订单号查询订单信息无效"),
    PUSH_ORDER_STATUS_FAILED_NO_THIRD_PARTY("push.order.status.failed.no.third.party", "推送订单状态失败，查无第三方平台订单"),
    PUSH_ORDER_STATUS_FAILED_NO_ORDER_INFO("push.order.status.failed.no.order.info", "推送订单状态失败，查无订单信息"),
    PUSH_ORDER_STATUS_FAILED_NO_PURCHASER("push.order.status.failed.no.purchaser", "推送订单状态失败，查无订单采购人"),
    INITIATE_SETTLEMENT_FAILED_NO_OBJECT("initiate.settlement.failed.no.object", "发起结算失败！无结算对象"),
    GENERATE_APPROVAL_LOG_FAILED_NO_ORDER("generate.approval.log.failed.no.order", "生成订单审批日志失败，orderId查无订单:{{0}}"),
    ORDER_REJECTION_SETTLEMENT_FAILED_INVALID_ID("order.rejection.settlement.failed.invalid.id", "订单驳回结算失败！无效的订单id"),
    DOCUMENT_TO_SAVE_NOT_EXIST("document.to.save.not.exist", "需要保存的单据不存在"),
    DOCUMENT_OLD_OR_FUNCTION_NOT_OPEN("document.old.or.function.not.open", "该单据为旧单或该单位没有开放此功能！"),
    AGGREGATE_AD_ORDER_INFO_FAILED("aggregate.ad.order.info.failed", "聚合广告投放订单信息失败"),
    ORDER_NOT_PENDING_SHIPMENT("order.not.pending.shipment", "当前订单非待发货状态！"),
    NO_ORDER_ID_LIST_PROVIDED("no.order.id.list.provided", "没有传入需要查询的订单Id列表！"),
    ORDER_FROZEN_CANNOT_SELECT_CARD("order.frozen.cannot.select.card", "订单号:{{0}}已在采购冻结, 无法选卡"),
    INCORRECT_SETTLEMENT_DATA_CONTACT_SUPPORT("incorrect.settlement.data.contact.support", "存在未正确生成结算数据的订单，请联系客服！"),
    ORDER_STATUS_NOT_ALLOW_FREEZE("order.status.not.allow.freeze", "当前订单状态非未冻结/冻结失败/解冻成功/换卡失败，不可进行冻结"),
    ORDER_NOT_FREEZING_FAILURE_CANNOT_REFREEZE("order.not.freezing.failure.cannot.refreeze", "当前订单非冻结失败状态，不能重新冻结"),
    ORDER_CHANGING_CARD_CANNOT_CHANGE_CARD_AGAIN("order.changing.card.cannot.change.card.again", "订单{{0}}正在换卡中，请勿再次发起换卡"),
    DEDUCTION_FAILED_NO_TEAM_LEADER("deduction.failed.no.team.leader", "扣减失败，查无课题组负责人"),
    CARD_REPLACEMENT_FAILED_NO_ORDER_ITEMS("card.replacement.failed.no.order.items", "换卡失败, 查无订单商品"),
    CARD_REPLACEMENT_FAILED_NO_ORDER_INFO("card.replacement.failed.no.order.info", "换卡失败！查无订单信息！"),
    CARD_AMOUNT_NOT_EQUAL_ORDER_AMOUNT("card.amount.not.equal.order.amount", "换卡所填总额与订单总金额不相等！请重新填写每张卡的使用金额！"),
    OPERATION_FAILED_NO_ORDER_FOUND("operation.failed.no.order.found", "操作失败，orderId：{{0}}查无订单"),
    OPERATION_FAILED_INVALID_USER("operation.failed.invalid.user", "操作失败，无效的用户!"),
    FAILED_TO_OBTAIN_USER_INFO("failed.to.obtain.user.info", "获取用户id为{{0}}的用户信息失败！"),
    ORDER_STATUS_NOT_MATCH_CANNOT_CHANGE_CARD("order.status.not.match.cannot.change.card", "订单{{0}}非{{1}}状态,不能进行换卡"),
    INPUT_AMOUNT_EXCEEDS_ORDER_AMOUNT("input.amount.exceeds.order.amount", "输入金额不可大于订单总金额，请重新输入"),
    FAILED_TO_UPDATE_ORDER_LOG("failed.to.update.order.log", "更新订单日志失败，请重试或联系客服"),
    FAILED_TO_UPDATE_ORDER_STATUS("failed.to.update.order.status", "更新订单状态失败，请检查订单是否存在，刷新后重试"),
    DEPARTMENT_CANNOT_VIEW_PURCHASER_INFO("department.cannot.view.purchaser.info", "该部门无法查看采购人中心信息，请到子系统查看和操作"),
    PLEASE_PROVIDE_START_AND_END_TIMES("please.provide.start.and.end.times", "请传入起止两个时间"),
    NO_PERMISSION_ONLY_MERCHANT_USER("no.permission.only.merchant.user", "无权限操作，商家用户才可操作！当前用户为：{{0}}"),
    NO_PERMISSION_VIEW("no.permission.view", "无权限查看！"),
    ACCOUNT_EXCEPTION_NO_USER_FOUND("account.exception.no.user.found", "账号异常, 查无用户:{{0}}"),
    SPLITTING_FAILED_NO_PERMISSION("splitting.failed.no.permission", "拆分失败, 无权限!"),
    HAZARDOUS_CHEMICALS_NO_SPLIT("hazardous.chemicals.no.split", "危化品不支持拆单, 请重试"),
    SPLIT_ITEMS_INCORRECT("split.items.incorrect", "拆单商品数有误, 商品id:{{0}} 购买总数为{{1}}; 当前拆分后总数为:{{2}}"),
    CANNOT_DELETE_OTHER_USER_INFO("cannot.delete.other.user.info", "不可删除其他用户导出的信息，请登录为该用户后重试"),
    UPDATE_PRODUCT_NAME_FAILED("update.product.name.failed", "更新商品名失败！无效的订单商品！"),
    MODIFICATION_FAILED_NOT_PENDING("modification.failed.not.pending", "修改失败！订单不是待确认状态！"),
    MODIFICATION_FAILED_NON_LARGE_UNIT("modification.failed.non.large.unit", "修改失败！非中大单位不可修改商品为中文名！"),
    ADDRESS_REGION_MISMATCH("address.region.mismatch", "收货地址省市区与地址标签不匹配，请重新编辑收货地址。"),
    NO_INVOICE_ATTACHMENT_VIEW_PERMISSION("no.invoice.attachment.view.permission", "无发票附件查看权限"),

    ;

    /**
     * 模板code
     */
    private final String templateCode;

    /**
     * 模板模板
     */
    private final String templateContent;

    /**
     * 获取完整模板Code
     */
    @Override
    public String getTemplateCode() {
        return templateCode;
    }

    /**
     * 获取模板Code前缀
     */
    @Override
    public String getTemplateCodePrefix() {
        return CommonConstant.LOCALE_PREFIX_ORDER_STORE;
    }

    @Override
    public String getTemplateContent() {
        return templateContent;
    }

    ExecptionMessageEnum(String templateCode, String templateContent) {
        this.templateCode = templateCode;
        this.templateContent = templateContent;
    }
}
