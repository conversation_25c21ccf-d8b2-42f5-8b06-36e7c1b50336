package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.vo.inwarehouse.ApprovalProgressVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领单详情")
public class ClaimDetailVO implements Serializable {

    private static final long serialVersionUID = 5214616601992785772L;

    @RpcModelProperty(value = "申领单Id")
    private Integer claimId;

    @RpcModelProperty(value = "申领单号")
    private String claimNo;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty(value = "机构Id")
    private Integer orgId;

    @RpcModelProperty(value = "申领人guid")
    private String claimUserGuid;

    @RpcModelProperty(value = "申领人名字")
    private String claimUserName;

    @RpcModelProperty(value = "创建时间")
    private Long createTime;

    @RpcModelProperty(value = "更新时间")
    private Long updateTime;

    @RpcModelProperty(value = "库房名称")
    private String roomName;

    @RpcModelProperty(value = "库房Id")
    private Integer roomId;

    @RpcModelProperty(value = "部门名称")
    private String departmentName;

    @RpcModelProperty(value = "部门Id")
    private Integer departmentId;

    @RpcModelProperty(value = "申领状态（0未完成，1已完成）")
    private Integer status;

    @RpcModelProperty(value = "申领状态名称")
    private String statusName;

    @RpcModelProperty(value = "审批状态（0审批中，1审批通过，2审批驳回）")
    private Integer approvalStatus;

    @RpcModelProperty(value = "审批状态名称")
    private String approvalStatusName;

    @RpcModelProperty(value = "申领单类型")
    private Integer claimType;

    @RpcModelProperty(value = "申领单类型名称")
    private String claimTypeName;
    
    @RpcModelProperty(value = "备注")
    private String remark;

    @RpcModelProperty(value = "申领单的商品列表")
    private List<WarehouseProductInfoVO> claimProductList;

    @RpcModelProperty(value = "申领单的操作日志列表")
    private List<ClaimOperationLogVO> claimOperationLogList;

    @RpcModelProperty(value = "申领单的审批日志列表")
    private List<ApprovalProgressVO> claimApprovalLogList;

    @RpcModelProperty(value = "申领人信息")
    private ClaimUserVO claimUserVO;

    public ClaimUserVO getClaimUserVO() {
        return claimUserVO;
    }

    public ClaimDetailVO setClaimUserVO(ClaimUserVO claimUserVO) {
        this.claimUserVO = claimUserVO;
        return this;
    }

    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getClaimUserGuid() {
        return claimUserGuid;
    }

    public void setClaimUserGuid(String claimUserGuid) {
        this.claimUserGuid = claimUserGuid;
    }

    public String getClaimUserName() {
        return claimUserName;
    }

    public void setClaimUserName(String claimUserName) {
        this.claimUserName = claimUserName;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Integer getClaimType() {
        return claimType;
    }

    public void setClaimType(Integer claimType) {
        this.claimType = claimType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<WarehouseProductInfoVO> getClaimProductList() {
        return claimProductList;
    }

    public void setClaimProductList(List<WarehouseProductInfoVO> claimProductList) {
        this.claimProductList = claimProductList;
    }

    public List<ApprovalProgressVO> getClaimApprovalLogList() {
        return claimApprovalLogList;
    }

    public void setClaimApprovalLogList(List<ApprovalProgressVO> claimApprovalLogList) {
        this.claimApprovalLogList = claimApprovalLogList;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getApprovalStatusName() {
        return approvalStatusName;
    }

    public void setApprovalStatusName(String approvalStatusName) {
        this.approvalStatusName = approvalStatusName;
    }

    public String getClaimTypeName() {
        return claimTypeName;
    }

    public void setClaimTypeName(String claimTypeName) {
        this.claimTypeName = claimTypeName;
    }

    public List<ClaimOperationLogVO> getClaimOperationLogList() {
        return claimOperationLogList;
    }

    public void setClaimOperationLogList(List<ClaimOperationLogVO> claimOperationLogList) {
        this.claimOperationLogList = claimOperationLogList;
    }

    @Override
    public String toString() {
        return "ClaimDetailVO{" +
                "claimId=" + claimId +
                ", claimNo='" + claimNo + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", orgId=" + orgId +
                ", claimUserGuid='" + claimUserGuid + '\'' +
                ", claimUserName='" + claimUserName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", roomName='" + roomName + '\'' +
                ", roomId=" + roomId +
                ", departmentName='" + departmentName + '\'' +
                ", departmentId=" + departmentId +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", approvalStatus=" + approvalStatus +
                ", approvalStatusName='" + approvalStatusName + '\'' +
                ", claimType=" + claimType +
                ", claimTypeName='" + claimTypeName + '\'' +
                ", remark='" + remark + '\'' +
                ", claimProductList=" + claimProductList +
                ", claimOperationLogList=" + claimOperationLogList +
                ", claimApprovalLogList=" + claimApprovalLogList +
                ", claimUserVO=" + claimUserVO +
                '}';
    }
}
