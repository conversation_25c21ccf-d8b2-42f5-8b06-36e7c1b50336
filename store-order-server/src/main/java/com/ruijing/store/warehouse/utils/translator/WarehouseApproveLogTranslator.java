package com.ruijing.store.warehouse.utils.translator;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogDTO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseApprovalLogVO;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2022/3/14 10:48
 */
public class WarehouseApproveLogTranslator {

    /**
     * 订单审批日志转换
     * @param input order接口拿到的t_order_approve_log表的数据
     * @return
     */
    public static WarehouseApprovalLogVO toOrderApproveLog(OrderApprovalLogDTO input) {
        WarehouseApprovalLogVO output = new WarehouseApprovalLogVO();
        output.setApproveDesc(input.getApproveDescription());
        output.setApproveStatus(input.getApproveStatus());
        output.setApproveTime(input.getCreationTime().getTime());
        output.setApprover(input.getOperatorName());
        output.setApproveLevel(input.getApproveLevel());
        output.setApproveUrl(input.getSignUrl());

        return output;
    }

    /**
     * 订单审批日志转换
     * @param orderApprovalLogList order接口拿到的t_order_approve_log表的数据
     * @return
     */
    public static List<WarehouseApprovalLogVO> toOrderApproveLogs(List<OrderApprovalLogDTO> orderApprovalLogList) {
        // 排序
        return orderApprovalLogList.stream().map(WarehouseApproveLogTranslator::toOrderApproveLog).sorted(Comparator.comparing(WarehouseApprovalLogVO::getApproveTime)).collect(Collectors.toList());
    }

    /**
     * 采购与竞价审批日志转换
     * @param purchaseApprovalLogList
     * @return
     */
    public static List<WarehouseApprovalLogVO> toPurchaseApproveLogs(List<OrderPurchaseApprovalLogDTO> purchaseApprovalLogList) {
        List<WarehouseApprovalLogVO> resList = New.list();
        for (OrderPurchaseApprovalLogDTO input : purchaseApprovalLogList) {
            WarehouseApprovalLogVO output = new WarehouseApprovalLogVO();
            output.setApproveDesc(input.getResult());
            output.setApproveLevel(input.getApproveLevel());
            output.setApproveTime(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, input.getDate()).getTime());
            output.setApprover(input.getApprover());
            resList.add(output);
        }
        resList.sort(Comparator.comparing(WarehouseApprovalLogVO::getApproveTime));
        return resList;
    }
}
