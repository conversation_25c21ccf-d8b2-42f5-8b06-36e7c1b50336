package com.ruijing.store.warehouse.message.constant;

import com.google.common.collect.Lists;
import com.reagent.commonbase.enums.OrgEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/6 9:48
 */
public class WarehouseConstant {

    /**
     * 采购人中心出入库查看权限
     */
    public static final String BUYER_CENTER_WAREHOUSE_VIEW = "BUYER_CENTER_WAREHOUSE_VIEW";

    /**
     * 采购人中心提交入库权限
     */
    public static final String BUYER_CENTER_SUBMISSION_STORAGE = "BUYER_CENTER_SUBMISSION_STORAGE";

    /**
     * 采购人中心申领出库权限
     */
    public static final String BUYER_CENTER_CLAIM_OUT_WAREHOUSE = "BUYER_CENTER_APPLY"; 

    /**
     * 快速部署（OMS医院配置），是否使用库房系统的配置code
     */
    public static final String USE_WAREHOUSE_SYSTEM_CODE = "USE_WAREHOUSE_SYSTEM";

    /**
     * 快速部署（OMS医院配置），使用的库房系统的版本的配置code
     */
    public static final String WAREHOUSE_SYSTEM_VERSION_CODE = "WAREHOUSE_SYSTEM_VERSION";

    /**
     * 快速部署（OMS医院配置），使用库房系统的配置值
     */
    public static final String USE_WAREHOUSE_SYSTEM_VALUE = "1";

    /**
     * 快速部署（OMS医院配置），使用新库房系统的版本的配置值
     */
    public static final String WAREHOUSE_SYSTEM_VERSION_VALUE = "1";

    /**
     * 订单项目名称、编码、经费卡号分割符
     */
    public static final String ORDER_PROJECT_NAME_OR_CODE_OR_FUND_CARD_NO_SEPARATOR = ",";

    /**
     * 科长所在竞价单或采购单审批级别
     */
    public static final int SECTION_CHIEF_APPROVAL_LEVEL = 2;

    /**
     * 需要一级商品分类的单位code
     */
    public static final List<String> ORG_CODE_NEED_FIRST_LEVEL_CATEGORY = Lists.newArrayList(OrgEnum.GUANG_ZHOU_HU_XI_JIAN_KANG_YAN_JIU_YUAN.getCode(),
            OrgEnum.GUANG_DONG_SHENG_ZHI_NENG_KE_XUE_YU_JI_SHU.getCode());

    /**
     * 需要打印订单条形码的单位code
     */
    public static final List<String> ORG_CODE_NEED_ORDER_NO_BARCODE = Lists.newArrayList(
            OrgEnum.QI_LU_GONG_YE_DA_XUE.getCode(), OrgEnum.GUANG_ZHOU_HU_XI_JIAN_KANG_YAN_JIU_YUAN.getCode(), OrgEnum.SHAN_DONG_KE_XUE_YUAN_HE_ZE_FEN_YUAN.getCode()
    );
}
