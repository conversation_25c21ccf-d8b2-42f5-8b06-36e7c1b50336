package com.ruijing.store.warehouse.message.bean;

import java.util.List;

/**
 * <AUTHOR>
 * @Date Created in 15:40 2021/01/05.
 * @Modified
 * @Description
 */
public class ApprovalProgressBean {

    /**
     * 审批状态，0未审，1已审
     */
    private Integer status;

    /**
     * 审批进度名称
     */
    private String approvalProgressName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private String optTime;

    /**
     * 该节点是否自动执行 AUTO
     */
    private String autoExecute;

    /**
     * 当前节点可审批人名称列表
     */
    private List<String> operatorList;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getApprovalProgressName() {
        return approvalProgressName;
    }

    public void setApprovalProgressName(String approvalProgressName) {
        this.approvalProgressName = approvalProgressName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOptTime() {
        return optTime;
    }

    public void setOptTime(String optTime) {
        this.optTime = optTime;
    }

    public String getAutoExecute() {
        return autoExecute;
    }

    public void setAutoExecute(String autoExecute) {
        this.autoExecute = autoExecute;
    }

    public List<String> getOperatorList() {
        return operatorList;
    }

    public void setOperatorList(List<String> operatorList) {
        this.operatorList = operatorList;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("ApprovalProgressBean{");
        sb.append("status=").append(status);
        sb.append(", approvalProgressName='").append(approvalProgressName).append('\'');
        sb.append(", operator='").append(operator).append('\'');
        sb.append(", optTime='").append(optTime).append('\'');
        sb.append(", autoExecute='").append(autoExecute).append('\'');
        sb.append(", operatorList=").append(operatorList);
        sb.append('}');
        return sb.toString();
    }
}
