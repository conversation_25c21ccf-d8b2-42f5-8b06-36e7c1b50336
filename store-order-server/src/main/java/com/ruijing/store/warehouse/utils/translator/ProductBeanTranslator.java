package com.ruijing.store.warehouse.utils.translator;

import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.other.dto.OrderOutWarehouseProductInfoDTO;
import com.ruijing.store.warehouse.message.bean.ProductBean;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO;
import com.ruijing.store.warehouse.utils.OrderDetailsUtil;
import com.ruijing.store.warehouse.utils.PriceUtil;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDetailDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDetailDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/31 9:41
 */
public class ProductBeanTranslator {

    public static ProductBean orderDetailDTO2ProductBean(OrderDetailDTO from) {
        if (from == null) {
            return null;
        }
        ProductBean to = new ProductBean();
        to.setProductId(from.getProductSn());
        to.setQuantityWithoutReturn(OrderDetailsUtil.getRealOrderProductQuantity(from));
        to.setBrand(from.getFbrand());
        to.setCasNo(from.getCasno());
        int dangerousType = OrderDetailsUtil.getDangerousType(from.getDangerousTypeId());
        to.setDangerousType(dangerousType);
        to.setDangerousTypeName(DangerousTypeEnum.get(dangerousType).getName());
        boolean isDangerousType = DangerousTypeEnum.isDangerousType(dangerousType);
        to.setDangerousFlag(isDangerousType ? 1 : 0);
        to.setGoodCode(from.getFgoodcode());
        to.setProductName(from.getFgoodname());
        to.setCategoryId(from.getCategoryid());
        to.setRegulatoryFlag(from.getRegulatoryTypeId());
        to.setProductPhoto(from.getFpicpath());
        to.setUnit(from.getFunit());
        to.setSpecifications(from.getFspec());
        to.setSupplierId(from.getSuppId());
        to.setSupplierName(from.getSuppName());
        to.setOrderDetailId(from.getId());
        to.setQuantityAfterReturn(from.getFquantity().subtract(from.getFcancelquantity()).intValue());
        to.setMedicalDeviceRegisCertNumber(from.getMedicalDeviceRegisCertNumber());
        return to;
    }

    public static ProductBean  bizWarehouseEntryDetailDTO2ProductBean(BizWarehouseEntryDetailDTO from) {
        if (from == null) {
            return null;
        }
        ProductBean to = new ProductBean();
        to.setTotalQuantity(from.getMeasurementNum() == null ? null : from.getMeasurementNum().doubleValue());
        to.setQuantityUnit(from.getMeasurementUnit());
        to.setSupplierName(from.getSuppName());
        to.setSupplierId(from.getSuppId());
        to.setSpecifications(from.getSpecifications());
        to.setUnit(from.getReceivedUnit());
        to.setQuantityWithoutReturn(from.getReceivedNum());
        to.setProductName(from.getProductName());
        to.setGoodCode(from.getProductCode());
        to.setCasNo(from.getCasNo());
        to.setBrand(from.getBrandName());
        to.setForm(from.getForm());
        to.setProductPhoto(from.getFpicpath());
        to.setRegulatoryFlag(from.getControlFlag());
        int dangerousType = OrderDetailsUtil.getDangerousType(from.getDangerousType());
        to.setDangerousType(dangerousType);
        to.setDangerousTypeName(DangerousTypeEnum.get(dangerousType).getName());
        //判断是否是危化品
        boolean isDangerousType = DangerousTypeEnum.isDangerousType(from.getDangerousType());
        to.setDangerousFlag(isDangerousType ? 1 : 0);
        to.setPersonalizedCategoryName(from.getSort());
        to.setSupplyCategory(from.getSupplyCategory());
        to.setMaterialNo(from.getMaterialNo());
        to.setCbsdWastage(from.getCbsdWastage())
                .setCbsdLegallyPurposes(from.getCbsdLegallyPurposes())
                .setCbsdStorageAreaId(from.getCbsdStorageAreaId())
                .setCbsdStorageArea(from.getCbsdStorageArea());
        return to;
    }

    public static ProductBean bizWarehouseExitDetailDTO2ProductBean(BizWarehouseExitDetailDTO from) {
        if (from == null) {
            return null;
        }
        ProductBean to = new ProductBean();
        to.setTotalQuantity(from.getMeasurementNum() == null ? null : from.getMeasurementNum().doubleValue());
        to.setQuantityUnit(from.getMeasurementUnit());
        to.setSupplierName(from.getSuppName());
        to.setSupplierId(from.getSuppId());
        to.setSpecifications(from.getSpecifications());
        to.setUnit(from.getExitedUnit());
        to.setQuantityWithoutReturn(from.getExitedNum());
        to.setProductName(from.getProductName());
        to.setGoodCode(from.getProductCode());
        to.setCasNo(from.getCasNo());
        to.setBrand(from.getBrandName());
        to.setForm(from.getForm());
        to.setRegulatoryFlag(from.getControlFlag());
        int dangerousType = OrderDetailsUtil.getDangerousType(from.getDangerousType());
        to.setDangerousType(dangerousType);
        to.setDangerousTypeName(DangerousTypeEnum.get(dangerousType).getName());
        //判断是否是危化品
        boolean isDangerousType = DangerousTypeEnum.isDangerousType(from.getDangerousType());
        to.setDangerousFlag(isDangerousType ? 1 : 0);
        to.setPersonalizedCategoryName(from.getSort());

        to.setSinglePrice(from.getUnitPrice().doubleValue());
        to.setTotalPrice(from.getPrice().doubleValue());
        return to;
    }

    public static WarehouseProductInfoVO productBean2WarehouseProductInfoVO(ProductBean from) {
        if (from == null) {
            return null;
        }
        WarehouseProductInfoVO to = new WarehouseProductInfoVO();
        to.setProductId(from.getProductId());
        to.setQuantity(from.getQuantityWithoutReturn());
        to.setQuantityAfterReturn(from.getQuantityAfterReturn());
        to.setBrand(from.getBrand());
        to.setCasNo(from.getCasNo());
        to.setDangerousType(from.getDangerousType());
        to.setDangerousTypeName(from.getDangerousTypeName());
        to.setDangerousFlag(from.getDangerousFlag());
        to.setGoodCode(from.getGoodCode());
        to.setProductName(from.getProductName());
        to.setCategoryId(from.getCategoryId());
        to.setRegulatoryFlag(from.getRegulatoryFlag());
        to.setProductPhoto(from.getProductPhoto());
        to.setUnit(from.getUnit());
        to.setSpecifications(from.getSpecifications());
        to.setNeedSubmitWarehouseTag(from.getNeedSubmitWarehouseTag());
        to.setNeedShowDangerousInputFlag(from.getNeedShowDangerousInputFlag());
        to.setDefaultWarehouseId(from.getDefaultWarehouseId());
        to.setSupplierId(from.getSupplierId());
        to.setSupplierName(from.getSupplierName());
        to.setTotalQuantity(from.getTotalQuantity());
        to.setQuantityUnit(from.getQuantityUnit());
        to.setForm(from.getForm());
        to.setFirstLevelCategoryId(from.getFirstLevelCategoryId());
        to.setFirstLevelCategoryName(from.getFirstLevelCategoryName());
        to.setSinglePrice(PriceUtil.formatDouble2TwoDecimal(from.getSinglePrice()));
        to.setTotalPrice(PriceUtil.formatDouble2TwoDecimal(from.getTotalPrice()));
        to.setTotalPriceAfterReturn(from.getTotalPriceAfterReturn() != null ? PriceUtil.formatDouble2TwoDecimal(from.getTotalPriceAfterReturn().doubleValue()) : null);

        to.setPersonalizedCategoryName(from.getPersonalizedCategoryName());
        to.setOrderDetailId(from.getOrderDetailId());
        to.setSupplyCategory(from.getSupplyCategory());
        to.setMaterialNo(from.getMaterialNo());
        to.setCbsdLegallyPurposes(from.getCbsdLegallyPurposes())
                .setCbsdStorageAreaId(from.getCbsdStorageAreaId())
                .setCbsdStorageArea(from.getCbsdStorageArea())
                .setCbsdWastage(from.getCbsdWastage());
        to.setBindGasBottleBarcodes(from.getBindGasBottleBarcodes());
        to.setMedicalDeviceRegisCertNumber(from.getMedicalDeviceRegisCertNumber());
        return to;
    }

    public static List<WarehouseProductInfoVO> productBeanList2WarehouseProductInfoVOList(List<ProductBean> from) {
        if (CollectionUtils.isEmpty(from)) {
            return Collections.emptyList();
        }
        return from.stream().map(ProductBeanTranslator::productBean2WarehouseProductInfoVO).collect(Collectors.toList());
    }

    /**
     * 库房的商品转为出库单dto
     *
     * @param from 库房的商品
     * @return 出库单数据
     */
    public static OrderOutWarehouseProductInfoDTO productBean2OrderOutWarehouseProductInfoDTO(ProductBean from) {
        if (from == null) {
            return null;
        }
        OrderOutWarehouseProductInfoDTO to = new OrderOutWarehouseProductInfoDTO();
        to.setProductId(from.getProductId());
        to.setQuantity(from.getQuantityWithoutReturn());
        to.setBrand(from.getBrand());
        to.setCasNo(from.getCasNo());
        to.setDangerousType(from.getDangerousType());
        to.setDangerousTypeName(from.getDangerousTypeName());
        to.setDangerousFlag(from.getDangerousFlag());
        to.setGoodCode(from.getGoodCode());
        to.setProductName(from.getProductName());
        to.setCategoryId(from.getCategoryId());
        to.setRegulatoryFlag(from.getRegulatoryFlag());
        to.setProductPhoto(from.getProductPhoto());
        to.setUnit(from.getUnit());
        to.setSpecifications(from.getSpecifications());
        to.setNeedSubmitWarehouseTag(from.getNeedSubmitWarehouseTag());
        to.setNeedShowDangerousInputFlag(from.getNeedShowDangerousInputFlag());
        to.setDefaultWarehouseId(from.getDefaultWarehouseId());
        to.setSupplierId(from.getSupplierId());
        to.setSupplierName(from.getSupplierName());
        to.setTotalQuantity(from.getTotalQuantity());
        to.setQuantityUnit(from.getQuantityUnit());
        to.setForm(from.getForm());
        to.setFirstLevelCategoryName(from.getFirstLevelCategoryName());
        to.setSinglePrice(PriceUtil.formatDouble2TwoDecimal(from.getSinglePrice()));
        to.setTotalPrice(PriceUtil.formatDouble2TwoDecimal(from.getTotalPrice()));
        to.setPersonalizedCategoryName(from.getPersonalizedCategoryName());
        to.setOrderDetailId(from.getOrderDetailId());
        to.setMedicalDeviceRegisCertNumber(from.getMedicalDeviceRegisCertNumber());
        return to;
    }

    /**
     * 库房的商品列表转为出库单dto列表
     *
     * @param from 库房的商品
     * @return 出库单数据
     */
    public static List<OrderOutWarehouseProductInfoDTO> productBeanList2OrderOutWarehouseProductInfoDTOList(List<ProductBean> from) {
        if (CollectionUtils.isEmpty(from)) {
            return Collections.emptyList();
        }
        return from.stream().map(ProductBeanTranslator::productBean2OrderOutWarehouseProductInfoDTO).collect(Collectors.toList());
    }
}
