package com.ruijing.store.warehouse.utils.translator;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.warehouse.message.bean.OrderBean;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2020/12/31 9:41
 */
public class OrderBeanTranslator {

    /**
     * 订单收货图片分割符
     */
    private static final String ORDER_PHOTO_SEPARATOR = ";";

    public static OrderBean orderMasterDTO2BaseOrderBean(OrderMasterDTO from) {
        if (from == null) {
            return null;
        }
        OrderBean to = new OrderBean();
        to.setOrderId(from.getId());
        to.setOrderNo(from.getForderno());
        to.setTotalPrice(from.getForderamounttotal().doubleValue());
        to.setPurchaserName(from.getFbuyername());
        to.setDepartmentName(from.getFbuydepartment());
        to.setOrgName(from.getFusername());
        to.setSupplierCode(from.getFsuppcode());
        to.setSupplierName(from.getFsuppname());
        to.setAcceptor(getPersonalAcceptor(from.getFusercode(), from.getFlastreceiveman()));
        to.setSpecies(from.getSpecies() == null ? null : from.getSpecies().intValue());
        to.setOrderType(OrderTypeEnum.getByCode(from.getOrderType()));
        String photoUrls = from.getReceivePicUrls();
        if (StringUtils.isNotBlank(photoUrls)) {
            to.setReceivingPhotos(Arrays.asList(photoUrls.split(ORDER_PHOTO_SEPARATOR)));
        } else {
            to.setReceivingPhotos(Collections.emptyList());
        }
        return to;
    }

    private static String getPersonalAcceptor(String orgCode, String orderAcceptor) {
        if (OrgEnum.NING_BO_ER_YUAN.getCode().equals(orgCode)) {
            return "马海香";
        }
        return orderAcceptor;
    }
}
