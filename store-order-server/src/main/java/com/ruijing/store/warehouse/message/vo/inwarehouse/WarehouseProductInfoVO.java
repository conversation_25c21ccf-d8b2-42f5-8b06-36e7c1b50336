package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库商品信息")
public class WarehouseProductInfoVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    /**
     * 需要入库
     */
    public static final int NEED_SUBMIT_WAREHOUSE = 1;

    /**
     * 不需要入库
     */
    public static final int NEED_NOT_SUBMIT_WAREHOUSE = 0;

    /**
     * 需要显示危化品输入框
     */
    public static final int NEED_SHOW_DANGEROUS_INPUT_TRUE = 1;

    /**
     * 不需要显示危化品输入框
     */
    public static final int NEED_SHOW_DANGEROUS_INPUT_FALSE = 0;

    @RpcModelProperty(value = "商品Id")
    private Long productId;

    @RpcModelProperty(value = "商品名称")
    private String productName;

    @RpcModelProperty(value = "商品规格")
    private String specifications;

    @RpcModelProperty(value = "商品品牌")
    private String brand;

    @RpcModelProperty(value = "商品货号")
    private String goodCode;

    @RpcModelProperty(value = "CAS号")
    private String casNo;

    @RpcModelProperty(value = "危化品标识(类型)")
    private Integer dangerousType;

    @RpcModelProperty(value = "危化品标识名称")
    private String dangerousTypeName;

    @RpcModelProperty(value = "是否是危化品, 0不是， 1是")
    private Integer dangerousFlag;

    @RpcModelProperty(value = "管制类型, 1,管制类;2,非管制,其他商品类型为null")
    private Integer regulatoryFlag;

    @RpcModelProperty(value = "单位")
    private String unit;

    @RpcModelProperty(value = "数量")
    private Integer quantity;

    @RpcModelProperty(value = "退货后订单商品数量")
    private Integer quantityAfterReturn;

    @RpcModelProperty("退货后的计量总量")
    private Double totalQuantityAfterReturn;

    @RpcModelProperty("单位计量含量")
    private BigDecimal unitMeasurementNum;

    @RpcModelProperty(value = "计量总量")
    private Double totalQuantity;

    @RpcModelProperty(value = "计量单位")
    private String quantityUnit;

    @RpcModelProperty(value = "供应商Id")
    private Integer supplierId;

    @RpcModelProperty(value = "供应商名称")
    private String supplierName;

    @RpcModelProperty(value = "商品图片")
    private String productPhoto;

    @RpcModelProperty(value = "是否是要提交入库的商品，如果这个商品的分类不在该单位指定的分类中则无需提交入库;0不需要入库，1需要入库")
    private Integer needSubmitWarehouseTag;

    @RpcModelProperty(value = "商品单价")
    private String singlePrice;

    @RpcModelProperty(value = "商品总额")
    private String totalPrice;

    @RpcModelProperty(value = "退货后的订单商品总价")
    private String totalPriceAfterReturn;

    @RpcModelProperty(value = "商品分类id")
    private Integer categoryId;

    @RpcModelProperty("一级商品分类id")
    private Integer firstLevelCategoryId;

    @RpcModelProperty(value = "一级商品分类名称")
    private String firstLevelCategoryName;

    @RpcModelProperty(value = "是否需要用户输入危化品属性, 0否，1是")
    private Integer needShowDangerousInputFlag;

    /**
     * {@link com.ruijing.store.wms.api.enums.FormEnum}
     */
    @RpcModelProperty(value = "商品状态, 1 固体, 2 液体, 3 气体")
    private Integer form;

    @RpcModelProperty(value = "默认库房Id")
    private Integer defaultWarehouseId;

    @RpcModelProperty(value = "个性化商品品类名称，只用作存取显示")
    private String personalizedCategoryName;

    @RpcModelProperty(value = "订单明细id")
    private Integer orderDetailId;

    @RpcModelProperty(value = "库房列表")
    private List<WarehouseVO> warehouseVOList;

    /**
     * 补充库房信息分类
     */
    @RpcModelProperty(value = "补充库房-分类")
    private String supplyCategory;

    @RpcModelProperty(value = "补充库房-物料编号")
    private String materialNo;

    @RpcModelProperty("中爆-损耗量")
    private String cbsdWastage;

    @RpcModelProperty("中爆-合法用途")
    private String cbsdLegallyPurposes;

    @RpcModelProperty(value = "中爆-储物场所id")
    private String cbsdStorageAreaId;

    @RpcModelProperty("中爆-储物场所")
    private String cbsdStorageArea;

    @RpcModelProperty("绑定的气瓶码")
    private List<String> bindGasBottleBarcodes;

    @RpcModelProperty("绑定的气瓶")
    private List<GasBottleVO> bindGasBottles;

    @RpcModelProperty("平台编码 原货号字段")
    private String productCode;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    public Double getTotalQuantityAfterReturn() {
        return totalQuantityAfterReturn;
    }

    public void setTotalQuantityAfterReturn(Double totalQuantityAfterReturn) {
        this.totalQuantityAfterReturn = totalQuantityAfterReturn;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getDangerousFlag() {
        return dangerousFlag;
    }

    public void setDangerousFlag(Integer dangerousFlag) {
        this.dangerousFlag = dangerousFlag;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProductPhoto() {
        return productPhoto;
    }

    public void setProductPhoto(String productPhoto) {
        this.productPhoto = productPhoto;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getQuantityUnit() {
        return quantityUnit;
    }

    public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
    }

    public Double getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getQuantityAfterReturn() {
        return quantityAfterReturn;
    }

    public void setQuantityAfterReturn(Integer quantityAfterReturn) {
        this.quantityAfterReturn = quantityAfterReturn;
    }

    public BigDecimal getUnitMeasurementNum() {
        return unitMeasurementNum;
    }

    public WarehouseProductInfoVO setUnitMeasurementNum(BigDecimal unitMeasurementNum) {
        this.unitMeasurementNum = unitMeasurementNum;
        return this;
    }

    public Integer getNeedSubmitWarehouseTag() {
        return needSubmitWarehouseTag;
    }

    public void setNeedSubmitWarehouseTag(Integer needSubmitWarehouseTag) {
        this.needSubmitWarehouseTag = needSubmitWarehouseTag;
    }

    public String getSinglePrice() {
        return singlePrice;
    }

    public void setSinglePrice(String singlePrice) {
        this.singlePrice = singlePrice;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTotalPriceAfterReturn() {
        return totalPriceAfterReturn;
    }

    public void setTotalPriceAfterReturn(String totalPriceAfterReturn) {
        this.totalPriceAfterReturn = totalPriceAfterReturn;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getNeedShowDangerousInputFlag() {
        return needShowDangerousInputFlag;
    }

    public void setNeedShowDangerousInputFlag(Integer needShowDangerousInputFlag) {
        this.needShowDangerousInputFlag = needShowDangerousInputFlag;
    }

    public Integer getRegulatoryFlag() {
        return regulatoryFlag;
    }

    public void setRegulatoryFlag(Integer regulatoryFlag) {
        this.regulatoryFlag = regulatoryFlag;
    }

    public Integer getForm() {
        return form;
    }

    public void setForm(Integer form) {
        this.form = form;
    }

    public String getFirstLevelCategoryName() {
        return firstLevelCategoryName;
    }

    public void setFirstLevelCategoryName(String firstLevelCategoryName) {
        this.firstLevelCategoryName = firstLevelCategoryName;
    }

    public Integer getFirstLevelCategoryId() {
        return firstLevelCategoryId;
    }

    public WarehouseProductInfoVO setFirstLevelCategoryId(Integer firstLevelCategoryId) {
        this.firstLevelCategoryId = firstLevelCategoryId;
        return this;
    }

    public Integer getDefaultWarehouseId() {
        return defaultWarehouseId;
    }

    public void setDefaultWarehouseId(Integer defaultWarehouseId) {
        this.defaultWarehouseId = defaultWarehouseId;
    }

    public String getPersonalizedCategoryName() {
        return personalizedCategoryName;
    }

    public void setPersonalizedCategoryName(String personalizedCategoryName) {
        this.personalizedCategoryName = personalizedCategoryName;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public List<WarehouseVO> getWarehouseVOList() {
        return warehouseVOList;
    }

    public void setWarehouseVOList(List<WarehouseVO> warehouseVOList) {
        this.warehouseVOList = warehouseVOList;
    }

    public String getSupplyCategory() {
        return supplyCategory;
    }

    public void setSupplyCategory(String supplyCategory) {
        this.supplyCategory = supplyCategory;
    }

    public String getMaterialNo() {
        return materialNo;
    }

    public void setMaterialNo(String materialNo) {
        this.materialNo = materialNo;
    }

    public String getCbsdWastage() {
        return cbsdWastage;
    }

    public WarehouseProductInfoVO setCbsdWastage(String cbsdWastage) {
        this.cbsdWastage = cbsdWastage;
        return this;
    }

    public String getCbsdLegallyPurposes() {
        return cbsdLegallyPurposes;
    }

    public WarehouseProductInfoVO setCbsdLegallyPurposes(String cbsdLegallyPurposes) {
        this.cbsdLegallyPurposes = cbsdLegallyPurposes;
        return this;
    }

    public String getCbsdStorageAreaId() {
        return cbsdStorageAreaId;
    }

    public WarehouseProductInfoVO setCbsdStorageAreaId(String cbsdStorageAreaId) {
        this.cbsdStorageAreaId = cbsdStorageAreaId;
        return this;
    }

    public String getCbsdStorageArea() {
        return cbsdStorageArea;
    }

    public WarehouseProductInfoVO setCbsdStorageArea(String cbsdStorageArea) {
        this.cbsdStorageArea = cbsdStorageArea;
        return this;
    }

    public List<String> getBindGasBottleBarcodes() {
        return bindGasBottleBarcodes;
    }

    public WarehouseProductInfoVO setBindGasBottleBarcodes(List<String> bindGasBottleBarcodes) {
        this.bindGasBottleBarcodes = bindGasBottleBarcodes;
        return this;
    }

    public List<GasBottleVO> getBindGasBottles() {
        return bindGasBottles;
    }

    public WarehouseProductInfoVO setBindGasBottles(List<GasBottleVO> bindGasBottles) {
        this.bindGasBottles = bindGasBottles;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public void setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
    }

    @Override
    public String toString() {
        return "WarehouseProductInfoVO{" +
                "productId=" + productId +
                ", productName='" + productName + '\'' +
                ", specifications='" + specifications + '\'' +
                ", brand='" + brand + '\'' +
                ", goodCode='" + goodCode + '\'' +
                ", casNo='" + casNo + '\'' +
                ", dangerousType=" + dangerousType +
                ", dangerousTypeName='" + dangerousTypeName + '\'' +
                ", dangerousFlag=" + dangerousFlag +
                ", regulatoryFlag=" + regulatoryFlag +
                ", unit='" + unit + '\'' +
                ", quantity=" + quantity +
                ", quantityAfterReturn=" + quantityAfterReturn +
                ", totalQuantityAfterReturn=" + totalQuantityAfterReturn +
                ", unitMeasurementNum=" + unitMeasurementNum +
                ", totalQuantity=" + totalQuantity +
                ", quantityUnit='" + quantityUnit + '\'' +
                ", supplierId=" + supplierId +
                ", supplierName='" + supplierName + '\'' +
                ", productPhoto='" + productPhoto + '\'' +
                ", needSubmitWarehouseTag=" + needSubmitWarehouseTag +
                ", singlePrice='" + singlePrice + '\'' +
                ", totalPrice='" + totalPrice + '\'' +
                ", totalPriceAfterReturn='" + totalPriceAfterReturn + '\'' +
                ", categoryId=" + categoryId +
                ", firstLevelCategoryId=" + firstLevelCategoryId +
                ", firstLevelCategoryName='" + firstLevelCategoryName + '\'' +
                ", needShowDangerousInputFlag=" + needShowDangerousInputFlag +
                ", form=" + form +
                ", defaultWarehouseId=" + defaultWarehouseId +
                ", personalizedCategoryName='" + personalizedCategoryName + '\'' +
                ", orderDetailId=" + orderDetailId +
                ", warehouseVOList=" + warehouseVOList +
                ", supplyCategory='" + supplyCategory + '\'' +
                ", materialNo='" + materialNo + '\'' +
                ", cbsdWastage='" + cbsdWastage + '\'' +
                ", cbsdLegallyPurposes='" + cbsdLegallyPurposes + '\'' +
                ", cbsdStorageAreaId='" + cbsdStorageAreaId + '\'' +
                ", cbsdStorageArea='" + cbsdStorageArea + '\'' +
                ", bindGasBottleBarcodes=" + bindGasBottleBarcodes +
                ", bindGasBottles=" + bindGasBottles +
                ", productCode='" + productCode + '\'' +
                ", medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + '\'' +
                '}';
    }
}
