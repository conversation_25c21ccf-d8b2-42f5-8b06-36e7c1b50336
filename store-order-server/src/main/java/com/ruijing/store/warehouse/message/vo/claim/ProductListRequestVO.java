package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:16 2020/12/24.
 * 申领单关联商品列表请求对象
 */
@RpcModel(description="申领单关联商品列表请求对象")
public class ProductListRequestVO implements Serializable {

    private static final long serialVersionUID = 4717454454475281942L;

    @RpcModelProperty(value = "申领库房Id, 增改申领单时使用", example = "94")
    private Integer roomId;

    @RpcModelProperty(value = "申领方式 0普通商品 1按危化品", example = "0")
    private Integer claimType;

    @RpcModelProperty(value = "当前页，>=1", example = "1")
    private Integer currentPage;

    @RpcModelProperty(value = "页面大小", example = "10")
    private Integer pageSize;

    @RpcModelProperty(value = "商品名称", example = "小鼠")
    private String productName;

    @RpcModelProperty(value = "商品货号")
    private String goodCode;

    @RpcModelProperty(value = "商品cas号")
    private String casNo;

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public Integer getClaimType() {
        return claimType;
    }

    public void setClaimType(Integer claimType) {
        this.claimType = claimType;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ProductListRequestVO{");
        sb.append("roomId=").append(roomId);
        sb.append(", claimType=").append(claimType);
        sb.append(", currentPage=").append(currentPage);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", productName='").append(productName).append('\'');
        sb.append(", goodCode='").append(goodCode).append('\'');
        sb.append(", casNo='").append(casNo).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
