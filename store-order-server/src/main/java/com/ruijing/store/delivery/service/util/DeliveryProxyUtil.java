package com.ruijing.store.delivery.service.util;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum;

/**
 * <AUTHOR>
 * @date 2023/2/1 9:38
 * @description
 */
public class DeliveryProxyUtil {

    /**
     * 供应商代配送设置页面
     */
    public final static String SUPP_DELIVERY_PROXY_SETTING_PAGE = Environment.isProdEnv() ? "https://www.rjmart.cn/SP/agentDeliveryManagement" : "https://www.test.rj-info.com/SP/agentDeliveryManagement";

    /**
     * 判断是否代配送未关闭且为采购人开启的
     * @param orderAddressDTO 地址信息
     * @return 是否代配送未关闭且为采购人开启的
     */
    public static boolean isDeliveryProxyOnWithBuyerOpen(OrderAddressDTO orderAddressDTO){
        return orderAddressDTO != null && DeliveryTypeEnum.PROXY.getCode().equals(orderAddressDTO.getDeliveryType()) && DeliveryProxySourceTypeEnum.BUYER.getCode().equals(orderAddressDTO.getProxySourceType());
    }

    /**
     * 判断是否采购人开启的代配送
     * @param orderAddressDTO 待配送数据
     * @return 是否采购人开启的代配送
     */
    public static boolean isBuyerOpenDeliveryProxy(OrderAddressDTO orderAddressDTO){
        return orderAddressDTO != null && DeliveryProxySourceTypeEnum.BUYER.getCode().equals(orderAddressDTO.getProxySourceType());
    }
}
