package com.ruijing.store.order.base.timeoutstatistics.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.ordermaster.dto.GoodsReturnParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.enums.TimeOutEnums;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.freezedeptlog.dto.FreezeDeptLogDTO;
import com.ruijing.store.order.base.freezedeptlog.enums.IsDeletedEnum;
import com.ruijing.store.order.base.freezedeptlog.service.FreezeDeptLogService;
import com.ruijing.store.order.base.timeoutstatistics.constant.TimeOutStatisticsServiceConstant;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * @description: 申请退货和统计数据相关策略
 * @author: zhongyulei
 * @create: 2019/11/28 10:30
 **/
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
@Service(TimeOutStatisticsServiceConstant.TIME_OUT_STATISTICS_WITH_EXAMINE)
@Primary
public class TimeoutStatisticsWithExamineImpl extends AbstractTimeoutStatisticsServiceImpl {
    @Resource
    private FreezeDeptLogService freezeDeptLogService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private TimeoutQueryService timeoutQueryService;

    @Override
    public boolean executeTimeOutStatisticsIncrease(Integer count, GoodsReturnParamDTO params, Map<String, Integer> timeOutConfigMap) {
        // 统计过滤：如果为南方医科大学 订单验收超时需求定制，订单结算方式为“课题组自结算”时，不计入超时设置统计
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(params.getOrderMasterId());
        if(OrgEnum.NAN_FANG_YI_KE.getCode().equals(orderMasterDO.getFusercode()) && OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderMasterDO.getFundStatus())){
            return false;
        }

        TimeoutStatisticsDTO examineStatistics = findByDepartmentIdAndType(params.getOrganizationId(), params.getDepartmentId(), TimeOutBusinessType.ACCEPTANCE.getValue());
        Integer timeOutCount = examineStatistics.getAmount();
        if (count == null || count == 0) {
            return false;
        }

        // 如果超时订单统计数 已存在且大于0
        if (timeOutCount != null && timeOutCount > 0) {
            logger.info("orgId:{},deptId:{},验收超时统计数量原数量{}，将增加{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), timeOutCount, count);
            // 撤销退货后, 验收超时统计数 +1
            timeOutCount += count;
            examineStatistics.setAmount(timeOutCount);
            updateAmountById(examineStatistics);
            return false;
        } else {
            // 去ES查一下验收超时订单的数量
            int examineLimitDays = timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode());
            TimeOutOrderParamsDTO timeoutQryParam = new TimeOutOrderParamsDTO();
            timeoutQryParam.setUserId(params.getOrganizationId());
            timeoutQryParam.setDepartmentIds(New.list(params.getDepartmentId()));
            timeoutQryParam.setPageNo(0);
            timeoutQryParam.setPageSize(0);
            timeoutQryParam.setOverTimeType(TimeOutEnums.EXAMINE.getType());
            Long examineTimeOutCount = timeoutQueryService.queryTimeOutOrder(timeoutQryParam, 0, examineLimitDays).getTotalHits();

            // 如果超过设定冻结阈值, 插入统计数据
            Integer finalTimeOutCount = examineTimeOutCount.intValue();
            transactionTemplate.execute(transactionStatus -> {
                // 添加统计数据
                if (finalTimeOutCount >= timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode())) {
                    logger.info("orgId:{},deptId:{},验收超时统计数量为空，查出数量0，将增加{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), finalTimeOutCount);
                    Date nowDate = new Date();
                    insertBatch(New.list(
                            new TimeoutStatisticsDTO(
                                    params.getOrganizationId(),
                                    params.getDepartmentId(),
                                    TimeOutBusinessType.ACCEPTANCE.getValue(),
                                    finalTimeOutCount,
                                    nowDate,
                                    timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode()),
                                    timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode())
                            )
                    ));
                    // 冻结课题组
                    freezeDeptLogService.insertList(New.list(
                            new FreezeDeptLogDTO(
                                    params.getOrganizationId(),
                                    params.getDepartmentId(),
                                    TimeOutBusinessType.ACCEPTANCE.getValue(),
                                    IsDeletedEnum.NOT_DELETED.getCode(),
                                    nowDate
                            )
                    ));
                }
                return transactionStatus.isCompleted();
            });

        }

        return true;
    }

    @Override
    public boolean executeTimeOutStatisticsDecrease(Integer count, GoodsReturnParamDTO params, Map<String, Integer> timeOutConfigMap) {
        boolean isLessThanConfigExamineAmount = false;
        TimeoutStatisticsDTO examineStatistics = findByDepartmentIdAndType(params.getOrganizationId(), params.getDepartmentId(), TimeOutBusinessType.ACCEPTANCE.getValue());
        Integer examineTimeOutCount = examineStatistics.getAmount();
        // 如果超时订单数大于0
        if (examineTimeOutCount != null && examineTimeOutCount > 0) {
            // 超时验收单退货后, 验收超时统计数 -count
            if (count != null && count > 0) {
                int initAmount = examineTimeOutCount;
                examineTimeOutCount -= count;
                if (examineTimeOutCount < timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode())) {
                    logger.info("orgId:{},deptId:{},验收超时统计数量，原数量{},减少{}，小于阈值{},将清空", params.getOrganizationId(), params.getDepartmentId(), initAmount, count, timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode()));
                    // 此时统计数小于配置超时单个数
                    deleteById(examineStatistics.getId());
                    isLessThanConfigExamineAmount = true;
                } else {
                    logger.info("orgId:{},deptId:{},验收超时统计数量，原数量{},减少{}", params.getOrganizationId(), params.getDepartmentId(), initAmount, count);
                    examineStatistics.setAmount(examineTimeOutCount);
                    updateAmountById(examineStatistics);
                }
            }

        } else {
            // 否则没有超过阈值
            isLessThanConfigExamineAmount = true;
        }
        return isLessThanConfigExamineAmount;
    }

}
