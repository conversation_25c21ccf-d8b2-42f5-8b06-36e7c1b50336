package com.ruijing.store.order.sysu;

import com.google.common.collect.Lists;
import com.reagent.research.custom.enums.order.OrderListTypeEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.search.client.enums.QueryType;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.*;
import com.ruijing.search.client.query.*;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.Record;
import com.ruijing.search.client.response.Response;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.search.client.sort.NestedSortItem;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.api.sysu.dto.OrderDeptDeliveryDateDTO;
import com.ruijing.store.order.api.sysu.dto.OrderReceiptApproveQueryDTO;
import com.ruijing.store.order.api.sysu.dto.OrderRequestDTO;
import com.ruijing.store.order.api.sysu.service.SysuOrderSearchService;
import com.ruijing.store.order.rpc.client.OrderSearchRPCServiceClient;
import com.ruijing.store.order.search.translator.OrderPojoTranslator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @description: 中大订单搜索服务实现
 * @author: zhengzhendong
 * @create: 2020-10-15 15:12
 **/

@MSharpService
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class SysuOrderSearchServiceImpl implements SysuOrderSearchService {

    private static final String ORDER_SEARCH_INDEX = "order";

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    /**
     * 不关联用户操作日志条件的查询类型集合（我的待验收，我的验收审批）
     */
    private static final List<Integer> EXCLUDE_LOG_RELATED_LIST_TYPE
            = Lists.newArrayList(OrderListTypeEnum.MY_STAY_APPROVAL.getValue(), OrderListTypeEnum.MY_WAITING_RECEIPT.getValue());

    @Resource
    private OrderSearchRPCServiceClient orderSearchRPCServiceClient;

    @Override
    public RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> overTimeOrderSearch(OrderRequestDTO orderRequestDto) {
        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setStart((orderRequestDto.getPageNo() - 1) * orderRequestDto.getPageSize());
        searchRequest.setPageSize(orderRequestDto.getPageSize());

        // 机构id
        if (Objects.nonNull(orderRequestDto.getOrgId())) {
            searchRequest.addQuery(new TermQuery("fuserid", orderRequestDto.getOrgId()));
        }

        // 设置课题组订单关联发货开始结束时间
        this.setDepartmentDeliveryTime(searchRequest, orderRequestDto);

        // 订单号或采购单号
        if (StringUtils.isNotBlank(orderRequestDto.getNo())) {
            List<Query> queryList = new ArrayList<>();
            queryList.add(new PhraseQuery("fbuyapplicationno", orderRequestDto.getNo().trim().toUpperCase()));
            queryList.add(new PhraseQuery("forderno", orderRequestDto.getNo().trim().toUpperCase()));
            searchRequest.addQuery(new OrQuery(queryList));
        }
        // 经费卡号
        if (StringUtils.isNotBlank(orderRequestDto.getFundCardNo())) {
            searchRequest.addQuery(new NestedQuery("card", new TermQuery("card.card_no", orderRequestDto.getFundCardNo())));
        }

        // 过滤订单号
        if (CollectionUtils.isNotEmpty(orderRequestDto.getExcludeOrderList())) {
            searchRequest.addNotFilter(new TermFilter("forderno.keyword", orderRequestDto.getExcludeOrderList()));
        }

        // 商品名称或货号
        if (StringUtils.isNotBlank(orderRequestDto.getProductSearch())) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new NestedQuery("order_detail", new PhraseQuery("order_detail.fgoodname", orderRequestDto.getProductSearch())));
            orQuery.addQuery(new NestedQuery("order_detail", new PhraseQuery("order_detail.fgoodcode", orderRequestDto.getProductSearch())));
            searchRequest.addQuery(orQuery);
        }

        // 采购经办人/供应商
        if (StringUtils.isNotBlank(orderRequestDto.getUserSearch())) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new PhraseQuery("fbuyername", orderRequestDto.getUserSearch()));
            orQuery.addQuery(new PhraseQuery("fsuppname", orderRequestDto.getUserSearch()));
            searchRequest.addQuery(orQuery);
        }

        // 关联关系
        if (Objects.nonNull(orderRequestDto.getRelateInfo())) {
            if (orderRequestDto.getRelateInfo()) {
                searchRequest.addFilter(new NotNullFilter("relateInfo"));
            } else {
                searchRequest.addFilter(new IsNullFilter("relateInfo"));
            }
        }
        // 经费状态
        if (Objects.nonNull(orderRequestDto.getFundStatus())) {
            searchRequest.addFilter(new TermFilter("fund_status", Collections.singletonList(orderRequestDto.getFundStatus())));
        }
        // 订单状态
        List<Integer> statusList = orderRequestDto.getStatusList();
        if (Objects.nonNull(statusList)) {
            searchRequest.addFilter(new TermFilter("status", statusList));
        }
        // 线上、线下单
        if (Objects.nonNull(orderRequestDto.getProcessSpecies())) {
            searchRequest.addFilter(new TermFilter("species", orderRequestDto.getProcessSpecies()));
        }

        // 如果用户不具备经费卡授权，则只可查询订单为采购人自己的
        if (Objects.nonNull(orderRequestDto.getCardAuthAccess()) && !orderRequestDto.getCardAuthAccess()) {
            searchRequest.addShouldQuery(new TermQuery("fbuyerid", orderRequestDto.getBuyerId()));
        }

        // exclude排除供应商ID
        if (CollectionUtils.isNotEmpty(orderRequestDto.getExcludeSuppIdList())) {
            searchRequest.addNotFilter(new TermFilter("fsuppid", orderRequestDto.getExcludeSuppIdList()));
        }

        searchRequest.addOrderSortItem(new FieldSortItem("fdeliverydate", SortOrder.DESC));

        Response response = orderSearchRPCServiceClient.search(searchRequest);
        List<Record> recordList = response.getRecordList();
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = OrderPojoTranslator.recordsToOrderMasterDTOS(recordList);
        SearchPageResultDTO<OrderMasterSearchDTO> result = new SearchPageResultDTO();
        result.setTotalHits(response.getTotalHits());
        result.setRecordList(orderMasterSearchDTOList);
        return RemoteResponse.success(result);
    }

    @Override
    public RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> statementOverTimeOverSearch(OrderRequestDTO orderRequestDto) {
        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setStart((orderRequestDto.getPageNo() - 1) * orderRequestDto.getPageSize());
        searchRequest.setPageSize(orderRequestDto.getPageSize());
        // 机构id
        if (Objects.nonNull(orderRequestDto.getOrgId())) {
            searchRequest.addFilter(new TermFilter("fuserid", orderRequestDto.getOrgId()));
        }
        if (Objects.nonNull(orderRequestDto.getBuyerId())){
            searchRequest.addFilter(new TermFilter("fbuyerid", orderRequestDto.getBuyerId()));
        }
        // 过滤订单号
        if (CollectionUtils.isNotEmpty(orderRequestDto.getExcludeOrderList())) {
            searchRequest.addNotFilter(new TermFilter("forderno.keyword", orderRequestDto.getExcludeOrderList()));
        }
        searchRequest.addFilter(new TermFilter("status", OrderStatusEnum.WaitingForStatement_1.getValue()));
        String overTimeDateStr = LocalDateTime.now().minusDays(35).format(formatter);
        AndFilter waitingStatementTimeFilter = new AndFilter(
                New.list(new TermFilter("order_extra.extra_key", OrderExtraEnum.WAITING_STATEMENT_TIME.getValue()),
                        new RangeFilter("order_extra.extra_value", null, overTimeDateStr))
        );
        searchRequest.addFilter(new NestedFilter("order_extra", waitingStatementTimeFilter));

        Response response = orderSearchRPCServiceClient.search(searchRequest);
        List<Record> recordList = response.getRecordList();
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = OrderPojoTranslator.recordsToOrderMasterDTOS(recordList);
        SearchPageResultDTO<OrderMasterSearchDTO> result = new SearchPageResultDTO();
        result.setTotalHits(response.getTotalHits());
        result.setRecordList(orderMasterSearchDTOList);
        return RemoteResponse.success(result);
    }

    /**
     * 设置课题组订单关联发货开始结束时间
     * @param searchRequest
     * @param orderRequestDto
     */
    private void setDepartmentDeliveryTime(Request searchRequest, OrderRequestDTO orderRequestDto) {
        OrQuery departmentDeliveryOrQuery = new OrQuery();

        // 部门与发货时间组合 or 查询
        if (CollectionUtils.isNotEmpty(orderRequestDto.getOrderDeptDeliveryDateDtoList())) {
            OrQuery orQuery = new OrQuery();
            for (OrderDeptDeliveryDateDTO orderDeptDeliveryDateDto : orderRequestDto.getOrderDeptDeliveryDateDtoList()) {
                List<Query> termQueryList = new ArrayList<>();
                termQueryList.add(new TermQuery("fbuydepartmentid", orderDeptDeliveryDateDto.getDepartmentId()));
                termQueryList.add(new RangeQuery("fdeliverydate", orderDeptDeliveryDateDto.getDeliveryStartDate(), orderDeptDeliveryDateDto.getDeliveryEndDate()));
                orQuery.addQuery(new AndQuery(termQueryList));
            }
            departmentDeliveryOrQuery.addQuery(orQuery);
        }

        if (CollectionUtils.isNotEmpty(orderRequestDto.getDepartmentIds())) {
            // 组合条件 (fbuydepartmentid in departmentIds and fdeliverydate between startDate and endDate)
            List<Query> termQueryList = new ArrayList<>();
            termQueryList.add(new TermQuery("fbuydepartmentid", orderRequestDto.getDepartmentIds()));
            if (StringUtils.isNotBlank(orderRequestDto.getDeliveryStartDate()) && StringUtils.isNotBlank(orderRequestDto.getDeliveryEndDate())) {
                // 发货时间
                termQueryList.add(new RangeQuery("fdeliverydate", orderRequestDto.getDeliveryStartDate(), orderRequestDto.getDeliveryEndDate()));
            }
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new AndQuery(termQueryList));
            departmentDeliveryOrQuery.addQuery(orQuery);
        } else {
            // 部门id
            if (Objects.nonNull(orderRequestDto.getDepartmentId())) {
                searchRequest.addFilter(new TermFilter("fbuydepartmentid", orderRequestDto.getDepartmentId()));
            }

            if (StringUtils.isNotBlank(orderRequestDto.getDeliveryStartDate()) && StringUtils.isNotBlank(orderRequestDto.getDeliveryEndDate())) {
                // 发货时间
                searchRequest.addFilter(new RangeFilter("fdeliverydate", orderRequestDto.getDeliveryStartDate(), orderRequestDto.getDeliveryEndDate()));
            }
        }

        if (CollectionUtils.isNotEmpty(departmentDeliveryOrQuery.getQueryList())) {
            searchRequest.addQuery(departmentDeliveryOrQuery);
        }
    }

    @Override
    public RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> orderListSearch(OrderRequestDTO orderRequestDto) {

        // 必须设置请求单位
        boolean hasOrg = Objects.nonNull(orderRequestDto.getOrgId()) || CollectionUtils.isNotEmpty(orderRequestDto.getOrgIds());
        Preconditions.isTrue(hasOrg, "单位id不可为空");

        if (Objects.nonNull(orderRequestDto.getOrgId())) {
            Preconditions.isTrue(ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orderRequestDto.getOrgId()), "单位需为中大或中大深圳");
        } else if (CollectionUtils.isNotEmpty(orderRequestDto.getOrgIds())) {
            Preconditions.isTrue(ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgIdList(orderRequestDto.getOrgIds()), "单位需为中大或中大深圳");
        }

        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setStart((orderRequestDto.getPageNo() - 1) * orderRequestDto.getPageSize());
        searchRequest.setPageSize(orderRequestDto.getPageSize());

        //设置查询条件
        this.setSearchRequest(orderRequestDto, searchRequest);
        // 设置排序
        this.setSortRequest(orderRequestDto, searchRequest);

        Response response = orderSearchRPCServiceClient.search(searchRequest);
        List<Record> recordList = response.getRecordList();
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = OrderPojoTranslator.recordsToOrderMasterDTOS(recordList);
        SearchPageResultDTO<OrderMasterSearchDTO> result = new SearchPageResultDTO<>();
        result.setTotalHits(response.getTotalHits());
        result.setRecordList(orderMasterSearchDTOList);
        return RemoteResponse.success(result);
    }

    @Override
    public PageableResponse<List<OrderMasterSearchDTO>> myApprovedRiskOrderSearch(OrderRequestDTO orderRequestDto) {
        // 必须设置请求单位
        boolean hasOrg = Objects.nonNull(orderRequestDto.getOrgId()) || CollectionUtils.isNotEmpty(orderRequestDto.getOrgIds());
        Preconditions.isTrue(hasOrg, "单位id不可为空");

        if (Objects.nonNull(orderRequestDto.getOrgId())) {
            Preconditions.isTrue(ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orderRequestDto.getOrgId()), "单位需为中大或中大深圳");
        } else if (CollectionUtils.isNotEmpty(orderRequestDto.getOrgIds())) {
            Preconditions.isTrue(ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgIdList(orderRequestDto.getOrgIds()), "单位需为中大或中大深圳");
        }

        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setStart((orderRequestDto.getPageNo() - 1) * orderRequestDto.getPageSize());
        searchRequest.setPageSize(orderRequestDto.getPageSize());

        // 设置请求单位
        if (Objects.nonNull(orderRequestDto.getOrgId())) {
            searchRequest.addQuery(new TermQuery("fuserid", orderRequestDto.getOrgId()));
        } else if (CollectionUtils.isNotEmpty(orderRequestDto.getOrgIds())) {
            searchRequest.addQuery(new TermQuery("fuserid", orderRequestDto.getOrgIds()));
        }

        // 订单号或采购单号
        if (StringUtils.isNotBlank(orderRequestDto.getNo())) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new PhraseQuery("fbuyapplicationno", orderRequestDto.getNo().trim().toUpperCase()));
            orQuery.addQuery(new PhraseQuery("forderno", orderRequestDto.getNo().trim().toUpperCase()));
            searchRequest.addQuery(orQuery);
        }

        // 单个部门id
        if (Objects.nonNull(orderRequestDto.getDepartmentId())) {
            searchRequest.addFilter(new TermFilter("fbuydepartmentid", orderRequestDto.getDepartmentId()));
        }

        // 采购人名称
        if (StringUtils.isNotBlank(orderRequestDto.getBuyerName())) {
            searchRequest.addQuery(new PhraseQuery("fbuyername", orderRequestDto.getBuyerName()));
        }

        // 线上、线下单
        if (Objects.nonNull(orderRequestDto.getProcessSpecies())) {
            searchRequest.addFilter(new TermFilter("species", orderRequestDto.getProcessSpecies()));
        }

        if(orderRequestDto.getOrderStartTime() != null || orderRequestDto.getOrderEndTime() != null){
            // 开始时间-结束时间
            String orderStartTime = orderRequestDto.getOrderStartTime();
            String orderEndTime = orderRequestDto.getOrderEndTime();
            searchRequest.addFilter(new RangeFilter("forderdate", orderStartTime, orderEndTime, true, true));
        }

        // 查询我的待审批记录
        Filter logTypeFilter = new TermFilter("log.approve_status", New.list(
                OrderApprovalEnum.APPLY_MODIFY_RISK_ORDER_TO_NORMAL.getValue(),
                OrderApprovalEnum.CLOSE_RISK_ORDER.getValue(),
                OrderApprovalEnum.MODIFY_RISK_ORDER_TO_NORMAL.getValue(),
                OrderApprovalEnum.REJECT_RISK_ORDER_MODIFY_APPLY.getValue()));
        Filter operatorFilter = new TermFilter("log.operator_id", orderRequestDto.getUserId());
        Filter andFilter = new AndFilter().addFilter(logTypeFilter).addFilter(operatorFilter);
        Filter logFilter = new NestedFilter("log", andFilter);
        searchRequest.addFilter(logFilter);
        searchRequest.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        Response response = orderSearchRPCServiceClient.search(searchRequest);
        List<Record> recordList = response.getRecordList();
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = OrderPojoTranslator.recordsToOrderMasterDTOS(recordList);
        return PageableResponse.<List<OrderMasterSearchDTO>>custom()
                .setPageSize(orderRequestDto.getPageSize())
                .setPageNo(orderRequestDto.getPageNo())
                .setData(orderMasterSearchDTOList)
                .setTotal(response.getTotalHits())
                .setSuccess();
    }

    private void setSortRequest(OrderRequestDTO orderRequestDto, Request searchRequest) {
        Integer listType = orderRequestDto.getListType();
        if (OrderListTypeEnum.MY_STAY_APPROVAL.getValue().equals(listType)) {
            /**
             * 待复核的订单，根据"提交复核时间"进行排序
             * "sort": [
             *     {
             *       "log.creation_time": {
             *         "order": "desc",
             *         "nested":{
             *           "path":"log",
             *           "filter":{
             *             "term":{"log.approve_status":"2"}
             *           }
             *         }
             *       }
             *     }
             *   ]
             */
            NestedSortItem nestedSortItem = new NestedSortItem("log.creation_time", SortOrder.ASC, "log", new TermFilter("log.approve_status", OrderApprovalEnum.PASS.getValue()));
            searchRequest.addOrderSortItem(nestedSortItem);
        } else if (OrderListTypeEnum.MY_ALREADY_APPROVAL.getValue().equals(listType)) {
            // 订单管理-已完成审批列表，订单按审批日期倒序排列（最后审批的订单放第一条）
            List<Integer> statusList = Arrays.asList(
                    OrderApprovalEnum.PASS.getValue(),
                    OrderApprovalEnum.SECOND_LEVEL_ACCEPT_APPROVE_PASS.getValue(),
                    OrderApprovalEnum.SETTLEMENT_REJECT.getValue(),
                    OrderApprovalEnum.SECOND_LEVEL_ACCEPT_APPROVE_REJECT.getValue()
                    );
            NestedSortItem nestedSortItem = new NestedSortItem("log.creation_time", SortOrder.DESC, "log", new TermFilter("log.approve_status", statusList));
            searchRequest.addOrderSortItem(nestedSortItem);
        } else {
            searchRequest.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        }
    }

    /**
     * 设置查询条件
     * @param orderRequestDto 订单请求参数
     * @param searchRequest 搜索对象
     */
    private void setSearchRequest(OrderRequestDTO orderRequestDto, Request searchRequest) {

        // 设置请求单位
        if (Objects.nonNull(orderRequestDto.getOrgId())) {
            searchRequest.addQuery(new TermQuery("fuserid", orderRequestDto.getOrgId()));
        } else if (CollectionUtils.isNotEmpty(orderRequestDto.getOrgIds())) {
            searchRequest.addQuery(new TermQuery("fuserid", orderRequestDto.getOrgIds()));
        }

        OrQuery orQueryParam = new OrQuery();
        // 用户id
        Integer userId = orderRequestDto.getUserId();
        if (Objects.nonNull(userId)) {
            orQueryParam.addQuery(new TermQuery("fbuyerid", userId));
        }

        // 课题组id集合
        if (CollectionUtils.isNotEmpty(orderRequestDto.getDepartmentIds())) {
            orQueryParam.addQuery(new TermQuery("fbuydepartmentid", orderRequestDto.getDepartmentIds()));
        }

        // "我的验收审批"列表，订单验收查询组合条件(（订单一级验收审核状态 + 课题组）or（订单二级验收审核状态 + 课题组）)
        if (CollectionUtils.isNotEmpty(orderRequestDto.getOrderReceiptApproveQueryDTOS())) {
            OrQuery orderReceiptApproveGroupQuery = new OrQuery();
            for (OrderReceiptApproveQueryDTO orderReceiptApproveQueryDTO : orderRequestDto.getOrderReceiptApproveQueryDTOS()) {
                AndQuery orderReceiptApproveQuery = new AndQuery();
                // 设置订单验收审核状态
                if (Objects.nonNull(orderReceiptApproveQueryDTO.getOrderStatus())) {
                    orderReceiptApproveQuery.addQuery(new TermQuery("status", orderReceiptApproveQueryDTO.getOrderStatus()));
                }

                // 设置订单验收审核可见课题组范围
                if (CollectionUtils.isNotEmpty(orderReceiptApproveQueryDTO.getDepartmentIds())) {
                    orderReceiptApproveQuery.addQuery(new TermQuery("fbuydepartmentid", orderReceiptApproveQueryDTO.getDepartmentIds()));
                }

                orderReceiptApproveGroupQuery.addQuery(orderReceiptApproveQuery);
            }

            orQueryParam.addQuery(orderReceiptApproveGroupQuery);
        }

        List<Integer> riskVerifiedStatusExcludeList = orderRequestDto.getRiskVerifiedStatusExcludeList();
        if (CollectionUtils.isNotEmpty(riskVerifiedStatusExcludeList)) {
            searchRequest.addNotFilter(new TermFilter("risk_verified_status", riskVerifiedStatusExcludeList));
        }
        // 订单待复核，过滤掉风险单，过滤掉退货中的订单
        if (OrderListTypeEnum.MY_STAY_APPROVAL.getValue().equals(orderRequestDto.getListType())
                && orderRequestDto.getStatusList().contains(OrderStatusEnum.OrderReceiveApprovalTwo.getValue())) {
            // 过滤掉退货中的订单
            List<Integer> excludeReturnStatusList = Arrays.asList(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode(), GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode(), GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode(), GoodsReturnStatusEnum.RETURNED_GOODS.getCode(), GoodsReturnStatusEnum.SUCCESS.getCode());
            NestedFilter termNestedFilter = new NestedFilter("order_detail", new TermFilter("order_detail.return_status", excludeReturnStatusList));
            searchRequest.addNotFilter(termNestedFilter);
        }

        // 授权给当前用户的经费卡id集合
        if (CollectionUtils.isNotEmpty(orderRequestDto.getFundCardIds())) {
            orQueryParam.addQuery(new NestedQuery("card", new TermQuery("card.card_id", orderRequestDto.getFundCardIds())));
        }

        // 设置订单商品详情搜索条件
        this.setOrderDetailSearchQuery(orderRequestDto, searchRequest);

        // 除了"我的待验收"、"我的待审批"列表查询类型外，其他的查询都要加上当前用户操作过的订单查询条件（防止出现二次验收审批(供应商取消结算)，而当前用户又没有被验收审批授权，从而能看见这笔单的场景）
        if (Objects.nonNull(userId) && !EXCLUDE_LOG_RELATED_LIST_TYPE.contains(orderRequestDto.getListType())) {
            orQueryParam.addQuery(new NestedQuery("log", new TermQuery("log.operator_id", userId)));
        }

        if (CollectionUtils.isNotEmpty(orQueryParam.getQueryList())) {
            searchRequest.addQuery(orQueryParam);
        }

        // 订单号List
        if (CollectionUtils.isNotEmpty(orderRequestDto.getOrderNoList())) {
            searchRequest.addQuery(new TermQuery("forderno.keyword", orderRequestDto.getOrderNoList()));
        }

        // 订单号或采购单号
        if (StringUtils.isNotBlank(orderRequestDto.getNo())) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new PhraseQuery("fbuyapplicationno", orderRequestDto.getNo().trim().toUpperCase()));
            orQuery.addQuery(new PhraseQuery("forderno", orderRequestDto.getNo().trim().toUpperCase()));
            searchRequest.addQuery(orQuery);
        }

        // 发货时间
        if (StringUtils.isNotBlank(orderRequestDto.getDeliveryStartDate()) && StringUtils.isNotBlank(orderRequestDto.getDeliveryEndDate())) {
            searchRequest.addFilter(new RangeFilter("fdeliverydate", orderRequestDto.getDeliveryStartDate(), orderRequestDto.getDeliveryEndDate()));
        }

        // 过滤订单号
        if (CollectionUtils.isNotEmpty(orderRequestDto.getExcludeOrderList())) {
            searchRequest.addNotFilter(new TermFilter("forderno.keyword", orderRequestDto.getExcludeOrderList()));
        }

        // 采购经办人/经费负责人/供应商/订单号
        if (StringUtils.isNotBlank(orderRequestDto.getUserSearch())) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new PhraseQuery("fbuyername", orderRequestDto.getUserSearch()));
            orQuery.addQuery(new PhraseQuery("fsuppname", orderRequestDto.getUserSearch()));
            orQuery.addQuery(new PhraseQuery("fbuydepartment", orderRequestDto.getUserSearch()));
            orQuery.addQuery(new PhraseQuery("forderno", orderRequestDto.getUserSearch().trim()));
            searchRequest.addQuery(orQuery);
        }

        // 单个部门id
        if (Objects.nonNull(orderRequestDto.getDepartmentId())) {
            searchRequest.addFilter(new TermFilter("fbuydepartmentid", orderRequestDto.getDepartmentId()));
        }
        // 学院
        if (null != orderRequestDto.getCollegeId()) {
            searchRequest.addFilter(new TermFilter("dept_parent_id", orderRequestDto.getCollegeId()));
        }

        if (CollectionUtils.isNotEmpty(orderRequestDto.getCollegeIds())) {
            searchRequest.addFilter(new TermFilter("dept_parent_id", orderRequestDto.getCollegeIds()));
        }

        if (StringUtils.isNotBlank(orderRequestDto.getCollegeName())) {
            searchRequest.addFilter(new TermFilter("dept_parent_name", orderRequestDto.getCollegeName()));
        }

        // 线上、线下单
        if (Objects.nonNull(orderRequestDto.getProcessSpecies())) {
            searchRequest.addFilter(new TermFilter("species", orderRequestDto.getProcessSpecies()));
        }

        // 采购人ID
        if (Objects.nonNull(orderRequestDto.getBuyerId())) {
            searchRequest.addQuery(new TermQuery("fbuyerid", orderRequestDto.getBuyerId()));
        }

        // 关联关系
        if (Objects.nonNull(orderRequestDto.getRelateInfo())) {
            if (orderRequestDto.getRelateInfo()) {
                NotNullFilter notNullFilter = new NotNullFilter("relateInfo");
                TermFilter termFilter = new TermFilter("relateInfo", "");
                searchRequest.addFilter(new AndNotFilter(notNullFilter, termFilter));
            } else {
                OrFilter orFilter = new OrFilter();
                orFilter.addFilter(new IsNullFilter("relateInfo"));
                orFilter.addFilter(new TermFilter("relateInfo", ""));
                searchRequest.addFilter(orFilter);
            }
        }

        // 经费状态
        if (Objects.nonNull(orderRequestDto.getFundStatus())) {
            searchRequest.addFilter(new TermFilter("fund_status", Collections.singletonList(orderRequestDto.getFundStatus())));
        }

        // 订单状态
        List<Integer> statusList = orderRequestDto.getStatusList();
        if (CollectionUtils.isNotEmpty(statusList)) {
            searchRequest.addFilter(new TermFilter("status", statusList));
        }

        // 财务结算单号
        if (StringUtils.isNotBlank(orderRequestDto.getFinanceNumber())) {
            searchRequest.addQuery(new PhraseQuery("extra_info", orderRequestDto.getFinanceNumber().trim()));
        }

        //结算状态
        List<Integer> statementStatusList = orderRequestDto.getStatementStatusList();
        if(CollectionUtils.isNotEmpty(orderRequestDto.getStatementStatusList())){
            searchRequest.addFilter(new TermFilter("statement_status", statementStatusList));
        }

        // 开始时间-结束时间
        String orderStartTime = orderRequestDto.getOrderStartTime();
        String orderEndTime = orderRequestDto.getOrderEndTime();
        searchRequest.addFilter(new RangeFilter("forderdate", orderStartTime, orderEndTime, true, true));

        // 我的已审批列表逻辑-需要将当前操作用户有驳回记录和验收审批通过的单都展示出来
        if (Objects.nonNull(orderRequestDto.getListType())
                && OrderListTypeEnum.MY_ALREADY_APPROVAL.getValue().equals(orderRequestDto.getListType())
                && Objects.nonNull(orderRequestDto.getMyApprovalUserId())) {
            AndQuery logAndQuery = new AndQuery();
            OrQuery logOrQuery = new OrQuery();
            logOrQuery.addQuery(new TermQuery("log.approve_status", OrderApprovalEnum.PASS.getValue()));
            logOrQuery.addQuery(new TermQuery("log.approve_status", OrderApprovalEnum.REJECT_DURING_RECEIVE_BY_BUYER.getValue()));
            logOrQuery.addQuery(new TermQuery("log.approve_status", OrderApprovalEnum.SECOND_LEVEL_ACCEPT_APPROVE_PASS.getValue()));
            logAndQuery.addQuery(new TermQuery("log.operator_id", orderRequestDto.getMyApprovalUserId()));
            logAndQuery.addQuery(logOrQuery);
            searchRequest.addQuery(new NestedQuery("log", logAndQuery));
        }

        // 备案参数
        if (Objects.nonNull(orderRequestDto.getConfirm())) {
            searchRequest.addQuery(new TermQuery("is_confirm", orderRequestDto.getConfirm()));
        }

        // 学院
        if (StringUtils.isNotBlank(orderRequestDto.getCollegeName())) {
            searchRequest.addQuery(new PhraseQuery("department_parent_name", orderRequestDto.getCollegeName()));
        }

        // 学院id
        if (Objects.nonNull(orderRequestDto.getCollegeId())) {
            searchRequest.addQuery(new TermQuery("department_parent_id", orderRequestDto.getCollegeId()));
        }

        // 采购人名称
        if (StringUtils.isNotBlank(orderRequestDto.getBuyerName())) {
            searchRequest.addQuery(new PhraseQuery("fbuyername", orderRequestDto.getBuyerName()));
        }

        // 课题组负责人名称
        if (StringUtils.isNotBlank(orderRequestDto.getDepartmentManagerName())) {
            searchRequest.addQuery(new PhraseQuery("fbuydepartment", orderRequestDto.getDepartmentManagerName()));
        }

        // 供应商id
        if (Objects.nonNull(orderRequestDto.getSuppId())) {
            searchRequest.addQuery(new TermQuery("fsuppid", orderRequestDto.getSuppId()));
        }

        // exclude排除供应商ID
        if (CollectionUtils.isNotEmpty(orderRequestDto.getExcludeSuppIdList())) {
            searchRequest.addNotFilter(new TermFilter("fsuppid", orderRequestDto.getExcludeSuppIdList()));
        }

        // 配送城市
        if (CollectionUtils.isNotEmpty(orderRequestDto.getCityNames())) {
            searchRequest.addFilter(new TermFilter("fbiderdeliveryplace", orderRequestDto.getCityNames()));
        }

        AndQuery fundCardAndQuery = new AndQuery();
        // 完整经费卡号
        if (StringUtils.isNotBlank(orderRequestDto.getFundCardNo())) {
            fundCardAndQuery.addQuery(new TermQuery("card.card_no", orderRequestDto.getFundCardNo()));
        }

        // 通配符类型经费卡号
        String fundCardNoWildCard = orderRequestDto.getFundCardNoWildCard();
        if (StringUtils.isNotBlank(fundCardNoWildCard)) {
            fundCardAndQuery.addQuery(new WildcardQuery("card.card_no", fundCardNoWildCard));
        }

        if (CollectionUtils.isNotEmpty(fundCardAndQuery.getQueryList())) {
            searchRequest.addQuery(new NestedQuery("card", fundCardAndQuery));
        }

        // 金额区间查询
        BigDecimal priceStart = orderRequestDto.getPriceStart();
        BigDecimal priceEnd = orderRequestDto.getPriceEnd();
        searchRequest.addFilter(new RangeFilter("forderamounttotal", Objects.isNull(priceStart) ? null : priceStart.toString(), Objects.isNull(priceEnd) ? null : priceEnd.toString(), true, true));
    }

    /**
     * 设置订单商品详情搜索条件
     * @param orderRequestDto
     * @param searchRequest
     */
    private void setOrderDetailSearchQuery(OrderRequestDTO orderRequestDto, Request searchRequest) {
        AndQuery orderDetailAndQuery = new AndQuery();

        // 管制品判断
        if (Objects.nonNull(orderRequestDto.getDangerousType())) {
            orderDetailAndQuery.addQuery(new TermQuery("order_detail.regulatory_type", orderRequestDto.getDangerousType()));
        }

        // 商品类型
        if (Objects.nonNull(orderRequestDto.getProductType())) {
            orderDetailAndQuery.addQuery(new TermQuery("order_detail.dangerous_type", orderRequestDto.getProductType()));
        }

        // 商品名称或货号
        if (StringUtils.isNotBlank(orderRequestDto.getProductSearch())) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new PhraseQuery("order_detail.fgoodname", orderRequestDto.getProductSearch()));
            orQuery.addQuery(new PhraseQuery("order_detail.fgoodcode", orderRequestDto.getProductSearch()));
            orderDetailAndQuery.addQuery(orQuery);
        }

        // 危化品类型集合
        if (CollectionUtils.isNotEmpty(orderRequestDto.getDangerousTypeList())) {
            orderDetailAndQuery.addQuery(new TermQuery("order_detail.dangerous_type", orderRequestDto.getDangerousTypeList()));
        }

        if (CollectionUtils.isNotEmpty(orderDetailAndQuery.getQueryList())) {
            searchRequest.addQuery(new NestedQuery("order_detail", orderDetailAndQuery));
        }
    }
}
