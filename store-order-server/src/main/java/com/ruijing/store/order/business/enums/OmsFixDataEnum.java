package com.ruijing.store.order.business.enums;

/**
 * @author: ch<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @createTime: 2024-12-02 15:27
 * @description: 记录修正数据枚举
 * 不能包含com.ruijing.store.order.api.base.enums.OrderApprovalEnum 中修正数据枚举对应的值
 *
 **/
public enum OmsFixDataEnum {


    /**
     * 修改订单状态
     */
    FIX_ORDER_STATUS(46, "修改订单状态"),

    /**
     * 修改经费状态
     */
    FIX_FUND_STATUS(47 , "修改经费状态"),

    /**
     * 重新冻结经费
     */
    FUND_RE_FREEZE(48, "重新冻结经费"),

    /**
     * 重新冻结经费
     */
    FUND_RE_UNFREEZE(49, "重新解冻经费"),

    /**
     * 解冻经费
     */
    FUND_UNFREEZE(50, "解冻经费"),


    FIX_ORDER_ITEM_CATEGORY(100, "修改订单详情商品分类"),

    DELETE_TEST_ORDER(101, "删除测试订单"),

    FUND_FREEZE(102, "冻结经费"),

    ;


    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    OmsFixDataEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static OmsFixDataEnum getByValue(Integer code) {
        for (OmsFixDataEnum item : OmsFixDataEnum.values()) {
            if (item.getValue().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer code) {
        for (OmsFixDataEnum item : OmsFixDataEnum.values()) {
            if (item.getValue().equals(code)) {
                return item.getName();
            }
        }
        return "";
    }
}
