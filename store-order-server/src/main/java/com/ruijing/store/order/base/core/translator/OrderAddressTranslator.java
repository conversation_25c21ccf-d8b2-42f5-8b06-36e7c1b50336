package com.ruijing.store.order.base.core.translator;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderAddressDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderResultDTO;

public class OrderAddressTranslator {

    /**
     * 生成订单dto to 地址dto
     * @param dto
     * @return
     */
    public static OrderAddressDTO generateDTOToDTO(GenerateOrderResultDTO dto) {
        OrderAddressDTO result = new OrderAddressDTO();
        result.setId(dto.getOrderMasterId());
        result.setOrderNo(dto.getOrderMasterNumber());
        result.setReceiverName(dto.getOrderAddressDTO().getReceiverName());
        result.setReceiverPhone(dto.getOrderAddressDTO().getReceiverPhone());
        result.setProvince(dto.getOrderAddressDTO().getProvince());
        result.setCity(dto.getOrderAddressDTO().getCity());
        result.setRegion(dto.getOrderAddressDTO().getRegion());
        result.setAddress(dto.getOrderAddressDTO().getAddress());
        result.setLabel(dto.getOrderAddressDTO().getLabel());
        result.setGeo(dto.getOrderAddressDTO().getGeo());
        result.setReceiverNameProxy(dto.getOrderAddressDTO().getReceiverNameProxy());
        result.setReceiverPhoneProxy(dto.getOrderAddressDTO().getReceiverPhoneProxy());
        result.setProvinceProxy(dto.getOrderAddressDTO().getProvinceProxy());
        result.setCityProxy(dto.getOrderAddressDTO().getCityProxy());
        result.setRegionProxy(dto.getOrderAddressDTO().getRegionProxy());
        result.setAddressProxy(dto.getOrderAddressDTO().getAddressProxy());
        result.setGeoProxy(dto.getOrderAddressDTO().getGeoProxy());
        result.setDeliveryType(dto.getOrderAddressDTO().getDeliveryType());
        result.setProxySourceType(dto.getOrderAddressDTO().getProxySourceType());
        return result;
    }

    /**
     * 生成订单dto to 地址dto
     * @param dto
     * @return
     */
    public static GenerateOrderAddressDTO dtoToGenerateDTO(OrderAddressDTO dto) {
        GenerateOrderAddressDTO result = new GenerateOrderAddressDTO();
        if (dto == null) {
            return result;
        }
        result.setReceiverName(dto.getReceiverName());
        result.setReceiverPhone(dto.getReceiverPhone());
        result.setProvince(dto.getProvince());
        result.setCity(dto.getCity());
        result.setRegion(dto.getRegion());
        result.setAddress(dto.getAddress());
        result.setGeo(dto.getGeo());
        result.setReceiverNameProxy(dto.getReceiverNameProxy());
        result.setReceiverPhoneProxy(dto.getReceiverPhoneProxy());
        result.setProvinceProxy(dto.getProvinceProxy());
        result.setCityProxy(dto.getCityProxy());
        result.setRegionProxy(dto.getRegionProxy());
        result.setAddressProxy(dto.getAddressProxy());
        result.setGeoProxy(dto.getGeoProxy());
        result.setDeliveryType(dto.getDeliveryType());
        result.setProxySourceType(dto.getProxySourceType());
        return result;
    }
}
