package com.ruijing.store.order.business.handler;

import com.reagent.auth.api.pojo.dto.UserDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.custom.enums.ZhongShanDaXueOrgEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.message.api.dto.EmailSendRequestRpcDTO;
import com.ruijing.message.api.dto.LetterSendRequestRpcDTO;
import com.ruijing.message.api.dto.WeChatSendRequestRpcDTO;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.pearl.client.PearlClient;
import com.ruijing.store.cms.api.dto.SendingPersonalSettingDTO;
import com.ruijing.store.cms.api.enums.SendingBusinessEnum;
import com.ruijing.store.cms.api.enums.SendingWayEnum;
import com.ruijing.store.cms.api.request.SendingSettingParam;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.gateway.buyercenter.request.ModifyAddrRequestDTO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.user.rpc.enumation.BusinessIdEnum;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruijing.store.order.business.handler.OrderEmailHandler.*;
import static com.ruijing.store.order.business.handler.SysLetterHandler.purchaseManagerOrderReturnLink;

/**
 * @author: liwenyu
 * @createTime: 2023-11-08 15:42
 * @description:
 **/
@Service
public class OrderMessageHandler {

    private final Logger logger = LoggerFactory.getLogger(OrderMessageHandler.class);

    @PearlValue(key = "EMAIL_RETURN_WWW")
    private String GOODS_RETURN_HTTP;

    @Resource
    private SysuClient sysuClient;

    @Resource
    private MessageSystemClient messageSystemClient;

    @Resource
    private UserClient userClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private UserSyncClient userSyncClient;

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private CmsServerClient cmsServerClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    public void sendSuppApplyCancelMsg(OrderMasterDO orderMasterDO) {
        UserBaseInfoDTO userBaseInfoDTO = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
        int sceneId = Environment.isProdEnv() ? 9 : 10084;
        int bizId = BusinessIdEnum.STORE.getVal();
        String orderId = orderMasterDO.getId().toString();
        String orderNo = orderMasterDO.getForderno();
        Integer orgId = orderMasterDO.getFuserid();
        String suppName = orderMasterDO.getFsuppname();
        String reason = orderMasterDO.getFcancelreason();
        Map<String, Object> dataMap = New.map();
        dataMap.put("suppName", suppName);
        dataMap.put("reason", reason);
        dataMap.put("orderId", orderId);
        dataMap.put("orderNo", orderNo);

        try {
            LetterSendRequestRpcDTO letterSendRequestRpcDTO = new LetterSendRequestRpcDTO();
            letterSendRequestRpcDTO.setSceneId(sceneId);
            letterSendRequestRpcDTO.setBusinessId(bizId);
            letterSendRequestRpcDTO.setOrgId(orgId);
            letterSendRequestRpcDTO.setGuids(New.list(userBaseInfoDTO.getGuid()));
            letterSendRequestRpcDTO.setData(dataMap);
            messageSystemClient.sendLetter(letterSendRequestRpcDTO);
        } catch (Exception e) {
            logger.error("订单：{}发站内信失败", orderNo,e);
        }

        try {
            if (isBuyerEmailEnabled(userBaseInfoDTO.getGuid(), orgId, SendingBusinessEnum.CANCEL_ORDER)) {
                EmailSendRequestRpcDTO emailSendRequestRpcDTO = new EmailSendRequestRpcDTO();
                emailSendRequestRpcDTO.setSceneId(sceneId);
                emailSendRequestRpcDTO.setCc(New.list(userBaseInfoDTO.getEmail()));
                EmailSendRequestRpcDTO.ReceiverInfoRpcDTO receiverInfoRpcDTO = new EmailSendRequestRpcDTO.ReceiverInfoRpcDTO();
                receiverInfoRpcDTO.setOrgId(orgId);
                receiverInfoRpcDTO.setGuid(userBaseInfoDTO.getGuid());
                receiverInfoRpcDTO.setEmail(userBaseInfoDTO.getEmail());
                receiverInfoRpcDTO.setBusinessId(bizId);
                emailSendRequestRpcDTO.setReceivers(New.list(receiverInfoRpcDTO));
                emailSendRequestRpcDTO.setData(dataMap);
                messageSystemClient.sendEmail(emailSendRequestRpcDTO);
            }
        } catch (Exception e) {
            logger.error("订单：{}发email失败", orderNo,e);
        }
    }

    /**
     * 发送推送失败消息
     * @param orderMasterDO 订单数据
     */
    public void sendPushFailMsg(OrderMasterDO orderMasterDO){
        try {
            UserBaseInfoDTO userBaseInfoDTO = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
            int sceneId = Environment.isProdEnv() ? 61 : 10119;
            int bizId = BusinessIdEnum.STORE.getVal();
            String orderId = orderMasterDO.getId().toString();
            String orderNo = orderMasterDO.getForderno();
            String deptName = orderMasterDO.getFbuydepartment();
            Map<String, Object> dataMap = New.map();
            dataMap.put("deptName", deptName);
            dataMap.put("orderId", orderId);
            dataMap.put("orderNo", orderNo);
            this.sendEmailToBuyer(sceneId, orderMasterDO.getFbuyerid(), userBaseInfoDTO.getEmail(), orderMasterDO.getFuserid(), bizId, dataMap);
            this.sendLetter(sceneId, New.list(userBaseInfoDTO.getGuid()), orderMasterDO.getFuserid(), bizId, dataMap);
        } catch (Exception e) {
            logger.error("发送推送失败消息异常, orderId:{}, orderNo:{}",
                orderMasterDO.getId(), orderMasterDO.getForderno(), e);
        }
    }

    /**
     * %s单位的用户取消订单 微信消息
     * @param orderMasterDO
     */
    public void cancelWaitingDeliveryWechatToSupp(OrderMasterDO orderMasterDO){
        try {
            Integer suppId = orderMasterDO.getFsuppid();
            Integer orgId = orderMasterDO.getFuserid();
            int sceneId = Environment.isProdEnv() ? 13 : 10112;
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orderDate", DateUtils.format("yyyy/MM/dd", orderMasterDO.getForderdate()));
            dataMap.put("orderNo", orderMasterDO.getForderno());
            dataMap.put("orderAmount", orderMasterDO.getForderamounttotal());
            dataMap.put("orderId", orderMasterDO.getId());
            dataMap.put("orgName", orderMasterDO.getFusername());
            dataMap.put("userName", orderMasterDO.getFusername());
            dataMap.put("deptName", orderMasterDO.getFbuydepartment());
            dataMap.put("first", orderMasterDO.getFusername() + "的用户取消订单");
            dataMap.put("remark", "点击查看订单详情");
            sendWechatToSupp(sceneId, suppId, orgId, BusinessIdEnum.STORE.getVal(), dataMap);
        } catch (Exception e) {
            logger.error("发送取消订单微信消息异常, orderId:{}, orderNo:{}",
                orderMasterDO.getId(), orderMasterDO.getForderno(), e);
        }
    }

    /**
     * 采购人 申请取消订单, 给供应商发送提醒邮件
     * @param orderMasterDO
     */
    public void sendPurchaserApplyCancelEmailToSupplier(OrderMasterDO orderMasterDO) {
        try {
            Integer suppId = orderMasterDO.getFsuppid();
            Integer orgId = orderMasterDO.getFuserid();
            int sceneId = Environment.isProdEnv() ? 13 : 10112;
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orgName", orderMasterDO.getFusername());
            dataMap.put("userName", orderMasterDO.getFbuyername());
            dataMap.put("deptName", orderMasterDO.getFbuydepartment());
            String link = String.format(orderDetailLink, orderMasterDO.getId());
            dataMap.put("link", link);
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterId(orderMasterDO.getId());
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailsByMasterId(orderDetailReq);
            // 订单详情表格
            buildOrderInfo(dataMap, orderMasterDO, resultResponse.getData());
            sendEmailToSupp(sceneId, suppId, orgId, dataMap);
        } catch (Exception e) {
            logger.error("发送采购人申请取消订单邮件异常, orderId:{}, orderNo:{}",
                orderMasterDO.getId(), orderMasterDO.getForderno(), e);
        }
    }

    /**
     * 订单修改收货地址通知
     * @param masterBeforeModify
     * @param modifyAddrRequestDTO
     */
    public void sendSupplierEmailModifyAddr(OrderMasterSearchDTO masterBeforeModify, ModifyAddrRequestDTO modifyAddrRequestDTO){
        try {
            int sceneId = Environment.isProdEnv() ? 11 : 10087;
            // 链接
            String envPrefix = PearlClient.get("rj.domain.url");
            if (!envPrefix.endsWith("/")) {
                envPrefix = envPrefix + "/";
            }
            String link = envPrefix + "SP/orderDetail?id=" + masterBeforeModify.getId();
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("link", link);
            dataMap.put("oldAddress", masterBeforeModify.getFbuyercontactman() + "&nbsp;&nbsp;&nbsp;" + masterBeforeModify.getFbuyertelephone() + "&nbsp;&nbsp;&nbsp;" + masterBeforeModify.getFbiderdeliveryplace());
            dataMap.put("newAddress", modifyAddrRequestDTO.getConsignee() + "&nbsp;&nbsp;&nbsp;" + modifyAddrRequestDTO.getMobile() + "&nbsp;&nbsp;&nbsp;" + modifyAddrRequestDTO.getProvince()+modifyAddrRequestDTO.getCity()+modifyAddrRequestDTO.getAddress());
            buildOrderInfo(dataMap, masterBeforeModify);
            sendEmailToSupp(sceneId, masterBeforeModify.getFsuppid(), masterBeforeModify.getFuserid(), dataMap);
        } catch (Exception e) {
            logger.error("发送修改地址邮件异常, orderId:{}, orderNo:{}",
                masterBeforeModify.getId(), masterBeforeModify.getForderno(), e);
        }
    }

    /**
     * 订单取消通知
     * @param orderMasterDO
     * @param cancelManId
     */
    public void sendOrderCancelEmailToSupplier(OrderMasterDO orderMasterDO, String cancelManId){
        try {
            if (SYSTEM_AUTO_CANCEL_ID.equals(cancelManId)) {
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterId(orderMasterDO.getId());
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailsByMasterId(orderDetailReq);
            int sceneId = Environment.isProdEnv() ? 12 : 10088;
            // 链接
            String envPrefix = PearlClient.get("rj.domain.url");
            if (!envPrefix.endsWith("/")) {
                envPrefix = envPrefix + "/";
            }
            String link = envPrefix + "SP/orderDetail?id=" + orderMasterDO.getId();
            // 设置参数
            Map<String, Object> dataMap = New.map();
            // 订单详情表格
            buildOrderInfo(dataMap, orderMasterDO, resultResponse.getData());
            dataMap.put("orgName", orderMasterDO.getFusername());
            dataMap.put("deptName", orderMasterDO.getFbuydepartment());
            dataMap.put("userName", orderMasterDO.getFbuyername());
            dataMap.put("link", link);
            sendEmailToSupp(sceneId, orderMasterDO.getFsuppid(), orderMasterDO.getFuserid(), dataMap);
        } catch (Exception e) {
            logger.error("发送订单取消邮件异常, orderId:{}, orderNo:{}",
                orderMasterDO.getId(), orderMasterDO.getForderno(), e);
        }
    }

    /**
     * %s单位的用户取消订单
     * @param orderMasterDO
     */
    public void cancelNotConfirmWechatToSupp(OrderMasterDO orderMasterDO){
        try {
            Integer suppId = orderMasterDO.getFsuppid();
            Integer orgId = orderMasterDO.getFuserid();
            int sceneId = Environment.isProdEnv() ? 12 : 10088;
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orderDate", DateUtils.format("yyyy/MM/dd", orderMasterDO.getForderdate()));
            dataMap.put("orderNo", orderMasterDO.getForderno());
            dataMap.put("orderAmount", orderMasterDO.getForderamounttotal());
            dataMap.put("orderId", orderMasterDO.getId());
            dataMap.put("orgName", orderMasterDO.getFusername());
            dataMap.put("userName", orderMasterDO.getFusername());
            dataMap.put("deptName", orderMasterDO.getFbuydepartment());
            dataMap.put("first", orderMasterDO.getFusername() + "的用户取消订单");
            dataMap.put("remark", "点击查看订单详情");
            sendWechatToSupp(sceneId, suppId, orgId, BusinessIdEnum.STORE.getVal(), dataMap);
        } catch (Exception e) {
            logger.error("发送取消未确认订单微信消息异常, orderId:{}, orderNo:{}",
                orderMasterDO.getId(), orderMasterDO.getForderno(), e);
        }
    }

    /**
     * 给供应商 发送超时未确认取消订单 的提醒邮件
     * @param orderMasterDOS
     * @param overDay
     * @param sendDay
     */
    public void sendPurchaseApplyCancelWarnEmailToSupplier(List<OrderMasterDO> orderMasterDOS, long overDay, long sendDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer suppId = orderMasterDO.getFsuppid();
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 18 : 10089;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("orgName", orderMasterDO.getFusername());
                dataMap.put("userName", orderMasterDO.getFbuyername());
                dataMap.put("deptName", orderMasterDO.getFbuydepartment());
                dataMap.put("overDay", overDay);
                dataMap.put("sendDay", sendDay);
                String link = String.format(orderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToSupp(sceneId, suppId, orgId, dataMap);
            });
        });

    }

    /**
     * 给采购人 发送超时未确认取消订单 的提醒邮件
     * @param orderMasterDOS
     * @param overDay
     * @param sendDay
     */
    public void sendSupplierApplyCancelWarnEmailToBuyer(List<OrderMasterDO> orderMasterDOS, long overDay, long sendDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 21 : 10090;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("suppName", orderMasterDO.getFsuppname());
                dataMap.put("overDay", overDay);
                dataMap.put("sendDay", sendDay);
                String link = String.format(purchaserOrderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToBuyer(sceneId, orderMasterDO.getFbuyerid(), orderMasterDO.getFbuyeremail(), orgId, BusinessIdEnum.STORE.getVal(), dataMap);
            });
        });
    }

    /**
     * 给供应商 发送超时未确认订单的 订单自动取消邮件
     * @param orderMasterDOS
     * @param overDay
     */
    public void sendUnConfirmOrderCancelEmailToSupplier(List<OrderMasterDO> orderMasterDOS, long overDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer suppId = orderMasterDO.getFsuppid();
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 16 : 10091;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("orgName", orderMasterDO.getFusername());
                dataMap.put("userName", orderMasterDO.getFbuyername());
                dataMap.put("deptName", orderMasterDO.getFbuydepartment());
                dataMap.put("overDay", overDay);
                String link = String.format(orderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToSupp(sceneId, suppId, orgId, dataMap);
            });
        });
    }

    /**
     * 采购人 同意取消订单, 给供应商发送提醒邮件
     * @param orderMasterDO
     * @param cancelManId
     */
    public void sendPurchaserAgreeCancelOrderEmailToSupplier(OrderMasterDO orderMasterDO, String cancelManId) {
        if (SYSTEM_AUTO_CANCEL_ID.equals(cancelManId)) {
            return;
        }
        try {
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterId(orderMasterDO.getId());
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailsByMasterId(orderDetailReq);
            Integer suppId = orderMasterDO.getFsuppid();
            Integer orgId = orderMasterDO.getFuserid();
            int sceneId = Environment.isProdEnv() ? 14 : 10092;
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orgName", orderMasterDO.getFusername());
            String link = String.format(orderDetailLink, orderMasterDO.getId());
            dataMap.put("link", link);
            // 订单详情表格
            buildOrderInfo(dataMap, orderMasterDO, resultResponse.getData());
            sendEmailToSupp(sceneId, suppId, orgId, dataMap);
        } catch (Exception e) {
            logger.error("发送采购人同意取消订单邮件异常, orderNo:{}", orderMasterDO.getForderno(), e);
        }
    }

    /**
     * 给供应商 发送超时未确认订单 的提醒邮件
     * @param orderMasterDOS
     * @param overDay
     */
    public void sendUnConfirmOrderWarnEmailToSupplier(List<OrderMasterDO> orderMasterDOS, long overDay, long sendDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer suppId = orderMasterDO.getFsuppid();
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 15 : 10093;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("orgName", orderMasterDO.getFusername());
                dataMap.put("userName", orderMasterDO.getFbuyername());
                dataMap.put("deptName", orderMasterDO.getFbuydepartment());
                dataMap.put("overDay", overDay);
                dataMap.put("sendDay", sendDay);
                String link = String.format(orderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToSupp(sceneId, suppId, orgId, dataMap);
            });
        });
    }

    /**
     * 给采购人 发送超时未确认订单的取消邮件
     * @param orderMasterDOS
     * @param overDay
     */
    public void sendUnConfirmOrderCancelEmailToPurchase(List<OrderMasterDO> orderMasterDOS, long overDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 17 : 10094;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("userName", orderMasterDO.getFbuyername());
                dataMap.put("deptName", orderMasterDO.getFbuydepartment());
                dataMap.put("overDay", overDay);
                String link = String.format(purchaserOrderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToBuyer(sceneId, orderMasterDO.getFbuyerid(), orderMasterDO.getFbuyeremail(), orgId, BusinessIdEnum.STORE.getVal(), dataMap);
            });
        });
    }

    /**
     * 供应商超时未确认取消  采购人发起的取消订单 给供应商自动取消订单 提醒邮件
     * @param orderMasterDOS
     * @param overDay
     */
    public void sendPurchaseApplyCancelEmailToSupplier(List<OrderMasterDO> orderMasterDOS, long overDay){
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer suppId = orderMasterDO.getFsuppid();
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 19 : 10095;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("orgName", orderMasterDO.getFusername());
                dataMap.put("userName", orderMasterDO.getFbuyername());
                dataMap.put("deptName", orderMasterDO.getFbuydepartment());
                dataMap.put("overDay", overDay);
                String link = String.format(orderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToSupp(sceneId, suppId, orgId, dataMap);
            });
        });
    }

    /**
     * 供应商超时未确认取消  采购人发起的取消订单 给采购人自动取消订单 提醒邮件
     * @param orderMasterDOS
     * @param overDay
     */
    public void sendPurchaseApplyCancelEmailToPurchase(List<OrderMasterDO> orderMasterDOS, long overDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 20 : 10096;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("deptName", orderMasterDO.getFbuydepartment());
                dataMap.put("overDay", overDay);
                String link = String.format(purchaserOrderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToBuyer(sceneId, orderMasterDO.getFbuyerid(), orderMasterDO.getFbuyeremail(), orgId, BusinessIdEnum.STORE.getVal(), dataMap);
            });
        });
    }

    /**
     * 给采购人 发送采购人超时未确认取消订单，自动取消订单的邮件
     * @param orderMasterDOS
     * @param overDay
     */
    public void sendSupplierApplyCancelEmailToPurchase(List<OrderMasterDO> orderMasterDOS, long overDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 23 : 10097;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("suppName", orderMasterDO.getFsuppname());
                dataMap.put("overDay", overDay);
                String link = String.format(purchaserOrderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToBuyer(sceneId, orderMasterDO.getFbuyerid(), orderMasterDO.getFbuyeremail(), orgId, BusinessIdEnum.STORE.getVal(), dataMap);
            });
        });
    }

    /**
     * 给供应商 发送采购人超时未确认取消订单， 自动取消订单的邮件
     * @param orderMasterDOS
     * @param overDay
     */
    public void sendSupplierApplyCancelEmailToSupplier(List<OrderMasterDO> orderMasterDOS, long overDay) {
        AsyncExecutor.runAsync(() ->{
            if(CollectionUtils.isEmpty(orderMasterDOS)){
                return;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
            RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailByMasterIdList(orderDetailReq);
            Map<Integer, List<OrderDetailDTO>> orderDetailMap = resultResponse.getData().stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
            orderMasterDOS.forEach(orderMasterDO -> {
                Integer suppId = orderMasterDO.getFsuppid();
                Integer orgId = orderMasterDO.getFuserid();
                int sceneId = Environment.isProdEnv() ? 22 : 10098;
                // 设置参数
                Map<String, Object> dataMap = New.map();
                dataMap.put("orgName", orderMasterDO.getFusername());
                dataMap.put("userName", orderMasterDO.getFbuyername());
                dataMap.put("deptName", orderMasterDO.getFbuydepartment());
                dataMap.put("overDay", overDay);
                String link = String.format(orderDetailLink, orderMasterDO.getId());
                dataMap.put("link", link);
                // 订单详情表格
                buildOrderInfo(dataMap, orderMasterDO, orderDetailMap.get(orderMasterDO.getId()));
                sendEmailToSupp(sceneId, suppId, orgId, dataMap);
            });
        });
    }

    /**
     * 给供应商 发送采购人申请退货的邮件
     * @param goodsReturn
     * @param operatorName
     */
    public void sendApplyReturnEmailToSupplier(GoodsReturn goodsReturn, String operatorName) {
        try {
            int sceneId = Environment.isProdEnv() ? 27 : 10099;
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orgName", goodsReturn.getOrgName());
            dataMap.put("userName", operatorName);
            dataMap.put("deptName", goodsReturn.getDepartmentName());
            String link = String.format(returnOrderDetailLink, goodsReturn.getOrderId(), goodsReturn.getId());
            dataMap.put("link", link);
            String goodsReturnDetailJSON = goodsReturn.getGoodsReturnDetailJSON();
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturnDetailJSON);
            BigDecimal total = goodsReturnInfoDetailVOS.stream().map(GoodsReturnInfoDetailVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            dataMap.put("returnAmount", total);
            dataMap.put("returnNo", goodsReturn.getReturnNo());
            // 订单详情表格
            sendEmailToSupp(sceneId, goodsReturn.getSupplierId(), goodsReturn.getOrgId(), dataMap);
        } catch (Exception e) {
            logger.error("发送退货申请邮件异常, returnNo:{}", goodsReturn.getReturnNo(), e);
        }
    }

    /**
     * 异步发送课题组冻结催单邮件
     * @param departmentDTO
     * @param userBaseInfoDTOList
     * @param orders
     */
    public void sendEmailDepartmentFreezeNoticeToPurchaser(DepartmentDTO departmentDTO, List<UserBaseInfoDTO> userBaseInfoDTOList, List<OrderTimeOutDTO> orders) {
        AsyncExecutor.runAsync(() ->{
            Integer orgId = departmentDTO.getOrganizationId();
            int sceneId = Environment.isProdEnv() ? 33 : 10100;
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("deptName", departmentDTO.getName());
            dataMap.put("orderNos", orders.stream().map(OrderTimeOutDTO::getOrderNo).collect(Collectors.joining("，")));
            dataMap.put("orderCount", orders.size());
            dataMap.put("link", waitingBalanceOrderLink);
            userBaseInfoDTOList.forEach(userBaseInfoDTO -> {
                sendEmailToBuyer(sceneId, userBaseInfoDTO.getId(), userBaseInfoDTO.getEmail(), orgId, BusinessIdEnum.STORE.getVal(), dataMap);
            });
        });
    }

    /**
     * 发送供应商退货信息给采购人
     * 发送退货信息站内信给采购人
     * @param masterDO
     * @param goodsReturn
     */
    public void sendReturnGoodsMsgToBuyer(OrderMasterDO masterDO, GoodsReturn goodsReturn, UserBaseInfoDTO userBaseInfoDTO) {
        try {
            if (!GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(goodsReturn.getGoodsReturnStatus())) {
                return;
            }
            int sceneId = Environment.isProdEnv() ? 29 : 10101;
            Map<String, Object> dataMap = New.map();
            dataMap.put("suppName", masterDO.getFsuppname());
            dataMap.put("returnNo", goodsReturn.getReturnNo());
            dataMap.put("link", String.format(purchaseManagerOrderReturnLink, goodsReturn.getId()));
            sendLetter(sceneId, New.list(userBaseInfoDTO.getGuid()), masterDO.getFuserid(), BusinessIdEnum.STORE.getVal(), dataMap);
        } catch (Exception e) {
            logger.error("发送退货消息异常, orderId:{}, returnNo:{}",
                masterDO.getId(), goodsReturn.getReturnNo(), e);
        }
    }

    /**
     * 供应商同意或拒绝退货给采购人发消息
     * @param goodsReturn
     * @param goodsReturnInfoDetailVOS
     * @param userBaseInfoDTO
     * @param result
     * @param returnAmount
     */
    public void sendGoodsReturnEmailToBuyer(GoodsReturn goodsReturn, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS,
                                            UserBaseInfoDTO userBaseInfoDTO, String result, BigDecimal returnAmount) {
        try {
            int sceneId = Environment.isProdEnv() ? 28 : 10102;
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("result", result);
            dataMap.put("suppName", goodsReturn.getSupplierName());
            dataMap.put("price", returnAmount.setScale(2, RoundingMode.HALF_UP).toString());
            String url = ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(goodsReturn.getOrgId()) ?
                    sysuClient.getAppHostByOrgCode(ZhongShanDaXueOrgEnum.getByOrgId(goodsReturn.getOrgId()).getOrgCode()) + "/refunddetails/%s/false?comeFromCASLogin=1"
                    : GOODS_RETURN_HTTP;
            dataMap.put("link", String.format(url, goodsReturn.getId()));
            List<Map<String, Object>> detailMapList = New.list();
            for (GoodsReturnInfoDetailVO detail : goodsReturnInfoDetailVOS) {
                Map<String, Object> detailMap = New.map();
                detailMap.put("goodName", detail.getGoodsName());
                detailMap.put("brandName", detail.getBrand());
                detailMap.put("goodCode", detail.getGoodsCode());
                detailMap.put("unit", detail.getUnit());
                detailMapList.add(detailMap);
            }
            dataMap.put("orderDetail", detailMapList);
            sendEmailToBuyer(sceneId, userBaseInfoDTO.getId(), userBaseInfoDTO.getEmail(), goodsReturn.getOrgId(), BusinessIdEnum.STORE.getVal(), dataMap);
        } catch (Exception e) {
            logger.error("发送退货邮件异常, returnNo:{}", goodsReturn.getReturnNo(), e);
        }
    }

    /**
     * 提醒供应商发货 （邮件和站内信）
     *
     * @param orderMasterDO 订单信息
     */
    public void sendDeliveryRemindMessageToSupplier(OrderMasterDO orderMasterDO) {
        AsyncExecutor.runAsync(() -> {
            Integer sceneId = Environment.isProdEnv() ? 87 : 10175;
            Integer suppId = orderMasterDO.getFsuppid();
            Integer orgId = orderMasterDO.getFuserid();
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orderNo", orderMasterDO.getForderno());
            dataMap.put("orgName", orderMasterDO.getFusername());
            dataMap.put("deptName", orderMasterDO.getDeptParentName());
            dataMap.put("orderAmount", orderMasterDO.getForderamounttotal());
            dataMap.put("link", String.format(orderDetailLink, orderMasterDO.getId()));
            sendEmailToSupp(sceneId, suppId, orgId, dataMap);
            sendLetterToSupp(sceneId, suppId, orgId, dataMap);
        });
    }

    /**
     * 提醒供应商确认订单 （邮件和站内信）
     *
     * @param orderMasterDO 订单信息
     */
    public void sendConfirmRemindMessageToSupplier(OrderMasterDO orderMasterDO) {
        AsyncExecutor.runAsync(() -> {
            Integer sceneId = Environment.isProdEnv() ? 88 : 10176;
            Integer suppId = orderMasterDO.getFsuppid();
            Integer orgId = orderMasterDO.getFuserid();
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orderNo", orderMasterDO.getForderno());
            dataMap.put("orgName", orderMasterDO.getFusername());
            dataMap.put("deptName", orderMasterDO.getDeptParentName());
            dataMap.put("orderAmount", orderMasterDO.getForderamounttotal());
            dataMap.put("link", String.format(orderDetailLink, orderMasterDO.getId()));
            sendEmailToSupp(sceneId, suppId, orgId, dataMap);
            sendLetterToSupp(sceneId, suppId, orgId, dataMap);
        });
    }


    public void sendNoticeClaimAfterAcceptForChongQingYiKe(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList){
        int sceneId = Environment.isProdEnv() ? 118 : 10208;
        Integer orgId = orderMasterDO.getFuserid();
        List<UserBaseInfoDTO> buyerList = userClient.getUserByIdsAndOrgId(New.list(orderMasterDO.getFbuyerid()), orderMasterDO.getFuserid());
        if(CollectionUtils.isEmpty(buyerList)){
            return;
        }

        int showMaxSize = 50;
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setBuyerIdList(New.list(orderMasterDO.getFbuyerid()));
        orderSearchParamDTO.setOrgIdList(New.list(orderMasterDO.getFuserid()));
        orderSearchParamDTO.setInventoryStatusList(New.list(InventoryStatusEnum.COMPLETE.getCode()));
        orderSearchParamDTO.addSourceField("forderno");
        orderSearchParamDTO.addSourceField("id");
        orderSearchParamDTO.setStartHit(0);
        orderSearchParamDTO.setPageSize(showMaxSize);
        SearchPageResultDTO<OrderMasterSearchDTO> pageResultDTO = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        Long totalCount = pageResultDTO.getTotalHits();

        Map<String, Object> emailOrLetterDataMap = New.map();
        // 数据刷新到ES有延迟，采用set获取总条数
        Set<String> orderNoSet = New.set(orderMasterDO.getForderno());
        for(OrderMasterSearchDTO item : pageResultDTO.getRecordList()){
            orderNoSet.add(item.getForderno());
        }
        // 如果>50，则直接认为同步延迟，做+1处理。否则按照set数量处理
        emailOrLetterDataMap.put("orderCount", totalCount > 50 ? totalCount + 1 : orderNoSet.size());
        emailOrLetterDataMap.put("orderNoListStr", String.join(",", orderNoSet));
        this.sendEmailToBuyer(sceneId, buyerList.get(0), buyerList.get(0).getEmail(), orgId, BusinessIdEnum.STORE.getVal(), emailOrLetterDataMap);
        this.sendLetter(sceneId, New.list(buyerList.get(0).getGuid()), orgId, BusinessIdEnum.STORE.getVal(), emailOrLetterDataMap);

        Map<String, Object> wechatDataMap = New.map();
        wechatDataMap.put("orderId", orderMasterDO.getId());
        wechatDataMap.put("orderNo", orderMasterDO.getForderno());
        wechatDataMap.put("suppName", orderMasterDO.getFsuppname());
        wechatDataMap.put("deliveryDate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderMasterDO.getFdeliverydate()));
        wechatDataMap.put("productInfo", orderDetailDOList.stream().map(OrderDetailDO::getFgoodname).collect(Collectors.joining(",")));
        wechatDataMap.put("buyerName", orderMasterDO.getFbuyername());

        List<UserDTO> userDTOList = userSyncClient.getUsersByGuid(New.list(buyerList.get(0).getGuid()));
        if(CollectionUtils.isNotEmpty(userDTOList)){
            this.sendWechatToBuyer(sceneId, userDTOList, orgId, wechatDataMap);
        }
    }

    public void sendNoticeClaimScheduleForChongQingYiKe(){
        Integer orgId = OrgEnum.ZHONG_QING_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue();
        int sceneId = Environment.isProdEnv() ? 118 : 10208;
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setOrgIdList(New.list(orgId));
        orderSearchParamDTO.setInventoryStatusList(New.list(InventoryStatusEnum.COMPLETE.getCode()));
        orderSearchParamDTO.addSourceField("forderno");
        orderSearchParamDTO.addSourceField("id");
        orderSearchParamDTO.addSourceField("fbuyerid");
        orderSearchParamDTO.addSourceField("fsuppname");
        orderSearchParamDTO.addSourceField("fdeliverydate");
        orderSearchParamDTO.addSourceField("order_detail.fgoodname");
        orderSearchParamDTO.addSourceField("fbuyername");
        orderSearchParamDTO.setStartHit(0);
        orderSearchParamDTO.setPageSize(500);
        SearchPageResultDTO<OrderMasterSearchDTO> pageResultDTO = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        if(CollectionUtils.isEmpty(pageResultDTO.getRecordList())){
            return;
        }
        Map<Integer, List<OrderMasterSearchDTO>> buyerIdMasterMap = DictionaryUtils.groupBy(pageResultDTO.getRecordList(), OrderMasterSearchDTO::getFbuyerid);
        for(Map.Entry<Integer, List<OrderMasterSearchDTO>> entry : buyerIdMasterMap.entrySet()){
            Integer buyerId = entry.getKey();
            List<UserBaseInfoDTO> buyerList = userClient.getUserByIdsAndOrgId(New.list(buyerId), orgId);
            if(CollectionUtils.isEmpty(buyerList)){
                continue;
            }
            Map<String, Object> emailOrLetterDataMap = New.map();
            List<String> orderNoList = New.listWithCapacity(entry.getValue().size());
            for(OrderMasterSearchDTO item : entry.getValue()){
                orderNoList.add(item.getForderno());
            }
            emailOrLetterDataMap.put("orderCount", entry.getValue().size());
            emailOrLetterDataMap.put("orderNoListStr", String.join(",", orderNoList));
            this.sendEmailToBuyer(sceneId, buyerList.get(0), buyerList.get(0).getEmail(), orgId, BusinessIdEnum.STORE.getVal(), emailOrLetterDataMap);
            this.sendLetter(sceneId, New.list(buyerList.get(0).getGuid()), orgId, BusinessIdEnum.STORE.getVal(), emailOrLetterDataMap);

            List<UserDTO> userDTOList = userSyncClient.getUsersByGuid(New.list(buyerList.get(0).getGuid()));
            if(CollectionUtils.isEmpty(userDTOList)){
                continue;
            }
            for(OrderMasterSearchDTO orderMasterSearchDTO : entry.getValue()){
                Map<String, Object> wechatDataMap = New.map();
                wechatDataMap.put("orderId", orderMasterSearchDTO.getId());
                wechatDataMap.put("orderNo", orderMasterSearchDTO.getForderno());
                wechatDataMap.put("suppName", orderMasterSearchDTO.getFsuppname());
                wechatDataMap.put("deliveryDate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderMasterSearchDTO.getFdeliverydate()));
                wechatDataMap.put("buyerName", orderMasterSearchDTO.getFbuyername());
                wechatDataMap.put("productInfo", orderMasterSearchDTO.getOrderDetail().stream().map(OrderDetailSearchDTO::getFgoodname).collect(Collectors.joining(",")));
                this.sendWechatToBuyer(sceneId, userDTOList, orgId, wechatDataMap);
            }
        }

    }

    /**
     * 取消发货通知采购人 (邮件和站内信)
     *
     * @param orderMasterDO 订单信息
     */
    public void sendCancelDeliveryToBuyer(OrderMasterDO orderMasterDO, String reason) {
        AsyncExecutor.runAsync(() -> {
            Integer sceneId = Environment.isProdEnv() ? 119 : 10221;
            Integer orgId = orderMasterDO.getFuserid();
            // 设置参数
            Map<String, Object> dataMap = New.map();
            dataMap.put("orderNo", orderMasterDO.getForderno());
            dataMap.put("suppName", orderMasterDO.getFsuppname());
            dataMap.put("reason", reason);
            UserBaseInfoDTO buyerInfo = userClient.getUserDetailByID(orderMasterDO.getFbuyerid());
            if (Objects.isNull(buyerInfo)) {
                logger.error("取消发货通知采购人失败,查询不到用户信息,orderNo:{},buyerId:{}", orderMasterDO.getForderno(), orderMasterDO.getFbuyerid());
                return;
            }
            sendEmailToBuyer(sceneId, buyerInfo, buyerInfo.getEmail(),
                    orgId, BusinessIdEnum.STORE.getVal(), dataMap);
            sendLetter(sceneId, New.list(buyerInfo.getGuid()),
                    orgId, BusinessIdEnum.STORE.getVal(), dataMap);
        });
    }


    private void buildOrderInfo(Map<String, Object> dataMap, OrderMasterDO orderMasterDO, List<OrderDetailDTO> orderDetailDTOList){
        if(CollectionUtils.isEmpty(orderDetailDTOList)){
            return;
        }
        dataMap.put("orderDate", DateUtils.format("yyyy/MM/dd", orderMasterDO.getForderdate()));
        dataMap.put("orderNo", orderMasterDO.getForderno());
        dataMap.put("orderAmount", orderMasterDO.getForderamounttotal());
        List<Map<String, Object>> detailMapList = New.list();
        for (OrderDetailDTO detail : orderDetailDTOList) {
            Map<String, Object> detailMap = New.map();
            detailMap.put("goodName", detail.getFgoodname());
            detailMap.put("brandName", detail.getFbrand());
            detailMap.put("goodCode", detail.getFgoodcode());
            detailMap.put("unit", detail.getFunit());
            detailMap.put("price", detail.getFbidprice());
            detailMapList.add(detailMap);
        }
        dataMap.put("orderDetail", detailMapList);
    }

    private void buildOrderInfo(Map<String, Object> dataMap, OrderMasterSearchDTO masterBeforeModify){
        dataMap.put("orderDate", DateUtils.format("yyyy/MM/dd", DateUtils.parse(masterBeforeModify.getForderdate())));
        dataMap.put("orderNo", masterBeforeModify.getForderno());
        dataMap.put("orderAmount", masterBeforeModify.getForderamounttotal());
        List<Map<String, Object>> detailMapList = New.list();
        for (OrderDetailSearchDTO detail : masterBeforeModify.getOrderDetail()) {
            Map<String, Object> detailMap = New.map();
            detailMap.put("goodName", detail.getFgoodname());
            detailMap.put("brandName", detail.getFbrand());
            detailMap.put("goodCode", detail.getFgoodcode());
            detailMap.put("unit", detail.getFunit());
            detailMap.put("price", detail.getFbidprice());
            detailMapList.add(detailMap);
        }
        dataMap.put("orderDetail", detailMapList);
    }


        /**
         * 异步发送微信消息
         * @param sceneId
         * @param suppId
         * @param orgId
         * @param bizId
         * @param dataMap
         */
    private void sendWechatToSupp(Integer sceneId, Integer suppId, Integer orgId, Integer bizId, Map<String, Object> dataMap){
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOS = suppClient.getSuppliersOrgBusinessUser(suppId, orgId);
        List<String> guids = suppUserDTOS.stream().map(com.ruijing.shop.crm.api.pojo.dto.UserDTO::getGuid).collect(Collectors.toList());
        List<UserDTO> userDTOList = userSyncClient.getUsersByGuid(guids);
        if(CollectionUtils.isEmpty(userDTOList)){
            return;
        }
        WeChatSendRequestRpcDTO weChatSendRequestRpcDTO = new WeChatSendRequestRpcDTO();
        weChatSendRequestRpcDTO.setData(dataMap);
        weChatSendRequestRpcDTO.setReceivers(userDTOList.stream().map(userDTO -> {
            WeChatSendRequestRpcDTO.ReceiverInfoRpcDTO receiverInfoRpcDTO = new WeChatSendRequestRpcDTO.ReceiverInfoRpcDTO();
            receiverInfoRpcDTO.setBusinessId(bizId);
            receiverInfoRpcDTO.setGuid(userDTO.getGuid());
            receiverInfoRpcDTO.setOpenId(userDTO.getOpenId());
            receiverInfoRpcDTO.setOrgId(orgId);
            receiverInfoRpcDTO.setSuppId(suppId);
            return receiverInfoRpcDTO;
        }).collect(Collectors.toList()));
        weChatSendRequestRpcDTO.setSceneId(sceneId);
        messageSystemClient.sendWeChat(weChatSendRequestRpcDTO);
    }

    private void sendWechatToBuyer(Integer sceneId, List<UserDTO> userDTOList, Integer orgId, Map<String, Object> dataMap){
        if(CollectionUtils.isEmpty(userDTOList)){
            return;
        }
        WeChatSendRequestRpcDTO weChatSendRequestRpcDTO = new WeChatSendRequestRpcDTO();
        weChatSendRequestRpcDTO.setData(dataMap);
        weChatSendRequestRpcDTO.setReceivers(userDTOList.stream().map(userDTO -> {
            WeChatSendRequestRpcDTO.ReceiverInfoRpcDTO receiverInfoRpcDTO = new WeChatSendRequestRpcDTO.ReceiverInfoRpcDTO();
            receiverInfoRpcDTO.setBusinessId(BusinessIdEnum.STORE.getVal());
            receiverInfoRpcDTO.setGuid(userDTO.getGuid());
            receiverInfoRpcDTO.setOpenId(userDTO.getOpenId());
            receiverInfoRpcDTO.setOrgId(orgId);
            return receiverInfoRpcDTO;
        }).collect(Collectors.toList()));
        weChatSendRequestRpcDTO.setSceneId(sceneId);
        messageSystemClient.sendWeChat(weChatSendRequestRpcDTO);
    }

    /**
     * 异步发送邮件给供应商
     * @param sceneId
     * @param suppId
     * @param orgId
     * @param dataMap
     */
    private void sendEmailToSupp(Integer sceneId, Integer suppId, Integer orgId, Map<String, Object> dataMap){
        // 找供应商业务员
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOList = suppClient.getSuppliersOrgBusinessUser(suppId, orgId);
        EmailSendRequestRpcDTO emailSendRequestRpcDTO = new EmailSendRequestRpcDTO();
        emailSendRequestRpcDTO.setSceneId(sceneId);
        emailSendRequestRpcDTO.setReceivers(suppUserDTOList.stream().map(suppUserDTO -> {
            EmailSendRequestRpcDTO.ReceiverInfoRpcDTO receiverInfoRpcDTO = new EmailSendRequestRpcDTO.ReceiverInfoRpcDTO();
            receiverInfoRpcDTO.setOrgId(orgId);
            receiverInfoRpcDTO.setSuppId(suppId);
            receiverInfoRpcDTO.setGuid(suppUserDTO.getGuid());
            receiverInfoRpcDTO.setEmail(suppUserDTO.getEmail());
            receiverInfoRpcDTO.setBusinessId(BusinessIdEnum.SUPP.getVal());
            return receiverInfoRpcDTO;
        }).collect(Collectors.toList()));
        emailSendRequestRpcDTO.setData(dataMap);
        messageSystemClient.sendEmail(emailSendRequestRpcDTO);
    }

    /**
     * 异步发送站内信给供应商
     *
     * @param sceneId 场景ID
     * @param suppId  供应商ID
     * @param orgId   组织ID
     * @param dataMap 参数
     */
    private void sendLetterToSupp(Integer sceneId, Integer suppId, Integer orgId, Map<String, Object> dataMap) {
        // 找供应商业务员
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOList = suppClient.getSuppliersOrgBusinessUser(suppId, orgId);
        List<String> suppUserGuids = suppUserDTOList.stream().map(com.ruijing.shop.crm.api.pojo.dto.UserDTO::getGuid).collect(Collectors.toList());
        LetterSendRequestRpcDTO letterSendRequestRpcDTO = new LetterSendRequestRpcDTO();
        letterSendRequestRpcDTO.setBusinessId(BusinessIdEnum.SUPP.getVal());
        letterSendRequestRpcDTO.setData(dataMap);
        letterSendRequestRpcDTO.setGuids(suppUserGuids);
        letterSendRequestRpcDTO.setOrgId(suppId);
        letterSendRequestRpcDTO.setSceneId(sceneId);
        messageSystemClient.sendLetter(letterSendRequestRpcDTO);
    }


    /**
     * 异步发送邮件给采购人
     * @param sceneId
     * @param buyerId
     * @param email
     * @param orgId
     * @param bizId
     * @param dataMap
     */
    private void sendEmailToBuyer(Integer sceneId, Integer buyerId, String email, Integer orgId, Integer bizId, Map<String, Object> dataMap){
        UserBaseInfoDTO userBaseInfoDTO = userClient.getUserDetailByID(buyerId);
        this.sendEmailToBuyer(sceneId, userBaseInfoDTO, email, orgId, bizId, dataMap);
    }

    private void sendEmailToBuyer(Integer sceneId, UserBaseInfoDTO userBaseInfoDTO, String email, Integer orgId, Integer bizId, Map<String, Object> dataMap){
        EmailSendRequestRpcDTO emailSendRequestRpcDTO = new EmailSendRequestRpcDTO();
        emailSendRequestRpcDTO.setSceneId(sceneId);
        EmailSendRequestRpcDTO.ReceiverInfoRpcDTO receiverInfoRpcDTO = new EmailSendRequestRpcDTO.ReceiverInfoRpcDTO();
        receiverInfoRpcDTO.setOrgId(orgId);
        receiverInfoRpcDTO.setGuid(userBaseInfoDTO.getGuid());
        receiverInfoRpcDTO.setEmail(email);
        receiverInfoRpcDTO.setBusinessId(bizId);
        emailSendRequestRpcDTO.setData(dataMap);
        emailSendRequestRpcDTO.setReceivers(New.list(receiverInfoRpcDTO));
        messageSystemClient.sendEmail(emailSendRequestRpcDTO);
    }

    private void sendLetter(Integer sceneId, List<String> guids, Integer orgId, Integer bizId, Map<String, Object> dataMap){
        LetterSendRequestRpcDTO letterSendRequestRpcDTO = new LetterSendRequestRpcDTO();
        letterSendRequestRpcDTO.setBusinessId(bizId);
        letterSendRequestRpcDTO.setData(dataMap);
        letterSendRequestRpcDTO.setGuids(guids);
        letterSendRequestRpcDTO.setOrgId(orgId);
        letterSendRequestRpcDTO.setSceneId(sceneId);
        messageSystemClient.sendLetter(letterSendRequestRpcDTO);
    }

    /**
     * 查询个人邮件开关配置
     */
    public Boolean isBuyerEmailEnabled(String guid, Integer orgId, SendingBusinessEnum businessEnum) {

        // 邮件开关暂不对 中大/中大深圳/中大采购物资平台 生效,默认开启
        Set<Integer> specialOrgIdSet = New.set(OrgEnum.ZHONG_SHAN_DA_XUE.getValue()
                , OrgEnum.ZHONG_SHAN_DA_XUE_SHEN_ZHEN.getValue()
                , OrgEnum.ZHONG_SHAN_DA_XUE_BAN_GONG.getValue());
        if (specialOrgIdSet.contains(orgId)) {
            return true;
        }

        List<SendingSettingParam> param = New.list();
        SendingSettingParam settingParam = new SendingSettingParam();
        settingParam.setGuid(guid);
        settingParam.setBusinessList(New.list(businessEnum.getValue()));
        settingParam.setWay(SendingWayEnum.EMAIL.getValue().byteValue());
        param.add(settingParam);
        List<SendingPersonalSettingDTO> personalSettingDTOList = cmsServerClient.listPersonSetting(param);
        // 查不到为默认打开
        if (CollectionUtils.isEmpty(personalSettingDTOList)) {
            return true;
        }
        Byte enable = personalSettingDTOList.get(0).getEnable();
        if (Objects.isNull(enable)) {
            return true;
        }
        Byte openConfig = 1;
        return Objects.equals(enable, openConfig);
    }
}
