package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import javax.persistence.Transient;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: zhukai
 * @date : 2020/3/16 10:33 上午
 * @description: 订单相关的一些配置项前端读取, 请一定记得在成员变量上加上@RpcModelProperty的value和id值
 */
public class OrderConfigsVO {

    /**
     * 经费卡是否必填 0：非必填 1：必填
     */
    @RpcModelProperty(value = "经费卡是否必填 0：非必填 1：必填", id = "MUST_HAVE_FUNDCARD")
    private String mustHaveFundCardConfig;

    /**
     * 验收审批配置 0：非验收审批  1：验收审批
     */
    @RpcModelProperty(value = "验收审批配置 0：费验收审批  1：验收审批", id = "ORG_ACCEPTANCE_APPROVAL_CONFIG")
    private String acceptanceApprovalConfig;

    /**
     * 签署采购合同：0 - 无需签署合同，1 - 提醒签署合同
     */
    @RpcModelProperty(value = "签署采购合同：0 - 无需签署合同，1 - 提醒签署合同", id = "REQUIRE_SIGN_PROCUREMENT_CONTRACT")
    private String requireContractConfig;

    /**
     * 订单采购合同金额
     */
    @RpcModelProperty(value = "订单采购合同金额", id = "ORDER_CONTRACT_THRESHOLD")
    private String orderContractThresholdConfig;

    @Transient
    private static Map<String, String> configCodeSetMethodInstance;

    public String getMustHaveFundCardConfig() {
        return mustHaveFundCardConfig;
    }

    public void setMustHaveFundCardConfig(String mustHaveFundCardConfig) {
        this.mustHaveFundCardConfig = mustHaveFundCardConfig;
    }

    public String getAcceptanceApprovalConfig() {
        return acceptanceApprovalConfig;
    }

    public void setAcceptanceApprovalConfig(String acceptanceApprovalConfig) {
        this.acceptanceApprovalConfig = acceptanceApprovalConfig;
    }

    public String getRequireContractConfig() {
        return requireContractConfig;
    }

    public void setRequireContractConfig(String requireContractConfig) {
        this.requireContractConfig = requireContractConfig;
    }

    public String getOrderContractThresholdConfig() {
        return orderContractThresholdConfig;
    }

    public void setOrderContractThresholdConfig(String orderContractThresholdConfig) {
        this.orderContractThresholdConfig = orderContractThresholdConfig;
    }

    @JsonIgnore
    public Map<String, String> getConfigCodeSetMethodMap() {
        if (configCodeSetMethodInstance != null) {
            return configCodeSetMethodInstance;
        }
        Field[] fields = this.getClass().getDeclaredFields();
        // 初始化field字典
        configCodeSetMethodInstance = new HashMap<>(fields.length);
        for (Field field : fields) {
            RpcModelProperty rpcModelProperty = field.getAnnotation(RpcModelProperty.class);
            if (rpcModelProperty != null) {
                String fieldName = field.getName();
                String setMethodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                configCodeSetMethodInstance.put(rpcModelProperty.id(), setMethodName);
            }
        }

        return configCodeSetMethodInstance;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderConfigsVO{");
        sb.append("mustHaveFundCardConfig='").append(mustHaveFundCardConfig).append('\'');
        sb.append(", acceptanceApprovalConfig='").append(acceptanceApprovalConfig).append('\'');
        sb.append(", requireContractConfig='").append(requireContractConfig).append('\'');
        sb.append(", orderContractThresholdConfig='").append(orderContractThresholdConfig).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
