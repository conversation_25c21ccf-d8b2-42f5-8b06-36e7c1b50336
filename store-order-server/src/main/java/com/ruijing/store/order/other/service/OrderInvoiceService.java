package com.ruijing.store.order.other.service;

import com.ruijing.store.order.gateway.buyercenter.request.CreateInvoiceDTO;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-05-09 10:18
 * @description:
 **/
public interface OrderInvoiceService {

    /**
     * 保存发票
     * @param createInvoiceDTOList
     * @param userId
     */
    void saveInvoice(List<CreateInvoiceDTO> createInvoiceDTOList, Long userId, Integer orgId, String accessCode);
}
