package com.ruijing.store.order.base.orderconfirm.service.impl;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import com.ruijing.store.order.base.orderconfirm.dto.OrderConfirmRecordDTO;
import com.ruijing.store.order.base.orderconfirm.dto.request.OrderConfirmForTheRecordRequest;
import com.ruijing.store.order.base.orderconfirm.dto.request.OrderConfirmRecordInfoRequest;
import com.ruijing.store.order.base.orderconfirm.enums.ConfirmRecordType;
import com.ruijing.store.order.base.orderconfirm.service.OrderConfirmForTheRecordService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.rpc.client.UserClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/8 21
 */
@Service
public class OrderConfirmForTheRecordServiceImpl implements OrderConfirmForTheRecordService {

    @Resource
    private OrderConfirmForTheRecordDOMapper orderConfirmForTheRecordDOMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private UserClient userClient;

    @Override
    @ServiceLog(description = "确认备案", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void confirmForTheRecord(RjSessionInfo rjSessionInfo, OrderConfirmForTheRecordRequest request) {
        Preconditions.notEmpty(request.getDtos(), "入参不可为空");
        List<Integer> orderIdList = request.getDtos().stream().map(OrderConfirmRecordDTO::getOrderId).collect(Collectors.toList());
        Map<Integer, OrderConfirmRecordDTO> orderConfirmForTheRecordDOMap = request.getDtos().stream().collect(Collectors.toMap(OrderConfirmRecordDTO::getOrderId, Function.identity()));
        List<OrderConfirmForTheRecordDO> orderConfirmForTheRecordDOList = orderConfirmForTheRecordDOMapper.findByOrderIdIn(orderIdList);
        BusinessErrUtil.isTrue(orderConfirmForTheRecordDOList.size() == orderConfirmForTheRecordDOMap.size(), ExecptionMessageEnum.SOME_ORDERS_NO_FILING_REQUIRED);
        Map<Integer,Integer> ordersDeptIdMap = orderMasterMapper.findByIdIn(orderIdList).stream().collect(Collectors.toMap(OrderMasterDO::getId, OrderMasterDO::getFbuydepartmentid));
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), true, ConfigConstant.CONFIG_CODE_CONFIRM_FOR_THE_RECORD);
        for(OrderConfirmForTheRecordDO record : orderConfirmForTheRecordDOList){
            BusinessErrUtil.isTrue(!record.getConfirm(), ExecptionMessageEnum.FILING_CONFIRMED_NO_NEED_AGAIN);
            BusinessErrUtil.isTrue(loginInfo.getDeptIdList().contains(ordersDeptIdMap.get(record.getOrderId())), ExecptionMessageEnum.NO_PERMISSION_TO_FILE_CONTACT_SUPPORT);
            OrderConfirmRecordDTO dto = orderConfirmForTheRecordDOMap.get(record.getOrderId());

            if (ConfirmRecordType.EASY_MAKE_DANGEROUS.getValue().equals(record.getType())){
                BusinessErrUtil.isTrue(null != dto
                        && CollectionUtils.isNotEmpty(dto.getPics())
                        && dto.getPics().size() <= 6, ExecptionMessageEnum.PRECURSOR_DRUG_ORDER_UPLOAD_LIMIT);
            }
            if (ConfirmRecordType.EASY_MAKE_BOMB.getValue().equals(record.getType())){
                BusinessErrUtil.isTrue(null != dto
                        && CollectionUtils.isEmpty(dto.getPics()), ExecptionMessageEnum.EXPLOSIVE_ORDER_NO_IMAGE_REQUIRED);
            }

            record.setConfirm(true);
            if (CollectionUtils.isNotEmpty(dto.getPics())) {
                record.setPics(String.join(";", dto.getPics()));
            }
            orderConfirmForTheRecordDOMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    @ServiceLog(description = "添加备案图片", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void addConfirmPics(RjSessionInfo rjSessionInfo, OrderConfirmForTheRecordRequest request) {
        Preconditions.notEmpty(request.getDtos(), "入参不可为空");
        List<Integer> orderIdList = request.getDtos().stream().map(OrderConfirmRecordDTO::getOrderId).collect(Collectors.toList());
        Map<Integer, OrderConfirmRecordDTO> orderConfirmForTheRecordDOMap = request.getDtos().stream().collect(Collectors.toMap(OrderConfirmRecordDTO::getOrderId, Function.identity()));
        List<OrderConfirmForTheRecordDO> orderConfirmForTheRecordDOList = orderConfirmForTheRecordDOMapper.findByOrderIdIn(orderIdList);
        BusinessErrUtil.isTrue(orderConfirmForTheRecordDOList.size() == orderConfirmForTheRecordDOMap.size(), ExecptionMessageEnum.SOME_ORDERS_NO_FILING_REQUIRED);
        Map<Integer,Integer> ordersDeptIdMap = orderMasterMapper.findByIdIn(orderIdList).stream().collect(Collectors.toMap(OrderMasterDO::getId, OrderMasterDO::getFbuydepartmentid));
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), true, ConfigConstant.CONFIG_CODE_CONFIRM_FOR_THE_RECORD);
        for(OrderConfirmForTheRecordDO record : orderConfirmForTheRecordDOList){
            BusinessErrUtil.isTrue(record.getConfirm(), ExecptionMessageEnum.ORDER_NOT_FILED_PLEASE_FILE_FIRST);
            BusinessErrUtil.isTrue(loginInfo.getDeptIdList().contains(ordersDeptIdMap.get(record.getOrderId())), ExecptionMessageEnum.NO_PERMISSION_ADD_IMAGE_CONTACT_SUPPORT);
            OrderConfirmRecordDTO dto = orderConfirmForTheRecordDOMap.get(record.getOrderId());
            BusinessErrUtil.isTrue(ConfirmRecordType.EASY_MAKE_DANGEROUS.getValue().equals(record.getType()) && StringUtils.isBlank(record.getAddPics()),
                    ExecptionMessageEnum.PRECURSOR_DRUG_ORDER_ADDITIONAL_UPLOAD);
            record.setAddPics(String.join(";", dto.getPics()));
            orderConfirmForTheRecordDOMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    @ServiceLog(description = "查询备案信息", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.READ)
    public OrderConfirmForTheRecordDTO confirmRecordInfo(RjSessionInfo rjSessionInfo, OrderConfirmRecordInfoRequest request) {
        List<OrderConfirmForTheRecordDO> orderConfirmForTheRecordDOList = orderConfirmForTheRecordDOMapper.findByOrderIdIn(New.list(request.getOrderId()));
        if(CollectionUtils.isEmpty(orderConfirmForTheRecordDOList)) {
            return null;
        }
        OrderConfirmForTheRecordDO orderConfirmForTheRecordDO = orderConfirmForTheRecordDOList.get(0);
        OrderConfirmForTheRecordDTO orderConfirmForTheRecordDTO = new OrderConfirmForTheRecordDTO();
        orderConfirmForTheRecordDTO.setId(orderConfirmForTheRecordDO.getId());
        orderConfirmForTheRecordDTO.setOrderId(orderConfirmForTheRecordDO.getOrderId());
        orderConfirmForTheRecordDTO.setPics(orderConfirmForTheRecordDO.getPics());
        orderConfirmForTheRecordDTO.setAddPics(orderConfirmForTheRecordDTO.getAddPics());
        return orderConfirmForTheRecordDTO;
    }
}
