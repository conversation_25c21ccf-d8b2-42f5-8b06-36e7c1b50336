package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @Author: Zeng <PERSON>
 * @Date: 2020/12/28 9:58
 */
@RpcModel("订单快照（这里字段先和采购一致，让前端用相同字段构建网页）")
public class OrderSnapshotVO implements Serializable {

    private static final long serialVersionUID = 6910904431470171006L;

    @RpcModelProperty(hidden = true)
    private Long id;

    @RpcModelProperty(value = "订单商品详情id")
    private Long detailId;

    @RpcModelProperty(value = "供应商id")
    private Integer suppId;

    @RpcModelProperty(value = "供应商名称")
    private String suppName;

    @RpcModelProperty(value = "供应商编码")
    private String suppCode;

    @RpcModelProperty(value = "供应商手机")
    private String suppMobile;

    @RpcModelProperty(value = "供应商固话")
    private String suppTelephone;

    @RpcModelProperty(value = "供应商QQ")
    private String suppQQ;

    @RpcModelProperty(value = "供应商是否入驻")
    private Boolean enter;

    @RpcModelProperty(value = "供应商是否已经资质认证")
    private Boolean licensed;

    @RpcModelProperty(value = "供应商资质图片")
    private String licensePic;

    @RpcModelProperty(value = "省")
    private String province;

    @RpcModelProperty(value = "市")
    private String city;

    @RpcModelProperty(value = "区")
    private String county;

    @RpcModelProperty(value = "供应商累计订单量")
    private Integer saleAmount;

    @RpcModelProperty(value = "品牌名称")
    private String brandName;

    @RpcModelProperty(value = "供应商品牌代理等级")
    private Integer suppBrandLevel;

    @RpcModelProperty(value = "商品名称")
    private String productName;

    @RpcModelProperty(value = "商品单价")
    private Double productPrice;

    @RpcModelProperty(value = "商品图片路径列表")
    private List<String> productPicPaths;

    @RpcModelProperty(value = "商品货号")
    private String goodCode;

    @RpcModelProperty(value = "商品单位")
    private String unit;

    @RpcModelProperty(value = "商品货期")
    private Integer deliveryTime;

    @RpcModelProperty(value = "商品CAS号码")
    private String casNumber;

    @RpcModelProperty(value = "商品运费")
    private Double carryFee;

    @RpcModelProperty(value = "商品规格")
    private String specification;

    @RpcModelProperty(value = "商品描述")
    private String description;

    @RpcModelProperty(value = "一级分类id")
    private Integer firstCategoryId;

    @RpcModelProperty(value = "一级分类名称")
    private String firstCategoryName;

    @RpcModelProperty(value = "二级分类id")
    private Integer secondCategoryId;

    @RpcModelProperty(value = "二级分类名称")
    private String secondCategoryName;

    @RpcModelProperty(value = "三级分类id")
    private Integer thirdCategoryId;

    @RpcModelProperty(value = "三级分类名称")
    private String thirdCategoryName;

    @RpcModelProperty(value = "管制品类型")
    private Integer regulatoryType;

    @RpcModelProperty(value = "管制品类型名称")
    private String regulatoryTypeName;

    @RpcModelProperty(value = "危化品类型")
    private Integer dangerousType;

    @RpcModelProperty(value = "危化品类型名称")
    private String dangerousTypeName;

    @RpcModelProperty(value = "创建数据的时间戳")
    private Long createTimeStamp;

    @RpcModelProperty(value = "采购数量")
    private Integer quantity;

    @RpcModelProperty(value = "商品名称副标题")
    private String subtitle;

    @RpcModelProperty(value = "商品详情广告区")
    private String advertisementUrl;

    @RpcModelProperty(value = "商家服务优势")
    private ProductTemplateDTO shopDetailsTemplate;

    @RpcModelProperty(value = "品牌优势")
    private ProductTemplateDTO brandDetailsTemplate;

    public Long getId() {
        return id;
    }

    public OrderSnapshotVO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getDetailId() {
        return detailId;
    }

    public OrderSnapshotVO setDetailId(Long detailId) {
        this.detailId = detailId;
        return this;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public OrderSnapshotVO setSuppId(Integer suppId) {
        this.suppId = suppId;
        return this;
    }

    public String getSuppName() {
        return suppName;
    }

    public OrderSnapshotVO setSuppName(String suppName) {
        this.suppName = suppName;
        return this;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public OrderSnapshotVO setSuppCode(String suppCode) {
        this.suppCode = suppCode;
        return this;
    }

    public String getSuppMobile() {
        return suppMobile;
    }

    public OrderSnapshotVO setSuppMobile(String suppMobile) {
        this.suppMobile = suppMobile;
        return this;
    }

    public String getSuppTelephone() {
        return suppTelephone;
    }

    public OrderSnapshotVO setSuppTelephone(String suppTelephone) {
        this.suppTelephone = suppTelephone;
        return this;
    }

    public String getSuppQQ() {
        return suppQQ;
    }

    public OrderSnapshotVO setSuppQQ(String suppQQ) {
        this.suppQQ = suppQQ;
        return this;
    }

    public Boolean getEnter() {
        return enter;
    }

    public OrderSnapshotVO setEnter(Boolean enter) {
        this.enter = enter;
        return this;
    }

    public Boolean getLicensed() {
        return licensed;
    }

    public OrderSnapshotVO setLicensed(Boolean licensed) {
        this.licensed = licensed;
        return this;
    }

    public String getLicensePic() {
        return licensePic;
    }

    public OrderSnapshotVO setLicensePic(String licensePic) {
        this.licensePic = licensePic;
        return this;
    }

    public String getProvince() {
        return province;
    }

    public OrderSnapshotVO setProvince(String province) {
        this.province = province;
        return this;
    }

    public String getCity() {
        return city;
    }

    public OrderSnapshotVO setCity(String city) {
        this.city = city;
        return this;
    }

    public String getCounty() {
        return county;
    }

    public OrderSnapshotVO setCounty(String county) {
        this.county = county;
        return this;
    }

    public Integer getSaleAmount() {
        return saleAmount;
    }

    public OrderSnapshotVO setSaleAmount(Integer saleAmount) {
        this.saleAmount = saleAmount;
        return this;
    }

    public String getBrandName() {
        return brandName;
    }

    public OrderSnapshotVO setBrandName(String brandName) {
        this.brandName = brandName;
        return this;
    }

    public Integer getSuppBrandLevel() {
        return suppBrandLevel;
    }

    public OrderSnapshotVO setSuppBrandLevel(Integer suppBrandLevel) {
        this.suppBrandLevel = suppBrandLevel;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public OrderSnapshotVO setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public Double getProductPrice() {
        return productPrice;
    }

    public OrderSnapshotVO setProductPrice(Double productPrice) {
        this.productPrice = productPrice;
        return this;
    }

    public List<String> getProductPicPaths() {
        return productPicPaths;
    }

    public OrderSnapshotVO setProductPicPaths(List<String> productPicPaths) {
        this.productPicPaths = productPicPaths;
        return this;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public OrderSnapshotVO setGoodCode(String goodCode) {
        this.goodCode = goodCode;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public OrderSnapshotVO setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public Integer getDeliveryTime() {
        return deliveryTime;
    }

    public OrderSnapshotVO setDeliveryTime(Integer deliveryTime) {
        this.deliveryTime = deliveryTime;
        return this;
    }

    public String getCasNumber() {
        return casNumber;
    }

    public OrderSnapshotVO setCasNumber(String casNumber) {
        this.casNumber = casNumber;
        return this;
    }

    public Double getCarryFee() {
        return carryFee;
    }

    public OrderSnapshotVO setCarryFee(Double carryFee) {
        this.carryFee = carryFee;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public OrderSnapshotVO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public OrderSnapshotVO setDescription(String description) {
        this.description = description;
        return this;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public OrderSnapshotVO setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
        return this;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public OrderSnapshotVO setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
        return this;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public OrderSnapshotVO setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
        return this;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public OrderSnapshotVO setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
        return this;
    }

    public Integer getThirdCategoryId() {
        return thirdCategoryId;
    }

    public OrderSnapshotVO setThirdCategoryId(Integer thirdCategoryId) {
        this.thirdCategoryId = thirdCategoryId;
        return this;
    }

    public String getThirdCategoryName() {
        return thirdCategoryName;
    }

    public OrderSnapshotVO setThirdCategoryName(String thirdCategoryName) {
        this.thirdCategoryName = thirdCategoryName;
        return this;
    }

    public Integer getRegulatoryType() {
        return regulatoryType;
    }

    public OrderSnapshotVO setRegulatoryType(Integer regulatoryType) {
        this.regulatoryType = regulatoryType;
        return this;
    }

    public String getRegulatoryTypeName() {
        return regulatoryTypeName;
    }

    public OrderSnapshotVO setRegulatoryTypeName(String regulatoryTypeName) {
        this.regulatoryTypeName = regulatoryTypeName;
        return this;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public OrderSnapshotVO setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
        return this;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public OrderSnapshotVO setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
        return this;
    }

    public Long getCreateTimeStamp() {
        return createTimeStamp;
    }

    public OrderSnapshotVO setCreateTimeStamp(Long createTimeStamp) {
        this.createTimeStamp = createTimeStamp;
        return this;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getAdvertisementUrl() {
        return advertisementUrl;
    }

    public void setAdvertisementUrl(String advertisementUrl) {
        this.advertisementUrl = advertisementUrl;
    }

    public ProductTemplateDTO getShopDetailsTemplate() {
        return shopDetailsTemplate;
    }

    public void setShopDetailsTemplate(ProductTemplateDTO shopDetailsTemplate) {
        this.shopDetailsTemplate = shopDetailsTemplate;
    }

    public ProductTemplateDTO getBrandDetailsTemplate() {
        return brandDetailsTemplate;
    }

    public void setBrandDetailsTemplate(ProductTemplateDTO brandDetailsTemplate) {
        this.brandDetailsTemplate = brandDetailsTemplate;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderSnapshotVO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("detailId=" + detailId)
                .add("suppId=" + suppId)
                .add("suppName='" + suppName + "'")
                .add("suppCode='" + suppCode + "'")
                .add("suppMobile='" + suppMobile + "'")
                .add("suppTelephone='" + suppTelephone + "'")
                .add("suppQQ='" + suppQQ + "'")
                .add("enter=" + enter)
                .add("licensed=" + licensed)
                .add("licensePic='" + licensePic + "'")
                .add("province='" + province + "'")
                .add("city='" + city + "'")
                .add("county='" + county + "'")
                .add("saleAmount=" + saleAmount)
                .add("brandName='" + brandName + "'")
                .add("suppBrandLevel=" + suppBrandLevel)
                .add("productName='" + productName + "'")
                .add("productPrice=" + productPrice)
                .add("productPicPaths=" + productPicPaths)
                .add("goodCode='" + goodCode + "'")
                .add("unit='" + unit + "'")
                .add("deliveryTime=" + deliveryTime)
                .add("casNumber='" + casNumber + "'")
                .add("carryFee=" + carryFee)
                .add("specification='" + specification + "'")
                .add("description='" + description + "'")
                .add("firstCategoryId=" + firstCategoryId)
                .add("firstCategoryName='" + firstCategoryName + "'")
                .add("secondCategoryId=" + secondCategoryId)
                .add("secondCategoryName='" + secondCategoryName + "'")
                .add("thirdCategoryId=" + thirdCategoryId)
                .add("thirdCategoryName='" + thirdCategoryName + "'")
                .add("regulatoryType=" + regulatoryType)
                .add("regulatoryTypeName='" + regulatoryTypeName + "'")
                .add("dangerousType=" + dangerousType)
                .add("dangerousTypeName='" + dangerousTypeName + "'")
                .add("createTimeStamp=" + createTimeStamp)
                .add("quantity=" + quantity)
                .add("subtitle='" + subtitle + "'")
                .add("advertisementUrl='" + advertisementUrl + "'")
                .add("shopDetailsTemplate=" + shopDetailsTemplate)
                .add("brandDetailsTemplate=" + brandDetailsTemplate)
                .toString();
    }
}
