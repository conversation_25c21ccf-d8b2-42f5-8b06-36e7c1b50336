package com.ruijing.store.order.base.orderconfirm.enums;


/**
 * <AUTHOR>
 * @Date Created in 9:44 AM 2018/11/8.
 * @Description
 * @Modified
 */
public enum ConfirmRecordType{
    /**
     * 易制毒类
     */
    EASY_MAKE_DANGEROUS(1,"易制毒类"),
    /**
     * 易制爆类
     */
    EASY_MAKE_BOMB(2,"易制爆类");

    private Integer value;
    private String desc;


    ConfirmRecordType(Integer value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
