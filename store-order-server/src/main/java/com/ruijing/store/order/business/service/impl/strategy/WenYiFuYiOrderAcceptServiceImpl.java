package com.ruijing.store.order.business.service.impl.strategy;

import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/8 15:42
 * @description 温医附一验收策略
 */
@Service(OrgConst.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN + OrderAcceptConstant.ACCEPT_SUFFIX)
public class WenYiFuYiOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    /**
     * 计算入库模式
     *
     * @param orderMasterDO    订单主表信息
     * @param receiptConfigMap 验收配置
     * @param orderDetailList  订单商品详情
     * @return 入库模式
     */
    @Override
    public InWarehouseModeEnum calculateInWarehouseMode(OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList) {
        // 温医附一非危化品无需入库
        if (orderDetailList.stream().anyMatch(detail -> detail.getDangerousTypeId() == null || DangerousTypeEnum.UN_DANGEROUS.getValue().equals(detail.getDangerousTypeId()))) {
            return InWarehouseModeEnum.NO_NEED;
        }
        return super.calculateInWarehouseMode(orderMasterDO, receiptConfigMap, orderDetailList);
    }
}
