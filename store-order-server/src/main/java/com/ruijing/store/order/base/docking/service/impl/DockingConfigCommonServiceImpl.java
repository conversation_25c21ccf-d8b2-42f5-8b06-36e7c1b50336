package com.ruijing.store.order.base.docking.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.reagent.order.enums.config.OldDateTypeEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.DockingConfigServiceClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/2 18:00
 * @description
 */
@Service
public class DockingConfigCommonServiceImpl implements DockingConfigCommonService {

    private final List<OuterBuyerDockingTypeEnum> GENERATE_ORDER_OLD_CONFIG_SET = New.list(OuterBuyerDockingTypeEnum.ORDER_PUSH);

    private final List<OuterBuyerDockingTypeEnum> ALL_ORDER_OLD_CONFIG_SET = New.list(OuterBuyerDockingTypeEnum.ORDER_PUSH, OuterBuyerDockingTypeEnum.ORDER_NO_PUSH);

    @Resource
    private DockingConfigServiceClient dockingConfigServiceClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Override
    public OrgDockingConfigDTO getConfig(String orgCode) {
        return dockingConfigServiceClient.getConfig(orgCode);
    }

    @Override
    public boolean getIfNeedOldPush(OrderMasterDO orderData, List<OuterBuyerDockingTypeEnum> outerBuyerDockingTypeEnumList) {
        // 如果启用了新推送策略，则不走旧推送（都通过监听CANAL进行获取订单事件进行推送，除退货）
        OrgDockingConfigDTO dockingConfigDTO = dockingConfigServiceClient.getConfig(orderData.getFusercode());
        if(!dockingConfigDTO.getEnable() && !dockingConfigDTO.getOldExist()){
            return dockingConfigServiceClient.getOuterBuyerSet(outerBuyerDockingTypeEnumList).contains(orderData.getFusercode());
        }
        return false;
    }

    @Override
    public boolean getIfNeedDocking(OrderMasterDO orderData, List<OrderDockingStrategyEnum> dockingStrategyEnumList){
        OrgDockingConfigDTO dockingConfigDTO = dockingConfigServiceClient.getConfig(orderData.getFusercode());
        // 兼容旧推送策略，不启用新对接配置则走旧配置
        if(!dockingConfigDTO.getEnable() && !dockingConfigDTO.getOldExist()){
            List<OuterBuyerDockingTypeEnum> getConfigParam = ALL_ORDER_OLD_CONFIG_SET;
            if(dockingStrategyEnumList.contains(OrderDockingStrategyEnum.PUSH_ORDER_AND_HANDLE_REQUEST)){
                getConfigParam = GENERATE_ORDER_OLD_CONFIG_SET;
            }
            boolean needDocking = dockingConfigServiceClient.getOuterBuyerSet(getConfigParam).contains(orderData.getFusercode());
            if(!needDocking){
                return false;
            }
            if(OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode().equals(orderData.getFusercode())){
                Date oldDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT ,OrderDateConstant.HUA_NAN_NONG_YE_DA_XUE_OLD_DOCKING_TIME);
                return !this.judgeOldOrderByBuyApp(orderData, oldDate);
            }
            return true;
        }
        // 新推送策略匹配逻辑，先看是否有匹配的策略，有才进行推送
        boolean needDocking = dockingConfigDTO.getStrategyEnumList().stream().anyMatch(dockingStrategyEnumList::contains);
        if(!needDocking){
            return false;
        }
        // 在看有没有旧单日期，有则判断是否为旧单
        return !this.isOldOrder(dockingConfigDTO, orderData);
    }

    /**
     * 判断是否旧单
     * @param dockingConfigDTO 对接配置
     * @param orderData 订单数据
     * @return 是否旧单
     */
    private boolean isOldOrder(OrgDockingConfigDTO dockingConfigDTO, OrderMasterDO orderData){
        // 在看有没有旧单日期，有则判断是否为旧单
        if(dockingConfigDTO.getOldDateConfigDTO() == null){
            return false;
        }
        Date oldDate = dockingConfigDTO.getOldDateConfigDTO().getOldDate();
        if(oldDate == null){
            return false;
        }
        Date orderDate = orderData.getForderdate();
        if(OldDateTypeEnum.ORDER.equals(dockingConfigDTO.getOldDateConfigDTO().getOldDateJudgeType())){
            if(orderDate != null){
                return orderDate.before(oldDate);
            }
            // 没传日期就去数据库取
            OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderData.getId());
            Preconditions.notNull(orderMasterDO, "没有找到匹配的订单");
            return orderMasterDO.getForderdate().before(oldDate);
        }
        return this.judgeOldOrderByBuyApp(orderData, oldDate);
    }

    @Override
    public boolean isNewDockingEnable(OrgDockingConfigDTO config, OrderMasterDO orderData, List<OrderDetailDO> orderDetailDOList) {
        if(!config.getEnable()){
            // 配置不启用或非新配置，返回false
            return false;
        }
        if(this.isOldOrder(config, orderData)){
            // 旧单 也不启用
            return false;
        }
        // 判断品类
        List<Integer> relatedCategory = config.getBaseConfig().getRelatedCategory();
        if(relatedCategory == null){
            return true;
        }
        orderDetailDOList = orderDetailDOList == null ? orderDetailMapper.findByFmasterid(orderData.getId()) : orderDetailDOList;
        return orderDetailDOList.stream().allMatch(detail->relatedCategory.contains(detail.getFirstCategoryId()) || relatedCategory.contains(detail.getSecondCategoryId()) || relatedCategory.contains(detail.getCategoryid()));
    }

    public List<Integer> getEnableNewDockingOrderList(OrgDockingConfigDTO config, List<OrderMasterSearchDTO> searchDTOList){
        if(!config.getEnable()){
            return New.emptyList();
        }
        Set<Integer> unMatchIdSet = New.set();
        Date oldDate;
        if(config.getOldDateConfigDTO() != null && (oldDate = config.getOldDateConfigDTO().getOldDate()) != null){
            if(OldDateTypeEnum.APPLICATION == config.getOldDateConfigDTO().getOldDateJudgeType()){
                Map<Integer, Integer> appIdOrderIdMap = DictionaryUtils.toMap(searchDTOList, OrderMasterSearchDTO::getFtbuyappid, OrderMasterSearchDTO::getId);
                List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseClient.findByMasterId(New.list(appIdOrderIdMap.keySet()));
                applicationMasterDTOList.forEach(applicationMasterDTO -> {
                    if(applicationMasterDTO.getCreateTime().before(oldDate)){
                        unMatchIdSet.add(appIdOrderIdMap.get(applicationMasterDTO.getId().intValue()));
                    }
                });
            } else {
                searchDTOList.forEach(orderMasterSearchDTO -> {
                    if(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, orderMasterSearchDTO.getForderdate()).before(oldDate)){
                        unMatchIdSet.add(orderMasterSearchDTO.getId());
                    }
                });
            }
        }
        List<Integer> relatedCategory = config.getBaseConfig().getRelatedCategory();
        if(relatedCategory != null){
            searchDTOList.forEach(orderMasterSearchDTO -> {
                boolean allMatchConfig = orderMasterSearchDTO.getOrderDetail().stream().allMatch(detail->relatedCategory.contains(detail.getFirstCategoryId()) || relatedCategory.contains(detail.getSecondCategoryId()) || relatedCategory.contains(detail.getCategoryId()));
                if(!allMatchConfig){
                    unMatchIdSet.add(orderMasterSearchDTO.getId());
                }
            });
        }
        return searchDTOList.stream().filter(searchDTO->!unMatchIdSet.contains(searchDTO.getId())).map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
    }

    @Override
    public boolean getIfOrgPushOrderAfterGenerate(String orgCode) {
        OrgDockingConfigDTO dockingConfigDTO = dockingConfigServiceClient.getConfig(orgCode);
        if(dockingConfigDTO.getEnable()){
            return OmsDockingConfigValueEnum.PUSH_ORDER.name().equals(dockingConfigDTO.getBidDockingConfigDTO().getBidPushOrderData()) || OmsDockingConfigValueEnum.PUSH_ORDER.name().equals(dockingConfigDTO.getAppDockingConfigDTO().getAppPushOrderData());
        } else if(dockingConfigDTO.getOldExist()){
            return dockingConfigDTO.getStrategyEnumList().contains(OrderDockingStrategyEnum.BID_PUSH_ORDER) || dockingConfigDTO.getStrategyEnumList().contains(OrderDockingStrategyEnum.APP_PUSH_ORDER);
        }
        return dockingConfigServiceClient.getOuterBuyerSet(GENERATE_ORDER_OLD_CONFIG_SET).contains(orgCode);
    }

    /**
     * 通过采购数据判断是否旧单
     * @param orderData 订单数据
     * @param oldDate 旧单日期
     * @return 是否旧单
     */
    private boolean judgeOldOrderByBuyApp(OrderMasterDO orderData, Date oldDate){
        Integer buyAppId = orderData.getFtbuyappid();
        // 没有就去数据库取
        if(buyAppId == null){
            OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderData.getId());
            buyAppId = orderMasterDO.getFtbuyappid();
        }
        ApplicationMasterDTO applicationMasterDTO =  applicationBaseClient.getApplicationMasterByApplyId(buyAppId, false);
        return applicationMasterDTO.getCreateTime().before(oldDate);
    }
}
