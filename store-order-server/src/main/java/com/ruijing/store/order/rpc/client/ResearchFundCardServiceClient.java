package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.api.dto.PageInfo;
import com.reagent.research.fundcard.api.FundCardEnumService;
import com.reagent.research.fundcard.api.FundCardRPCService;
import com.reagent.research.fundcard.api.authority.FundCardAuthApproveRPCService;
import com.reagent.research.fundcard.api.authority.dto.AuthApproveChangeAmountDTO;
import com.reagent.research.fundcard.api.budget.FundCardBindRPCService;
import com.reagent.research.fundcard.api.budget.dto.BindDTO;
import com.reagent.research.fundcard.api.rule.FundCardRuleRPCService;
import com.reagent.research.fundcard.api.rule.FundPurchaseRuleRPCService;
import com.reagent.research.fundcard.api.rule.dto.FundCardAmountDTO;
import com.reagent.research.fundcard.api.rule.dto.FundCardPurchaseRuleCheckDTO;
import com.reagent.research.fundcard.api.rule.dto.FundCardUsableDTO;
import com.reagent.research.fundcard.api.simple.FundCardSimpleService;
import com.reagent.research.fundcard.dto.*;
import com.reagent.research.fundcard.dto.v2.EmployeeRequestDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.enums.FundCardScopeEnum;
import com.reagent.research.fundcard.enums.SourceTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description：新经费卡服务客户端
 * @date ：Created in 2019/12/30
 */
@ServiceClient
public class ResearchFundCardServiceClient {

    @MSharpReference(remoteAppkey = "research-fundcard-service")
    private FundCardRPCService fundCardRPCService;

    @MSharpReference(remoteAppkey = "research-fundcard-service")
    private FundCardRuleRPCService fundCardRuleRPCService;

    @MSharpReference(remoteAppkey = "research-fundcard-service")
    private FundCardBindRPCService fundCardBindRPCService;

    @MSharpReference(remoteAppkey = "research-fundcard-service")
    private FundCardEnumService fundCardEnumService;

    @MSharpReference(remoteAppkey = "research-fundcard-service")
    private FundCardSimpleService fundCardSimpleService;

    @MSharpReference
    private FundCardAuthApproveRPCService fundCardAuthApproveRpcService;

    @MSharpReference
    private FundPurchaseRuleRPCService fundPurchaseRuleRPCService;

    /**
     * 根据第一层经费卡号查询经费卡的树信息
     *
     * @param cardNos 第一层经费卡号
     * @param orgCode 医院code
     * @return 经费卡的树信息
     */
    @ServiceLog(description = "根据第一层经费卡号查询经费卡的树信息", serviceType = ServiceType.RPC_CLIENT)
    public List<FundCardDTO> getFundCardListByCardNos(List<String> cardNos, String orgCode) {
        OrgRequest<List<String>> request = new OrgRequest<>();
        request.setData(cardNos);
        request.setOrgCode(orgCode);
        RemoteResponse<List<FundCardDTO>> response = fundCardRPCService.getFundCardListByCardNos(request);
        Preconditions.isTrue(response.isSuccess(), "根据第一层经费卡号查询经费卡的树信息");
        return response.getData();
    }

    @ServiceLog(description = "根据经费卡号获取全层级的经费卡", serviceType = ServiceType.COMMON_SERVICE)
    public List<FundCardDTO> listByCodes(String orgCode, List<String> cardNoList) {
        OrgRequest<List<String>> request = new OrgRequest<>();
        request.setOrgCode(orgCode);
        request.setData(cardNoList);
        List<Integer> scopeList = New.list(FundCardScopeEnum.PARENT.getScope(), FundCardScopeEnum.SUB.getScope());
        RemoteResponse<List<FundCardDTO>> response = fundCardSimpleService.listByCodes(request, scopeList);
        Preconditions.isTrue(response != null && response.isSuccess(), "获取全层级经费卡失败，request=" + request);
        return response.getData();
    }

    /**
     * 根据经费卡id获取经费卡信息
     *
     * @param cardIds 经费ids
     * @param orgCode 医院code
     * @return 经费卡的树信息
     */
    @ServiceLog(description = "根据经费卡id获取经费卡信息", serviceType = ServiceType.RPC_CLIENT)
    public List<FundCardDTO> getFundCardListByCardIds(List<String> cardIds, String orgCode) {
        OrgRequest<List<String>> request = new OrgRequest<>();
        request.setData(cardIds);
        request.setOrgCode(orgCode);
        RemoteResponse<List<FundCardDTO>> response = fundCardRPCService.getFundCardListByCardIds(request);
        Preconditions.isTrue(response.isSuccess(), "根据经费卡id获取经费卡信息失败");
        return response.getData();
    }


    /**
     * 通过卡id查询经费卡信息，查询单张经费卡信息
     *
     * @param cardId
     * @param orgCode
     * @return
     */
    public FundCardDTO getFundCardByCardId(String cardId, String orgCode) {
        Preconditions.isTrue(cardId != null, "校验经费卡失败，经费卡id为空");
        Preconditions.isTrue(orgCode != null, "校验经费卡失败，机构编码为空");
        List<FundCardDTO> fundCardList = getFundCardListByCardIds(New.list(cardId), orgCode);
        if (CollectionUtils.isEmpty(fundCardList)) {
            return null;
        }

        return fundCardList.get(0);
    }

    /**
     * 批量冻结经费
     *
     * @param request 请求参数
     * @return 是否发起冻结
     */
    @ServiceLog(description = "批量冻结经费", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public FundCardResultDTO fundCardFreezeBatch(OrgRequest<FreezeDTO> request) {
        Preconditions.notNull(request, "冻结经费失败，入参为空");
        RemoteResponse<FundCardResultDTO> response = fundCardRPCService.fundCardFreezeBatch(request);
        Preconditions.isTrue(response.isSuccess(), "批量冻结经费异常: " + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    /**
     * 批量解冻经费
     *
     * @param request 请求参数
     * @return 是否发起解冻
     */
    @ServiceLog(description = "预算系统批量解冻经费接口", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public FundCardResultDTO fundCardUnFreezeBatch(OrgRequest<UnfreezeDTO> request) {

        RemoteResponse<FundCardResultDTO> response = fundCardRPCService.fundCardUnFreezeBatch(request);
        Assert.isTrue(response.isSuccess(), "预算系统批量解冻经费接口异常" + JsonUtils.toJson(response.getMsg()));
        return response.getData();
    }

    /**
     * 检测经费卡是否充足
     *
     * @param fundCard      经费卡信息
     * @param freezeAmount 冻结金额
     * @return
     */
    public void checkFundCardSufficient(FundCardDTO fundCard, BigDecimal freezeAmount) {
        Preconditions.isTrue(freezeAmount != null, "校验经费卡失败，冻结金额为空");
        Preconditions.isTrue(fundCard != null, "校验经费卡失败，无效的经费卡");
        // 冻结金额比剩余金额大，则余额不足
        Preconditions.isTrue(freezeAmount.compareTo(fundCard.getBalanceAmount()) <= 0, "经费预算不足，请重新选择经费卡");
    }

    /**
     * 通过工号获取经费卡信息
     *
     * @param orgCode        医院code
     * @param piEmployeeNum  pi工号信息
     * @param buyerJobNumber 用户工号
     * @return 经费卡列表
     */
    @ServiceLog(description = "通过工号获取经费卡信息", serviceType = ServiceType.RPC_CLIENT)
    public List<FundCardDTO> listFundCardByJobNumber(String orgCode, List<String> piEmployeeNum, String buyerJobNumber, Integer pageNo, Integer pageSize) {
        List<FundCardDTO> result = null;
        OrgRequest<EmployeeRequestDTO> request = new OrgRequest<>();
        EmployeeRequestDTO employeeRequest = new EmployeeRequestDTO();
        employeeRequest.setPiEmployeeNum(piEmployeeNum);
        employeeRequest.setBuyerJobNumber(buyerJobNumber);
        request.setData(employeeRequest);
        request.setOrgCode(orgCode);
        request.setPageSize(pageSize);
        request.setPageNo(pageNo);
        RemoteResponse<PageInfo<FundCardDTO>> response = fundCardRPCService.getUsableFundCardListByBuyerJobNumber(request);

        result = Optional.ofNullable(response.getData()).orElse(new PageInfo<>()).getData();
        return result;
    }

    /**
     * 根据经费卡id查询所有层级经费卡信息
     *
     * @param cardIdList
     * @return
     */
    @ServiceLog(description = "根据经费卡id查询所有层级经费卡信息", serviceType = ServiceType.RPC_CLIENT)
    public List<FundCardDTO> findAllCardByOrgCodeAndCardId(String orgCode, List<String> cardIdList) {
        List<List<String>> partition = Lists.partition(cardIdList, 100);
        List<FundCardDTO> fundCardList = New.list();
        for (List<String> part : partition) {
            OrgRequest<List<String>> request = new OrgRequest<>();
            request.setOrgCode(orgCode);
            request.setData(New.list(part));
            RemoteResponse<List<FundCardDTO>> response = fundCardRPCService.getAllLevelFundCardListByCardIds(request);
            Preconditions.notNull(response, "查询经费卡异常！结果为空！");
            Preconditions.isTrue(response.isSuccess(), JsonUtils.toJson(response.getMsg()));
            fundCardList.addAll(response.getData());
        }
        return fundCardList;
    }

    /**
     * 根据经费卡id查询当前层级经费卡信息
     *
     * @param orgCode
     * @param cardIdList
     * @return
     */
    @ServiceLog(description = "根据经费卡id查询当前层级经费卡信息", serviceType = ServiceType.RPC_CLIENT)
    public List<FundCardDTO> findCurrentCardByOrgCodeAndCardId(String orgCode, List<String> cardIdList) {
        List<List<String>> partition = Lists.partition(cardIdList, 200);
        List<FundCardDTO> fundCardDTOList = New.list();
        for (List<String> cardIdPart : partition) {
            OrgRequest<List<String>> request = new OrgRequest<>();
            request.setOrgCode(orgCode);
            request.setData(New.list(cardIdPart));
            RemoteResponse<List<FundCardDTO>> response = fundCardRPCService.getFundCardListByCardIds(request);
            Preconditions.notNull(response, "查询经费卡异常！结果为空！");
            Preconditions.isTrue(response.isSuccess(), JsonUtils.toJson(response.getMsg()));
            fundCardDTOList.addAll(response.getData());
        }
        return fundCardDTOList;
    }

    /**
     * 经费卡换卡，涉及财务对接
     */
    @ServiceLog(description = "预算系统换卡接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public FundCardResultDTO changeFundCard(String orgCode, ChangeFundCardDTO param,List<Integer> departmentIdList) {
        Preconditions.isTrue(StringUtils.isNotBlank(orgCode), "换卡失败，医院编码code为空！");
        OrgRequest<ChangeFundCardDTO> request = new OrgRequest<>();
        request.setData(param);
        request.setOrgCode(orgCode);
        request.setDepartmentIds(departmentIdList);

        RemoteResponse<FundCardResultDTO> response = fundCardRPCService.changeFundCard(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJson(response.getMsg()));
        return response.getData();
    }

    @ServiceLog(description = "根据机构编码判断是否新旧预算单位", serviceType = ServiceType.RPC_CLIENT)
    public boolean isNewBudgetByOrgCode(String orgCode) {
        OrgRequest request = new OrgRequest();
        request.setOrgCode(orgCode);
        RemoteResponse<Boolean> response = fundCardRuleRPCService.isNewBudgetByOrgCode(request);
        Assert.isTrue(response.isSuccess(), JsonUtils.toJson(response.getMsg()));

        return response.getData();
    }

    /**
     * 预算系统，经费卡绑定rpc接口
     *
     * @param bindDTO
     * @param orgCode
     */
    @ServiceLog(description = "预算系统绑定经费卡，推送绑卡数据接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void fundCardBindBatch(List<BindDTO> bindDTO, String orgCode) {
        OrgRequest<List<BindDTO>> request = new OrgRequest<>();
        request.setData(bindDTO);
        request.setOrgCode(orgCode);

        RemoteResponse response = fundCardBindRPCService.fundCardBindBatch(request);
        Assert.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "预算系统经费卡支付接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public FundCardResultDTO orderDefray(String orgCode, List<DefrayDTO> defrayListDto) {
        OrgRequest<List<DefrayDTO>> request = new OrgRequest<>();
        request.setOrgCode(orgCode);
        request.setData(defrayListDto);

        RemoteResponse<FundCardResultDTO> response = fundCardRPCService.fundCardDefrayBatch(request);
        Assert.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(description = "校验经费卡可用接口", serviceType = ServiceType.RPC_CLIENT)
    public List<FundCardAmountDTO> judgeUsableFundCardByRule(OrgRequest<FundCardUsableDTO> request) {
        RemoteResponse<List<FundCardAmountDTO>> response = fundCardRuleRPCService.judgeUsableFundCardByRule(request);
        Preconditions.notNull(response, "校验经费卡可用接口结果为空！");
        Preconditions.isTrue(response.isSuccess(), response.getMsg());

        return response.getData();
    }

    @ServiceLog(description = "拆单重新冻结&解冻经费", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public FundCardResultDTO fundCardSplitOrder(OrgRequest<FundCardSplitOrderDTO> request) {
        RemoteResponse<FundCardResultDTO> response = fundCardRPCService.fundCardSplitOrder(request);
        Preconditions.notNull(response, "拆单重新冻结&解冻经费接口结果为空！");
        Preconditions.isTrue(response.isSuccess(), "拆单重新冻结&解冻经费失败！" + response.getMsg());

        return response.getData();
    }

    /**
     * 查找经费类型枚举对应的经费类型描述
     * @param orgCode
     * @return
     */
    public Map<Integer, String> getFundTypeMap(String orgCode) {
        OrgRequest<List<Integer>> req = new OrgRequest<>();
        req.setOrgCode(orgCode);
        RemoteResponse<Map<Integer, String>> fundTypeMapRes = fundCardEnumService.getFundTypeMap(req);
        Preconditions.isTrue(fundTypeMapRes != null && fundTypeMapRes.isSuccess(), "查找经费类型失败，orgCode="+orgCode);
        return fundTypeMapRes.getData();
    }

    @ServiceLog(operationType = OperationType.WRITE, description = "经费授权审批金额返还", serviceType = ServiceType.COMMON_SERVICE)
    public void reduceAuthUsedApproveAmount(OrderMasterDO orderMasterDO, String fundCardId, BigDecimal changeAmount, String returnNo){
        OrgRequest<List<AuthApproveChangeAmountDTO>> request = new OrgRequest<>();
        request.setOrgCode(orderMasterDO.getFusercode());
        request.setOrgId(orderMasterDO.getFuserid());
        AuthApproveChangeAmountDTO authApproveChangeAmountDTO = new AuthApproveChangeAmountDTO();
        authApproveChangeAmountDTO.setFundCardId(fundCardId);
        authApproveChangeAmountDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
        authApproveChangeAmountDTO.setChangeAmount(changeAmount.negate());
        authApproveChangeAmountDTO.setSerialNumber(orderMasterDO.getForderno());
        // 退货用退货单号，否则用订单号，做了幂等
        authApproveChangeAmountDTO.setReturnSerialNumber(returnNo == null ? orderMasterDO.getForderno() : returnNo);
        request.setData(New.list(authApproveChangeAmountDTO));
        RemoteResponse<Boolean> response = fundCardAuthApproveRpcService.reduceUsedApproveAmount(request);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "经费采购规则校验", serviceType = ServiceType.RPC_CLIENT)
    public Boolean fundCardPurchaseRuleCheck(FundCardPurchaseRuleCheckDTO fundCardPurchaseRuleCheckDTO, String orgCode) {
        OrgRequest<FundCardPurchaseRuleCheckDTO> request = new OrgRequest<>();
        request.setOrgCode(orgCode);
        request.setData(fundCardPurchaseRuleCheckDTO);
        RemoteResponse<Boolean> response = fundPurchaseRuleRPCService.fundCardPurchaseRuleCheck(request);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
    
}
