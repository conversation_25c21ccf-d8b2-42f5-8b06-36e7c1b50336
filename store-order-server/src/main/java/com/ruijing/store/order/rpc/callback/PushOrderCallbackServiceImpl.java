package com.ruijing.store.order.rpc.callback;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.financial.docking.dto.order.OrderCallbackResult;
import com.reagent.tpi.tpiclient.api.order.v2.PushOrderCallbackService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.apply.enums.application.PushStatusEnum;
import com.ruijing.store.approval.api.enums.ApproveLevelEnum;
import com.ruijing.store.order.api.base.docking.enums.DockingPushStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.BusinessDockingRPCClient;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import com.ruijing.store.order.rpc.client.PurchaseApprovalLogClient;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * @description: 推送订单回调接口
 * @author: zhongyulei
 * @create: 2020/11/3 16:27
 **/
@MSharpService
public class PushOrderCallbackServiceImpl implements PushOrderCallbackService {

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private BusinessDockingRPCClient businessDockingRPCClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Override
    @ServiceLog(description = "订单推送回调接口", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> handlePushOrderResult(CallbackRequest<OrderCallbackResult> request) {
        String orgCode = request.getOrgCode();
        if (!DockingConstant.THIRD_PARTY_PLATFORM_ORGANIZATION.contains(orgCode)) {
            throw new IllegalStateException("回调失败，非单号对接单位不进行推送回调处理！orgCode: " + orgCode);
        }
        Preconditions.notNull(request.getData(), "订单推送回调处理失败！回调数据为空！");
        // 回调处理
        boolean processResult = this.handlePushOrderCallBackSuccess(orgCode, request);

        return RemoteResponse.<Boolean>custom().setSuccess().setData(processResult);
    }

    /**
     * 订单推送回调成功处理
     */
    private boolean handlePushOrderCallBackSuccess(String orgCode, CallbackRequest<OrderCallbackResult> request) {
        String orderNo = request.getData().getOrderNo();
        String extraOrderNo = request.getData().getExtraOrderNo();
        // 像华师这种没有自己的业务单号的，对方单号默认使用我方的单号
        if (StringUtils.isBlank(extraOrderNo)) {
            extraOrderNo = orderNo;
        }
        boolean processResult = request.isSuccess();
        int extraStatus = processResult ? DockingPushStatusEnum.SUCCESS.getCode() : DockingPushStatusEnum.FAILED.getCode();
        // 记录单号对接信息
        dockingExtraService.saveOrUpdateDockingExtra(new DockingExtra(orderNo, extraOrderNo, extraStatus, request.getMsg()));
        // 记录业务对接信息
        if (DockingConstant.THIRD_PARTY_PLATFORM_ORGANIZATION.contains(orgCode)) {
            OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
            businessDockingRPCClient.saveBusinessOrders(orderNo, extraOrderNo, orderMasterDO.getStatus(), null, orderMasterDO.getFuserid(), orgCode);
            if (!processResult) {
                // 还要把审批日志记录到采购单详情
                purchaseApprovalLogClient.addApprovalLog(orderMasterDO.getFtbuyappid(), DockingConstant.SYSTEM_OPERATOR_NAME, ApproveLevelEnum.DEFAULT.getValue(), 0, "系统审批单推送失败", request.getMsg());
                applicationBaseClient.saveApplicationMaster(orderMasterDO.getFtbuyappid(), null, null, PushStatusEnum.FAIL.getValue());
            } else if (OrderTypeEnum.BID_ORDER.getCode().byteValue() != orderMasterDO.getOrderType()) {
                purchaseApprovalLogClient.addApprovalLog(orderMasterDO.getFtbuyappid(), DockingConstant.SYSTEM_OPERATOR_NAME, ApproveLevelEnum.DEFAULT.getValue(), 0, "系统审批单推送成功", request.getMsg());
                applicationBaseClient.saveApplicationMaster(orderMasterDO.getFtbuyappid(), null, null, PushStatusEnum.APPROVE.getValue());
            }
        }
        // 记录对接日志
        orderOtherLogClient.createOrderDockingLog(request.getData().getOrderNo(), orgCode, null,
                JsonUtils.toJsonIgnoreNull(request), "对接单位订单推送回调", null);
        return processResult;
    }
}
