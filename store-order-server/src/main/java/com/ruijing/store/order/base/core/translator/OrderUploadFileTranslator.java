package com.ruijing.store.order.base.core.translator;

import com.reagent.research.sysu.order.api.dto.UploadFileDTO;
import com.ruijing.store.order.gateway.file.vo.UploadFileInfoVO;

/**
 * @author: <PERSON>wenyu
 * @create: 2024-12-27 17:09
 * @description:
 */
public class OrderUploadFileTranslator {

    public static UploadFileInfoVO sysuUploadFileDTO2OrderFileVO(UploadFileDTO uploadFileDTO){
        UploadFileInfoVO uploadFileInfoVO = new UploadFileInfoVO();
        uploadFileInfoVO.setUrl(uploadFileDTO.getFileUrl());
        uploadFileInfoVO.setFileName(uploadFileDTO.getFileName());
        return uploadFileInfoVO;
    }
}
