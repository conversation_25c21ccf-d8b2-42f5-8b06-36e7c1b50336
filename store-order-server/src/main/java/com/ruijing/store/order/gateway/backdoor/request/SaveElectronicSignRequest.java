package com.ruijing.store.order.gateway.backdoor.request;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;

@Model("保存验收电子签请求体")
public class SaveElectronicSignRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单号")
    private String orderNo;

    @ModelProperty("电子签名密码")
    private String password;

    @ModelProperty("slang")
    private String slang;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSlang() {
        return slang;
    }

    public SaveElectronicSignRequest setSlang(String slang) {
        this.slang = slang;
        return this;
    }

    @Override
    public String toString() {
        return "SaveElectronicSignRequest{" +
                "orderNo='" + orderNo + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
