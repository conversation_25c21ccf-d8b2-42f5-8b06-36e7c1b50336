package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.message.api.dto.EmailSendRequestRpcDTO;
import com.ruijing.message.api.dto.LetterSendRequestRpcDTO;
import com.ruijing.message.api.dto.WeChatSendRequestRpcDTO;
import com.ruijing.message.api.service.MessageSystemRpcService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

/**
 * @author: liwenyu
 * @createTime: 2023-11-08 10:28
 * @description:
 **/
@ServiceClient
public class MessageSystemClient {

    @MSharpReference(remoteAppkey = "msharp-message-service")
    private MessageSystemRpcService messageSystemRpcService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public void sendEmail(EmailSendRequestRpcDTO emailSendRequestRpcDTO){
        emailSendRequestRpcDTO.setAsync(true);
        RemoteResponse<Void> response = messageSystemRpcService.sendEmail(emailSendRequestRpcDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public void sendWeChat(WeChatSendRequestRpcDTO weChatSendRequestRpcDTO){
        weChatSendRequestRpcDTO.setAsync(true);
        RemoteResponse<Void> response = messageSystemRpcService.sendWeChat(weChatSendRequestRpcDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public void sendLetter(LetterSendRequestRpcDTO letterSendRequestRpcDTO){
        letterSendRequestRpcDTO.setAsync(true);
        RemoteResponse<Void> response = messageSystemRpcService.sendLetter(letterSendRequestRpcDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
