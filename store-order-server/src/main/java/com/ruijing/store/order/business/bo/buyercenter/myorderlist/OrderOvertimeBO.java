package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

import java.util.Set;

/**
 * @Author: <PERSON><PERSON>
 * @Description:
 * @DateTime: 2021/11/16 10:10
 */
public class OrderOvertimeBO {
    /**
     * 验收超市订单id列表
     */
    Set<Integer> acceptOvertimeOrderList;

    /**
     * 结算超时订单id列表
     */
    Set<Integer> balanceOvertimeOrderList;

    public Set<Integer> getAcceptOvertimeOrderList() {
        return acceptOvertimeOrderList;
    }

    public OrderOvertimeBO setAcceptOvertimeOrderList(Set<Integer> acceptOvertimeOrderList) {
        this.acceptOvertimeOrderList = acceptOvertimeOrderList;
        return this;
    }

    public Set<Integer> getBalanceOvertimeOrderList() {
        return balanceOvertimeOrderList;
    }

    public OrderOvertimeBO setBalanceOvertimeOrderList(Set<Integer> balanceOvertimeOrderList) {
        this.balanceOvertimeOrderList = balanceOvertimeOrderList;
        return this;
    }
}
