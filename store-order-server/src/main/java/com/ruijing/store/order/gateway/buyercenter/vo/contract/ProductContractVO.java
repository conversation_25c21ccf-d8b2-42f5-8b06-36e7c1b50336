package com.ruijing.store.order.gateway.buyercenter.vo.contract;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: zhangzhifeng
 * @Date: 2021/06/28 11:10
 */
@RpcModel("合同-合同商品信息返回体")
public class ProductContractVO implements Serializable {
    private static final long serialVersionUID = -9100839301015957547L;

    /**
     * 订单明细id
     */
    @RpcModelProperty(value="订单明细id")
    private Integer orderDetailId;

    /**
     * 商品编号
     */
    @RpcModelProperty(value = "商品编号")
    private Long productSn;

    /**
     * 方法学
     */
    @RpcModelProperty(value = "方法学")
    private String methodology;

    /**
     * 包装单位
     */
    @NotBlank(message = "包装单位不能为空")
    @RpcModelProperty(value = "包装单位")
    private String unit;

    /**
     * 数量
     */
    @RpcModelProperty(value="数量")
    private BigDecimal quantity;

    /**
     * 商品单价
     */
    @RpcModelProperty(value="商品单价")
    private BigDecimal originalPrice;

    /**
     * 商品小计 = 数量 * 商品单价
     */
    @RpcModelProperty(value="商品小计")
    private BigDecimal totalPrice;

    /**
     * 品牌名称
     */
    @RpcModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 产地
     */
    @RpcModelProperty(value = "产地")
    private String originPlace;

    /**
     * 商品名称
     */
    @RpcModelProperty(value="商品名称")
    private String goodname;

    /**
     * 规格
     */
    @RpcModelProperty(value="规格")
    private String fspec;

    /**
     * 备注
     */
    @RpcModelProperty(value = "备注")
    private String remark;

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public Long getProductSn() {
        return productSn;
    }

    public void setProductSn(Long productSn) {
        this.productSn = productSn;
    }

    public String getMethodology() {
        return methodology;
    }

    public void setMethodology(String methodology) {
        this.methodology = methodology;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getGoodname() {
        return goodname;
    }

    public void setGoodname(String goodname) {
        this.goodname = goodname;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getOriginPlace() {
        return originPlace;
    }

    public void setOriginPlace(String originPlace) {
        this.originPlace = originPlace;
    }

    public String getFspec() {
        return fspec;
    }

    public void setFspec(String fspec) {
        this.fspec = fspec;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ProductContractVO{");
        sb.append("orderDetailId=").append(orderDetailId);
        sb.append(", productSn=").append(productSn);
        sb.append(", methodology=").append(methodology);
        sb.append(", unit=").append(unit);
        sb.append(", quantity=").append(quantity);
        sb.append(", originalPrice=").append(originalPrice);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", goodname=").append(goodname);
        sb.append(", brandName=").append(brandName);
        sb.append(", originPlace=").append(originPlace);
        sb.append(", fspec=").append(fspec);
        sb.append('}');
        return sb.toString();
    }
}