package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.*;
import com.reagent.order.base.order.enums.product.OrderProductBatchesStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.reagent.order.base.order.service.OrderUniqueBarCodeRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.shop.trade.api.service.SuppOrderOperateRpcService;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.GoodsReturnBarcodeDataDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoVO;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.BatchesBarCodeDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.DetailBatchesDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailBathesDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.CategoryConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BatchesBarCodeVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderBatchesVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderDetailBatchesVO;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;
import com.ruijing.store.order.other.dto.UniqueBarCodeDTO;
import com.ruijing.store.order.other.translator.OrderUniqueBarCodeTranslator;
import com.ruijing.store.order.rpc.enums.ProductFillInBatchInfoEnum;
import com.ruijing.store.order.service.OrderBatchService;
import com.ruijing.store.order.util.BarCodeUtils;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.QrCodeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@ServiceClient
public class OrderUniqueBarCodeRPCClient {

    private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    /**
     * 使用一物一码的标志位
     */
    private final int USE_EACH_PRODUCT_EACH_CODE = 1;

    /**
     * 使用批次的标志位
     */
    private final int USE_BATCH = 1 << 1;

    /**
     * 默认类型，做兼容
     */
    private final List<Integer> DEFAULT_TYPE = New.list(UniqueBarCodeTypeEnum.ORG.getCode());

    /**
     * 可退货的订单状态：待收货/待验收审批/待结算/已完成
     */
    private final List<Integer> CAN_RETURN_ORDER_STATUS_LIST = New.list(OrderStatusEnum.WaitingForReceive.getValue(),
            OrderStatusEnum.OrderReceiveApproval.getValue(),
            OrderStatusEnum.WaitingForStatement_1.getValue(),
            OrderStatusEnum.Finish.getValue());

    @MSharpReference(remoteAppkey = "order-galaxy-service", timeout = "20000")
    private OrderUniqueBarCodeRPCService orderUniqueBarCodeRPCService;

    @MSharpReference(remoteAppkey = "shop-trade-service")
    private SuppOrderOperateRpcService suppOrderOperateRpcService;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private GasBottleClient gasBottleClient;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Resource
    private OrderBatchService orderBatchService;

    /**
     * 条形码dto 转换 前端批次dto
     * @return
     */
    public static DetailBatchesDTO barCodeToBatchesDTO(OrderUniqueBarCodeDTO item) {
        DetailBatchesDTO result = new DetailBatchesDTO();
        result.setDetailId(item.getOrderDetailId());
        result.setBatch(item.getBatches());
        result.setExpiration(item.getExpiration());
        result.setManufacturer(item.getManufacturer());
        result.setProductionDate(DateUtils.format("yyyy-MM-dd",item.getProductionDate()));
        result.setExterior(item.getExterior());
        result.setGasBottleBarcode(item.getGasBottleBarcode());
        result.setUniqueBarCode(item.getUniBarCode());
        result.setType(item.getType());

        return result;
    }

    @ServiceLog(description = "查询商品的其中一条批次明细", serviceType = ServiceType.RPC_CLIENT)
    public OrderUniqueBarCodeDTO findFirstByDetailId(Integer detailId) {
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setDetailId(detailId);
        request.setLimit(1);
        request.setTypeList(DEFAULT_TYPE);
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByDetailId(request);

        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        if (CollectionUtils.isEmpty(response.getData())) {
            return null;
        }
        return response.getData().get(0);
    }

    @ServiceLog(description = "查询商品的批次明细", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderUniqueBarCodeDTO> findByDetailId(Integer detailId) {
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setDetailId(detailId).setTypeList(DEFAULT_TYPE);
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByDetailId(request);

        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    @ServiceLog(description = "查询商品的批次明细", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderUniqueBarCodeDTO> findByDetailIdList(List<Integer> detailIdList) {
        List<OrderUniqueBarCodeDTO> result = new ArrayList<>();
        List<List<Integer>> partition = Lists.partition(detailIdList, 100);
        for (List<Integer> ids : partition) {
            OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
            request.setTypeList(DEFAULT_TYPE).setDetailIdList(ids);
            RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByDetailId(request);
            Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
            result.addAll(response.getData());
        }
        return result;
    }

    /**
     * 获取订单批次信息
     * @return
     */
    public DetailBatchesDTO outPutSingleBatchesInfo(Integer detailId) {
        OrderUniqueBarCodeDTO barCodeDTO = findFirstByDetailId(detailId);
        return barCodeToBatchesDTO(barCodeDTO);
    }

    public List<OrderUniqueBarCodeDTO> outPutOrderBatchesInfo(List<Integer> detailIdList) {
        return findByDetailId(detailIdList.get(0));
    }

    /**
     * 录入条形码批次信息
     * @param request
     * @return
     */
    @ServiceLog(description = "录入条形码批次信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean inputOrderBarCodeBatches(List<UniqueBarCodeDTO> request) {
        if (CollectionUtils.isEmpty(request)) {
            return false;
        }
        List<OrderUniqueBarCodeDTO> paramList = request.stream().map(item->{
            OrderUniqueBarCodeDTO orderUniqueBarCodeDTO = OrderUniqueBarCodeTranslator.orderDto2galaxyDto(item);
            if(this.completeBatchDataFill(orderUniqueBarCodeDTO)){
                orderUniqueBarCodeDTO.setBatchesStatus(OrderProductBatchesStatusEnum.INPUTTED.getCode());
            }
            return orderUniqueBarCodeDTO;
        }).collect(Collectors.toList());
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.addDetailBatches(paramList);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.isSuccess();
    }

    /**
     * 修改条形码批次信息，barCode必填
     * @param request
     * @return
     */
    @ServiceLog(description = "修改条形码批次信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean modifyOrderBarCodeBatches(List<OrderUniqueBarCodeDTO> request) {
        if (CollectionUtils.isEmpty(request) || request.stream().anyMatch(r -> r.getUniBarCode() == null)) {
            throw new IllegalStateException("修改条形码批次信息, 批次信息为空");
        }
        List<List<OrderUniqueBarCodeDTO>> partition = Lists.partition(request, 200);
        for (List<OrderUniqueBarCodeDTO> dtos : partition) {
            RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.setDetailBatches(new ArrayList<>(dtos));
            Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        }
        return true;
    }

    @ServiceLog(description = "一物一码修改批次信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean modifyBatchesForEachProductEachCode(List<UniqueBarCodeDTO> request) {
        List<OrderUniqueBarCodeDTO> reqBarCodeList = request.stream().map(item->{
            OrderUniqueBarCodeDTO orderUniqueBarCodeDTO = OrderUniqueBarCodeTranslator.orderDto2galaxyDto(item);
            if(this.completeBatchDataFill(orderUniqueBarCodeDTO)){
                orderUniqueBarCodeDTO.setBatchesStatus(OrderProductBatchesStatusEnum.INPUTTED.getCode());
            }
            return orderUniqueBarCodeDTO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(request) || request.stream().anyMatch(r -> r.getBarCode() == null)) {
            throw new IllegalStateException("修改条形码批次信息, 批次信息为空");
        }
        String orderNo = request.get(0).getOrderNo();
        cacheClient.controlRepeatOperation("updateOrderBarcodeBatches:" + orderNo, 3);

        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.setDetailBatches(reqBarCodeList);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        cacheClient.removeCache("modifyOrderBarCodeBatches:" + orderNo);
        return response.isSuccess();
    }

    @ServiceLog(description = "更新批次信息（不需要二维码的模式）", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean modifyCommonBatches(List<UniqueBarCodeDTO> requestDataList){
        BusinessErrUtil.notEmpty(requestDataList, "需要修改的数据为空！");
        String orderNo = requestDataList.get(0).getOrderNo();
        BusinessErrUtil.notNull(orderNo, "订单号不可空");
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, "没有找到该订单号对应的订单");
        int mode = this.getOrderMode(orderMasterDO.getId());
        BusinessErrUtil.isTrue(enableMode(mode, USE_BATCH) && !enableMode(mode, USE_EACH_PRODUCT_EACH_CODE), "仅有需要填写批次的非一物一码单可以调用此接口");
        cacheClient.controlRepeatOperation("updateOrderBarcodeBatches:" + orderNo, 3);
        // 获取单位的getter
        List<Function<OrderUniqueBarCodeDTO, ?>> functionList = this.getBatchModeValidateFieldGetter(orderMasterDO.getFusercode(), orderMasterDO.getId());
        BusinessErrUtil.notEmpty(functionList, "该单位没有开启填写批次功能！");
        List<OrderUniqueBarCodeDTO> reqBarCodeList = requestDataList.stream().map(item->{
            OrderUniqueBarCodeDTO orderUniqueBarCodeDTO = OrderUniqueBarCodeTranslator.orderDto2galaxyDto(item);
            if(this.completeBatchDataFill(orderUniqueBarCodeDTO)){
                orderUniqueBarCodeDTO.setBatchesStatus(OrderProductBatchesStatusEnum.INPUTTED.getCode());
            }
            return orderUniqueBarCodeDTO;
        }).collect(Collectors.toList());
        reqBarCodeList.forEach(reqBarCode -> {
            // 通过配置来校验必填项是否都填了
            for(Function<OrderUniqueBarCodeDTO, ?> function : functionList){
                Object field = function.apply(reqBarCode);
                BusinessErrUtil.notNull(field, "你有部分商品的信息未填写完整，请填写后再提交");
                if(field instanceof String){
                    BusinessErrUtil.isTrue(StringUtils.isNotBlank((String) field), "你有部分商品的信息未填写完整，请填写后再提交");
                }
            }
            BusinessErrUtil.isTrue(reqBarCode.getTotal() != null, "你有部分商品的信息未填写完整，请填写后再提交");
            reqBarCode.setBatches(reqBarCode.getBatches() != null ? reqBarCode.getBatches().trim() : null);
            reqBarCode.setManufacturer(reqBarCode.getManufacturer() != null ? reqBarCode.getManufacturer().trim() : null);
            reqBarCode.setExpiration(reqBarCode.getExpiration() != null ? reqBarCode.getExpiration().trim() : null);
            reqBarCode.setProductionDate(reqBarCode.getProductionDate() != null ? reqBarCode.getProductionDate() : null);
        });

        Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdReqDataListMap = DictionaryUtils.groupBy(reqBarCodeList, OrderUniqueBarCodeDTO::getOrderDetailId);
        BusinessErrUtil.isTrue(detailIdReqDataListMap.get(null) == null, "订单详情id不可空！");
        List<OrderDetailDO> detailDOList = orderDetailMapper.findByIdIn(detailIdReqDataListMap.keySet());

        // 批次比较器（按照标识批次重复的字段依次排序，若前后指针指向的相同，则存在重复）
        Comparator<OrderUniqueBarCodeDTO> batchDataComparator = Comparator.comparing(OrderUniqueBarCodeDTO::getBatches)
                .thenComparing(OrderUniqueBarCodeDTO::getExpiration)
                .thenComparing(OrderUniqueBarCodeDTO::getManufacturer)
                .thenComparing(OrderUniqueBarCodeDTO::getProductionDate);
        for(OrderDetailDO orderDetailDO : detailDOList){
            List<OrderUniqueBarCodeDTO> matchDataList = detailIdReqDataListMap.get(orderDetailDO.getId());
            if(CollectionUtils.isEmpty(matchDataList)){
                continue;
            }
            // 1.判断填写的批次数量是否超出了订单商品数量上限
            Integer totalCount = matchDataList.stream().map(OrderUniqueBarCodeDTO::getTotal).reduce(Integer::sum).orElse(0);
            BusinessErrUtil.isTrue(totalCount <= orderDetailDO.getFquantity().intValue(), "你有部分种类商品的数量超过订单实际购买数量，请核对修改后再提交。");

            // 2.判断是否批次重复
            if(matchDataList.size() > 1){
                // 按照批次-过期时间的顺序排序，如果批次有相同的两个项，其两者顺序比较后的值必为0，即一定紧挨着。故排序完毕后判断前后两项是否相同即可
                matchDataList.sort(batchDataComparator);
                // 记录前一项的指针
                OrderUniqueBarCodeDTO prev = null;
                for(OrderUniqueBarCodeDTO item : matchDataList){
                    if(prev != null){
                        // 判断需要填写的字段是否存在全部完全相同的情况
                        boolean sameBatch = true;
                        for(Function<OrderUniqueBarCodeDTO, ?> function : functionList){
                            Object prevField = function.apply(prev);
                            Object itemField = function.apply(item);
                            sameBatch = sameBatch && prevField.equals(itemField);
                        }
                        BusinessErrUtil.isTrue(!sameBatch, "存在有同种商品批次信息相同的情况，请核对修改后再提交。");
                    }
                    // 指向当前项，让下一个项与其比较
                    prev = item;
                }
            }
        }
        orderBatchService.overWriteBatches(requestDataList);
        this.overwriteOldBatches(orderMasterDO, functionList, detailIdReqDataListMap);
        // 新旧批次数据双写
        boolean needLog = ProcessSpeciesEnum.NORMAL.getValue().equals(orderMasterDO.getSpecies().intValue())
                && OrderStatusEnum.WaitingForReceive.getValue().equals(orderMasterDO.getStatus());
        if(needLog){
            // 记日志，打印修改前数据
            OrderApprovalLog log = new OrderApprovalLog();
            log.setOrderId(orderMasterDO.getId());
            log.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
            log.setReason("-");
            log.setApproveStatus(OrderApprovalEnum.SUPP_MODIFY_BATCHES.getValue());
            orderApprovalLogMapper.insertSelective(log);
        }
        return true;
    }

    private int getOrderMode(Integer orderId){
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue(), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue()));
        int mode = 0;
        for (OrderExtraDTO orderExtraDTO : orderExtraDTOList) {
            if (OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue().equals(orderExtraDTO.getExtraKey()) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTO.getExtraValue())) {
                mode = mode ^ USE_EACH_PRODUCT_EACH_CODE;
            } else if (OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue().equals(orderExtraDTO.getExtraKey()) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTO.getExtraValue())) {
                mode = mode ^ USE_BATCH;
            }
        }
        return mode;
    }

    /**
     * 通过按位与判断是否启用了该模式
     * @param mode getOrderMode获取到的结果
     * @param modeFlag 需要判断的标志位
     * @return 是否启用对应的模式
     */
    private boolean enableMode(int mode, int modeFlag){
        return (mode & modeFlag) == modeFlag;
    }

    /**
     * 更新旧批次(仅适用批次，不使用一物一码)
     * @param orderMasterDO 订单数据
     * @param functionList 批次获取函数
     * @param detailIdReqDataListMap 详情id-请求数据映射
     */
    private void overwriteOldBatches(OrderMasterDO orderMasterDO, List<Function<OrderUniqueBarCodeDTO, ?>> functionList, Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdReqDataListMap){
        boolean needLog = ProcessSpeciesEnum.NORMAL.getValue().equals(orderMasterDO.getSpecies().intValue())
                && OrderStatusEnum.WaitingForReceive.getValue().equals(orderMasterDO.getStatus());
        OrderDetailBatchesRequestDTO findByOrderNoParam = new OrderDetailBatchesRequestDTO();
        findByOrderNoParam.setOrderNo(orderMasterDO.getForderno()).setTypeList(DEFAULT_TYPE);
        RemoteResponse<List<OrderUniqueBarCodeDTO>> dbBatchResp = orderUniqueBarCodeRPCService.findByOrderNo(findByOrderNoParam);
        Preconditions.isTrue(dbBatchResp.isSuccess(), dbBatchResp.getMsg());
        if(CollectionUtils.isEmpty(dbBatchResp.getData())){
            return;
        }

        List<OrderUniqueBarCodeDTO> dbBatchList = dbBatchResp.getData();

        if(needLog) {
            // 打印修改前数据
            this.logSuppModifyBatches(orderMasterDO.getForderno() + "修改前批次信息：\n", dbBatchList, functionList);
        }

        // 1.清空目前已填写的所有批次及有效期数据
        dbBatchList.forEach(item-> {
            item.setBatches(StringUtils.EMPTY);
            item.setExpiration(StringUtils.EMPTY);
            item.setManufacturer(StringUtils.EMPTY);
            item.setProductionDate(null);
            item.setBatchesStatus(OrderProductBatchesStatusEnum.UN_INPUT.getCode());
        });
        Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdDbBatchListMap = DictionaryUtils.groupBy(dbBatchList, OrderUniqueBarCodeDTO::getOrderDetailId);
        for(Map.Entry<Integer, List<OrderUniqueBarCodeDTO>> dbDataEntry : detailIdDbBatchListMap.entrySet()){
            // 2. 通过订单商品详情id，找到请求及数据库对应的条形码/批次数据
            Integer detailId = dbDataEntry.getKey();
            List<OrderUniqueBarCodeDTO> matchReqDataList = detailIdReqDataListMap.get(detailId);
            if(CollectionUtils.isEmpty(matchReqDataList)){
                continue;
            }
            // 3. 生成请求数据里面对应total条条形码/批次的条形码数据。（即数据从请求会写到数据库已存的数据中）
            Iterator<OrderUniqueBarCodeDTO> reqDataItr = matchReqDataList.iterator();
            Iterator<OrderUniqueBarCodeDTO> dbDataItr = dbDataEntry.getValue().iterator();
            reqDataItr.forEachRemaining(reqData->{
                int total = reqData.getTotal();
                for(int i = 0; i < total; i++){
                    OrderUniqueBarCodeDTO dbData = dbDataItr.next();
                    dbData.setBatches(reqData.getBatches());
                    dbData.setExpiration(reqData.getExpiration());
                    dbData.setManufacturer(reqData.getManufacturer());
                    dbData.setProductionDate(reqData.getProductionDate());
                    dbData.setBatchesStatus(OrderProductBatchesStatusEnum.INPUTTED.getCode());
                }
            });
        }

        if(needLog){
            this.logSuppModifyBatches(orderMasterDO.getForderno() + "修改后批次信息：\n", dbBatchList, functionList);
        }
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.setDetailBatches(dbBatchList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }


    /**
     * 根据订单号获取条形码批次统计信息
     * @param orderNo
     * @return
     */
    @ServiceLog(description = "根据订单号获取条形码批次统计信息", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderUniqueBarCodeStatisticsDTO> getBarCodeBatchesByOrderNo(String orderNo, List<Integer> typeList) {
        if (orderNo == null) {
            throw new IllegalStateException("获取条形码批次信息, 订单号为空");
        }
        typeList = typeList == null ? DEFAULT_TYPE : typeList;
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setOrderNo(orderNo).setTypeList(typeList);
        RemoteResponse<List<OrderUniqueBarCodeStatisticsDTO>> response = orderUniqueBarCodeRPCService.findBarCodeStatisticsByOrderNo(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    /**
     * 根据订单号获取条形码批次统计信息
     * @param detailId
     * @return
     */
    @ServiceLog(description = "根据detailId获取条形码批次统计信息", serviceType = ServiceType.RPC_CLIENT)
    public OrderUniqueBarCodeStatisticsDTO getBarCodeBatchesByDetailId(Integer detailId) {
        if (detailId == null) {
            throw new IllegalStateException("获取条形码批次信息, detailId为空");
        }
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setDetailId(detailId).setTypeList(DEFAULT_TYPE);
        RemoteResponse<OrderUniqueBarCodeStatisticsDTO> response = orderUniqueBarCodeRPCService.findBarCodeStatisticsByDetailId(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    @ServiceLog(description = "单号查询商品的一物一码明细", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderUniqueBarCodeDTO> findByOrderNo(String orderNo, List<Integer> typeList) {
        if (orderNo == null) {
            return Collections.emptyList();
        }
        typeList = typeList == null ? DEFAULT_TYPE : typeList;
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setOrderNo(orderNo).setTypeList(typeList);
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByOrderNo(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    @ServiceLog(description = "条形码查询一物一码明细", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderUniqueBarCodeDTO> findByBarCode(List<String> barCodeList) {
        if (CollectionUtils.isEmpty(barCodeList)) {
            return Collections.emptyList();
        }

        List<OrderUniqueBarCodeDTO> result = new ArrayList<>(barCodeList.size());
        List<List<String>> partition = Lists.partition(barCodeList, 200);
        for (List<String> list : partition) {
            OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
            request.setUniBarCodeList(list);
            RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByBarCodeList(request);
            Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
            result.addAll(response.getData());
        }

        return result;
    }

    @ServiceLog(description = "条形码查询批次明细", serviceType = ServiceType.RPC_CLIENT)
    public OrderUniqueBarCodeDTO findByBarCode(String barCode) {
        if (barCode == null) {
            return null;
        }
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setUniBarCode(barCode);
        RemoteResponse<OrderUniqueBarCodeDTO> response = orderUniqueBarCodeRPCService.findByBarCode(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    @ServiceLog(description = "条形码查询可退货的一物明细", serviceType = ServiceType.RPC_CLIENT)
    public UniqueBarCodeDTO findCanReturnBatchesByBarcode(String barcode){
        OrderUniqueBarCodeDTO orderUniqueBarCodeDTO = this.findByBarCode(barcode);
        BusinessErrUtil.isTrue(orderUniqueBarCodeDTO != null && orderUniqueBarCodeDTO.getOrderNo() != null, "找不到匹配的商品信息！");
        BusinessErrUtil.isTrue(!(OrderProductInventoryStatusEnum.INBOUNDING.getCode() == orderUniqueBarCodeDTO.getInventoryStatus()
                || OrderProductInventoryStatusEnum.COMPLETE_INBOUND.getCode() == orderUniqueBarCodeDTO.getInventoryStatus()), ExecptionMessageEnum.PRODUCT_IN_OR_STORED);
        Preconditions.isTrue(OrderProductTransactionStatusEnum.RECEIVED.getCode() == orderUniqueBarCodeDTO.getTransactionStatus()
                        || OrderProductTransactionStatusEnum.WAITING_FOR_RECEIVE.getCode() == orderUniqueBarCodeDTO.getTransactionStatus(),
                "添加失败！商品二维码<待收货>或<已收货>状态才允许退货");
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderUniqueBarCodeDTO.getOrderNo());
        BusinessErrUtil.isTrue(ProcessSpeciesEnum.NORMAL.getValue().equals(orderMasterDO.getSpecies().intValue()), "商品所属订单非线上单，不可退货");
        BusinessErrUtil.isTrue(CAN_RETURN_ORDER_STATUS_LIST.contains(orderMasterDO.getStatus()), "商品所属订单状态非待收货/待验收审批/待结算/已完成，不可退货");
        List<BaseOrderExtraDTO> extraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderMasterDO.getId()), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(extraDTOList) && CommonValueUtils.parseNumberStrToBoolean(extraDTOList.get(0).getExtraValue()), "该订单非一物一码单据，不可扫码退货");
        UniqueBarCodeDTO uniqueBarCodeDTO = OrderUniqueBarCodeTranslator.galaxyDto2orderDto(orderUniqueBarCodeDTO);
        if(StringUtils.isNotBlank(uniqueBarCodeDTO.getGasBottleBarcode())){
            List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(New.list(orderUniqueBarCodeDTO.getGasBottleBarcode()));
            if(CollectionUtils.isNotEmpty(gasBottleVOList)){
                uniqueBarCodeDTO.setGasBottle(gasBottleVOList.get(0));
            }
        }
        // 查询商品信息
        OrderDetailDO orderDetailDO = orderDetailMapper.selectByPrimaryKey(uniqueBarCodeDTO.getOrderDetailId());
        Map<Integer, String> extraInfoMap = getOrderDetailExtraInfo(orderMasterDO.getId(), uniqueBarCodeDTO.getOrderDetailId());
        // 填充订单详情商品信息
        fillOrderDetailInfo(uniqueBarCodeDTO, orderDetailDO, extraInfoMap);
        return uniqueBarCodeDTO;
    }

    /**
     * 填充订单详情商品信息
     */
    private void fillOrderDetailInfo(UniqueBarCodeDTO uniqueBarCodeDTO, OrderDetailDO orderDetailDO, Map<Integer, String> type2DetailExtraMap) {
        if (Objects.isNull(orderDetailDO)||Objects.isNull(uniqueBarCodeDTO)) {
            return;
        }
        // 危化品标签
        Integer dangerousTypeId = orderDetailDO.getDangerousTypeId();
        if (Objects.isNull(dangerousTypeId)) {
            dangerousTypeId = DangerousTypeEnum.UN_DANGEROUS.getValue();
        }
        uniqueBarCodeDTO.setFirstCategoryId(orderDetailDO.getFirstCategoryId());
        uniqueBarCodeDTO.setDangerousType(dangerousTypeId);
        uniqueBarCodeDTO.setDangerousTag(DangerousTypeEnum.get(dangerousTypeId).getName());
        uniqueBarCodeDTO.setGoodsCode(orderDetailDO.getFgoodcode());
        uniqueBarCodeDTO.setUnit(orderDetailDO.getFunit());
        uniqueBarCodeDTO.setCasNo(orderDetailDO.getCasno());
        // 包装规格 , 化学试剂和危险化学品 取值详情的specification，其他分类取PACKING_UNIT
        if (New.set(CategoryConstant.DANGEROUS_ID, CategoryConstant.CHEMICAL_REAGENTS_ID).contains(orderDetailDO.getFirstCategoryId())) {
            uniqueBarCodeDTO.setPackingSpec(orderDetailDO.getFspec());
        } else {
            String packUnitExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PACKING_UNIT.getType());
            uniqueBarCodeDTO.setPackingSpec(packUnitExtraDTO);
        }
        // 浓度
        String concentration = type2DetailExtraMap.get(OrderDetailExtraEnum.CONCENTRATION.getType());
        uniqueBarCodeDTO.setPurity(concentration);
        // 注册编码
        String restCertCode = type2DetailExtraMap.get(OrderDetailExtraEnum.MEDICAL_DEVICE_REGIS_CERT_NUMBER.getType());
        uniqueBarCodeDTO.setMedicalDeviceRegisCertNumber(restCertCode);

        // 完成周期
        String completionCycle = type2DetailExtraMap.get(OrderDetailExtraEnum.COMPLETION_CYCLE.getType());
        uniqueBarCodeDTO.setCompletionCycle(completionCycle);

        // 出版社
        String press = type2DetailExtraMap.get(OrderDetailExtraEnum.PRESS.getType());
        uniqueBarCodeDTO.setPress(press);
        // 产品规格
        String productSpec = type2DetailExtraMap.get(OrderDetailExtraEnum.PRODUCT_SPECIFICATION.getType());
        uniqueBarCodeDTO.setProductSpec(productSpec);
        // 型号
        String model = type2DetailExtraMap.get(OrderDetailExtraEnum.MODEL_NUMBER.getType());
        uniqueBarCodeDTO.setModelNumber(model);

    }

    /**
     * 获取订单详情额外信息
     *
     * @param orderId 订单ID
     * @param orderDetailId 订单详情ID
     */
    private Map<Integer, String> getOrderDetailExtraInfo(Integer orderId, Integer orderDetailId) {
        if (Objects.isNull(orderId) || Objects.isNull(orderDetailId)) {
            return New.emptyMap();
        }
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(
                New.list(orderId), New.list(orderDetailId));

        if (CollectionUtils.isEmpty(orderDetailExtraDTOList)) {
            return New.emptyMap();
        }
        
        Map<Integer, String> resultMap = New.map();

        for (OrderDetailExtraDTO extraDTO : orderDetailExtraDTOList) {
            if (Objects.nonNull(extraDTO.getExtraKeyType()) && StringUtils.isNotBlank(extraDTO.getExtraValue())) {
                resultMap.put(extraDTO.getExtraKeyType(), extraDTO.getExtraValue());
            }
        }

        return resultMap;
    }

    @ServiceLog(description = "退货单号查询批次明细", serviceType = ServiceType.RPC_CLIENT)
    public List<UniqueBarCodeDTO> getReturnAllDetailBatches(String returnNo){
        List<OrderUniqueBarCodeDTO> barCodeList = this.findByReturnNo(returnNo);
        List<UniqueBarCodeDTO> resList = barCodeList.stream().map(OrderUniqueBarCodeTranslator::galaxyDto2orderDto).collect(Collectors.toList());
        List<String> gasBottleBarcodes = resList.stream().map(UniqueBarCodeDTO::getGasBottleBarcode).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(gasBottleBarcodes)){
            return resList;
        }
        // 填充订单详情信息
        List<Integer> orderDetailIds = resList.stream().map(UniqueBarCodeDTO::getOrderDetailId).distinct().collect(Collectors.toList());
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByIdIn(orderDetailIds);
        BusinessErrUtil.notEmpty(orderDetailDOList,ExecptionMessageEnum.ORDER_DETAILS_NOT_FOUND);
        Map<Integer, OrderDetailDO> detailId2DOMap = DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, Function.identity());
        for (UniqueBarCodeDTO barCodeDTO : resList) {
            Integer orderDetailId = barCodeDTO.getOrderDetailId();
            OrderDetailDO orderDetailDO = detailId2DOMap.get(orderDetailId);
            if (Objects.isNull(orderDetailDO)) {
                continue;
            }
            // 填充危化品标签
            Integer dangerousTypeId = orderDetailDO.getDangerousTypeId();
            if (Objects.isNull(dangerousTypeId)) {
                dangerousTypeId = DangerousTypeEnum.UN_DANGEROUS.getValue();
            }
            barCodeDTO.setDangerousType(dangerousTypeId);
            barCodeDTO.setDangerousTag(DangerousTypeEnum.get(dangerousTypeId).getName());
        }


        List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(gasBottleBarcodes);
        Map<String, GasBottleVO> gasBottleQrcodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
        resList.forEach(item->item.setGasBottle(gasBottleQrcodeIdentityMap.get(item.getGasBottleBarcode())));
        return resList;
    }

    @ServiceLog(description = "退货单号查询批次明细", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderUniqueBarCodeDTO> findByReturnNo(String returnNo) {
        List<GoodsReturn> returnDataList = goodsReturnMapper.findByReturnNoIn(New.list(returnNo));
        Preconditions.notEmpty(returnDataList, returnNo + "没有查到有效的退货数据");

        GoodsReturnInfoVO goodsReturnInfoVO = GoodsReturnTranslator.doToGoodsReturnInfoVO(returnDataList.get(0));
        Map<String, GoodsReturnBarcodeDataDTO> barcodeGoodsReturnDataMap = goodsReturnInfoVO.getReturnInfoDetailList().stream().map(GoodsReturnInfoDetailVO::getGoodsReturnBarcodeDataDTOList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toMap(GoodsReturnBarcodeDataDTO::getBarcode, Function.identity(), (o, n)->n));

        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setTypeList(DEFAULT_TYPE);
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response;
        if(barcodeGoodsReturnDataMap.isEmpty()){
            // 如果是没在退货单信息里面记退货批次的旧数据，只能按照退货单号查了
            request.setReturnNo(returnNo);
            response = orderUniqueBarCodeRPCService.findByBusinessNo(request);
        }else {
            // 否则，根据退货单里面的批次数据去查对应批次
            request.setUniBarCodeList(New.list(barcodeGoodsReturnDataMap.keySet()));
            response = orderUniqueBarCodeRPCService.findByBarCodeList(request);
        }
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        List<OrderUniqueBarCodeDTO> resList = response.getData();
        resList.forEach(res->{
            GoodsReturnBarcodeDataDTO returnBarcode = barcodeGoodsReturnDataMap.get(res.getUniBarCode());
            if(returnBarcode != null){
                res.setReturnReason(returnBarcode.getReturnReason());
                res.setReturnDescription(returnBarcode.getReturnDescription());
            }
        });
        return resList;
    }

    @ServiceLog(description = "删除订单二维码信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void deleteByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setOrderNo(orderNo);
        request.setTypeList(DEFAULT_TYPE);
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.deleteByOrderNo(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
    }

    @ServiceLog(description = "生成订单二维码信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void generateBarCode(Integer orderId) {
        if (orderId == null) {
            return;
        }
        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderId(orderId.longValue());
        RemoteResponse<Boolean> response = orderUniqueBarCodeRPCService.generatedBarCodeByOrder(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
    }

    @ServiceLog(description = "生成订单二维码信息（深圳湾）", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public Boolean generateGoodsQrCodeBySZWSYS(Integer orderId) {
        if (Objects.isNull(orderId)) {
            return false;
        }
        RemoteResponse<Boolean> response = suppOrderOperateRpcService.generateGoodsQrCodeBySZWSYS(orderId);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }


    @ServiceLog(description = "修改条形码批次信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean updateStatusByReturnNo(String returnNo, Integer updateStatus) {
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setReturnNo(returnNo);
        request.setTransactionStatus(updateStatus);
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.updateStatusByBusinessNo(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData() > 0;
    }

    @ServiceLog(description = "根据二维码修改状态", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void updateStatusByBarcode(List<String> barcodeList, Integer transactionStatus, Integer inventoryStatus){
        if(CollectionUtils.isEmpty(barcodeList)){
            return;
        }
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setUniBarCodeList(barcodeList);
        request.setTransactionStatus(transactionStatus);
        request.setInventoryStatus(inventoryStatus);
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.updateStatusByBusinessNo(request);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    public OrderBatchesVO findByReturnNoAndDetailId(String returnNo, Integer detailId) {
        OrderDetailDO orderDetailDO = orderDetailMapper.selectByPrimaryKey(detailId);
        if(orderDetailDO == null){
            return null;
        }

        Integer orderId = orderDetailDO.getFmasterid();
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, "订单不存在！");
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        // 如果标记了是一物一码单或者三院临床单，按一物一码输出
        boolean isEachProductEachCode = orderExtraDTOList.stream().anyMatch(baseOrderExtraDTO -> OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue().equals(baseOrderExtraDTO.getExtraKey()) && CommonValueUtils.parseNumberStrToBoolean(baseOrderExtraDTO.getExtraValue()));
        if(OrgEnum.SHEN_ZHEN_WAN_SHI_YAN_SHI.getValue() == orderMasterDO.getFuserid()){
            isEachProductEachCode = true;
        }
        List<OrderUniqueBarCodeDTO> byReturnNo = findByReturnNo(returnNo);
        byReturnNo = byReturnNo.stream().filter(orderUniqueBarCodeDTO -> detailId.equals(orderUniqueBarCodeDTO.getOrderDetailId())).collect(Collectors.toList());
        return this.convertBarcodeData2BatchesVO(New.list(orderDetailDO), byReturnNo, isEachProductEachCode, false);
    }

    public OrderBatchesVO getOrderBatches(String orderNo, List<Integer> typeList) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, "订单不存在！");

        // 如果标记了是一物一码单或者三院临床单，按一物一码输出
        int mode = this.getOrderMode(orderMasterDO.getId());
        boolean isEachProductEachCode = enableMode(mode, USE_EACH_PRODUCT_EACH_CODE);
        boolean outputBarcodeImg = false;
        if(OrgEnum.SHEN_ZHEN_WAN_SHI_YAN_SHI.getValue() == orderMasterDO.getFuserid()){
            isEachProductEachCode = true;
            outputBarcodeImg = true;
        }

        if(!isEachProductEachCode){
            // 非一物一码 仅读取批次
            return orderBatchService.getOrderBatchesVO(orderMasterDO);
        }else {
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
            OrderBatchesVO orderBatchesVO = new OrderBatchesVO();
            List<OrderDetailBatchesVO> orderDetailBatchesVOList = orderDetailDOList.stream().map(detailDO-> new OrderDetailBatchesVO()
                    .setDetailId(detailDO.getId())
                    .setProductName(detailDO.getFgoodname())
                    .setProductCode(detailDO.getFgoodcode())
                    .setSpec(detailDO.getFspec())
                    .setBrand(detailDO.getFbrand())
                    .setCasNo(detailDO.getCasno())
                    .setSecondCategoryId(detailDO.getSecondCategoryId())
                    .setSecondCategoryName(detailDO.getSecondCategoryName())).collect(Collectors.toList());
            orderBatchesVO.setOrderDetailBathes(orderDetailBatchesVOList);
            // 一物一码 保留旧处理
            List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = findByOrderNo(orderNo, typeList);
            if (CollectionUtils.isEmpty(orderUniqueBarCodeDTOList)) {
                return orderBatchesVO;
            }
            Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdBarcodeGroupMap = orderUniqueBarCodeDTOList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId));
            for(OrderDetailBatchesVO orderDetailBatchesVO : orderBatchesVO.getOrderDetailBathes()){
                List<OrderUniqueBarCodeDTO> barCodeList = detailIdBarcodeGroupMap.get(orderDetailBatchesVO.getDetailId());
                orderDetailBatchesVO.setBatches(this.wrapperBatches(barCodeList).stream().map(OrderUniqueBarCodeTranslator::batchesBarCodeDTO2VO).collect(Collectors.toList()));
                if (outputBarcodeImg) {
                    // 深圳湾实验室，输出二维码
                    orderDetailBatchesVO.getBatches().forEach(item -> {
                        if (UniqueBarCodeTypeEnum.ORG.getCode().equals(item.getType())) {
                            try {
                                item.setBarCodeQrImg(QrCodeUtil.getBase64(item.getBarCode(), 120, 120));
                            } catch (Exception e) {
                                logger.error("生成二维码图片失败", e);
                            }
                        }
                    });
                }
            }

            List<String> gasBottleBarcodes = orderBatchesVO.getOrderDetailBathes().stream().map(OrderDetailBatchesVO::getBatches).flatMap(List::stream).map(BatchesBarCodeVO::getGasBottleBarcode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gasBottleBarcodes)) {
                List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(gasBottleBarcodes);
                Map<String, GasBottleVO> gasBottleCodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
                orderBatchesVO.getOrderDetailBathes().forEach(item -> {
                    if (CollectionUtils.isNotEmpty(item.getBatches())) {
                        item.getBatches().forEach(batch -> batch.setGasBottle(gasBottleCodeIdentityMap.get(batch.getGasBottleBarcode())));
                    }
                });
            }
            return orderBatchesVO;
        }
    }

    /**
     * 三院临床/中爆类的，需要二维码的batch包装
     * @param collect 数据集
     * @return 包装好的数据
     */
    private List<BatchesBarCodeDTO> wrapperBatches(List<OrderUniqueBarCodeDTO> collect) {
        List<BatchesBarCodeDTO> batchesBarCodeDTOList = collect.stream().map(this::galaxyDto2StoreOrderDto).collect(Collectors.toList());
        batchesBarCodeDTOList.forEach(item->{
            if(UniqueBarCodeTypeEnum.CHINA_BLAST.getCode().equals(item.getType())){
                try{
                    item.setBarCodeImg(BarCodeUtils.getBase64Img(item.getBarCode()));
                }catch (Exception e){
                    logger.error("生成中爆条形码图片失败", e);
                }
            }
        });
        return batchesBarCodeDTOList;
    }

    /**
     * 厦大附一类的，不需要二维码的batch包装。--按照批次和有效期进行distinct，数量回写到quantity字段。过滤掉空的批次
     * @param collect 数据集
     * @return 包装好的数据
     */
    public List<OrderUniqueBarCodeDTO> groupBatchesByField(List<OrderUniqueBarCodeDTO> collect, List<Function<OrderUniqueBarCodeDTO, ?>> functionList) {
        Map<String, OrderUniqueBarCodeDTO> uniqKeyIdentityMap = New.map();
        // 单位二维码处理
        collect.stream().filter(item->UniqueBarCodeTypeEnum.ORG.getCode().equals(item.getType())).forEach(item->{
            if(OrderProductTransactionStatusEnum.RETURNED.getCode() == item.getTransactionStatus()){
                // 退货完成的不返回
                return;
            }
            List<Object> uniqKeyObjList = new ArrayList<>(functionList.size());
            for(Function<OrderUniqueBarCodeDTO, ?> function : functionList){
                // 判断是否有字段缺省，缺了的的也不能归类
                Object field = function.apply(item);
                if(field == null){
                    uniqKeyObjList.add(StringUtils.EMPTY);
                    continue;
                }
                if(field instanceof String && StringUtils.isBlank((String) field)){
                    uniqKeyObjList.add(StringUtils.EMPTY);
                    continue;
                }
                uniqKeyObjList.add(field);
            }
            // 归类到一个组下，然后聚合
            String uniqKey = StringUtils.join(uniqKeyObjList, "_");
            OrderUniqueBarCodeDTO matchRecord = uniqKeyIdentityMap.computeIfAbsent(uniqKey, k -> item);
            matchRecord.setTotal(matchRecord.getTotal() == null ? 1 : 1 + matchRecord.getTotal());

        });
        List<OrderUniqueBarCodeDTO> resultList = New.list(uniqKeyIdentityMap.values());
        // 单位二维码处理完毕，处理中爆数据
        resultList.addAll(collect.stream().filter(item->UniqueBarCodeTypeEnum.CHINA_BLAST.getCode().equals(item.getType())).collect(Collectors.toList()));
        return resultList;
    }

    private BatchesBarCodeDTO galaxyDto2StoreOrderDto(OrderUniqueBarCodeDTO it){
        if(it == null){
            return null;
        }
        BatchesBarCodeDTO dto = new BatchesBarCodeDTO();
        dto.setBarCode(it.getUniBarCode())
                .setType(it.getType())
                .setBatches(it.getBatches())
                .setExpiration(it.getExpiration())
                .setManufacturer(it.getManufacturer())
                .setProductionDate(DateUtils.format("yyyy-MM-dd", it.getProductionDate()))
                .setExterior(it.getExterior())
                .setGasBottleBarcode(it.getGasBottleBarcode())
                .setStatus(it.getStatus())
                .setBatchesStatus(it.getBatchesStatus())
                .setInventoryStatus(it.getInventoryStatus())
                .setTransactionStatus(it.getTransactionStatus())
                .setPrinted(it.getPrinted())
                .setQuantity(it.getTotal() != null ? it.getTotal() : 1)
                .setReturnReason(it.getReturnReason())
                .setReturnDescription(it.getReturnDescription());
        return dto;
    }

    /**
     * 获取批次模式下聚合用到的字段
     * @return 需要聚合的字段的getter方法
     */
    public List<Function<OrderUniqueBarCodeDTO, ?>> getBatchModeNeedAggFieldGetter(){
        return New.list(OrderUniqueBarCodeDTO::getBatches,
                OrderUniqueBarCodeDTO::getExpiration,
                OrderUniqueBarCodeDTO::getManufacturer,
                OrderUniqueBarCodeDTO::getProductionDate);
    }

    private boolean completeBatchDataFill(OrderUniqueBarCodeDTO orderUniqueBarCodeDTO){
        List<Function<OrderUniqueBarCodeDTO, ?>> functionList = this.getBatchModeNeedAggFieldGetter();
        for(Function<OrderUniqueBarCodeDTO, ?> function : functionList){
            Object fieldValue = function.apply(orderUniqueBarCodeDTO);
            if(fieldValue instanceof String && StringUtils.isNotBlank((String) fieldValue)){
                return true;
            }
            if(fieldValue != null){
                return true;
            }
        }
        return false;
    }

    /**
     * 获取批次模式下需要进行聚合/填写的字段
     * @return 需要填写的字段的getter方法
     */
    private List<Function<OrderUniqueBarCodeDTO, ?>> getBatchModeValidateFieldGetter(String orgCode, Integer orderId){
        if(OrgEnum.SHEN_ZHEN_SHI_DI_SAN_REN_MIN_YI_YUAN.getCode().equals(orgCode)){
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
            if(orderDetailDOList.stream().anyMatch(detail-> CategoryConstant.EXPERIMENT_ANIMAL_ID == detail.getFirstCategoryId())){
                // 深圳三院动物类不读配置，写死
                return New.list(OrderUniqueBarCodeDTO::getManufacturer);
            }
        }
        List<BaseConfigDTO> baseConfigList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, New.list(ConfigCodeEnum.PRODUCT_FILL_IN_BATCH_INFO.name()));
        if(CollectionUtils.isNotEmpty(baseConfigList) && StringUtils.isNotBlank(baseConfigList.get(0).getConfigValue())){
            List<String> configValues = New.list(baseConfigList.get(0).getConfigValue().split(","));
            List<Function<OrderUniqueBarCodeDTO, ?>> resultList = new ArrayList<>(configValues.size());
            if(configValues.contains(ProductFillInBatchInfoEnum.BATCHES.getKey())){
                // 批次号
                resultList.add(OrderUniqueBarCodeDTO::getBatches);
            }
            if(configValues.contains(ProductFillInBatchInfoEnum.EXPIRATION.getKey())){
                // 有效期
                resultList.add(OrderUniqueBarCodeDTO::getExpiration);
            }
            if(configValues.contains(ProductFillInBatchInfoEnum.MANUFACTURER.getKey())){
                // 生产厂家
                resultList.add(OrderUniqueBarCodeDTO::getManufacturer);
            }
            if(configValues.contains(ProductFillInBatchInfoEnum.PRODUCTION_DATE.getKey())){
                // 生产日期
                resultList.add(OrderUniqueBarCodeDTO::getProductionDate);
            }
            return resultList;
        }
        return New.emptyList();
    }

    /**
     * 条形码数据转为批次数据
     * @param orderDetailDOList 订单商品明细
     * @param orderUniqueBarCodeDTOList 批次数据
     * @param isEachProductEachCode 是否一物一码模式
     * @return 批次vo
     */
    private OrderBatchesVO convertBarcodeData2BatchesVO(List<OrderDetailDO> orderDetailDOList, List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList, boolean isEachProductEachCode, boolean outputBarcodeQrImg){
        Map<Integer, OrderDetailDO> detailIdIdentityMap = DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, Function.identity());
        Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdGroupMap = orderUniqueBarCodeDTOList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId));
        List<OrderDetailBathesDTO> collect = new ArrayList<>(detailIdGroupMap.keySet().size());
        detailIdGroupMap.forEach((detailId, barCodeList) -> {
            OrderDetailDO detailDO = detailIdIdentityMap.get(detailId);
            OrderDetailBathesDTO dto = new OrderDetailBathesDTO()
                    .setDetailId(barCodeList.get(0).getOrderDetailId())
                    .setProductName(barCodeList.get(0).getProductName())
                    .setProductCode(barCodeList.get(0).getProductCode())
                    .setSpec(barCodeList.get(0).getSpec())
                    .setBrand(barCodeList.get(0).getBrand())
                    .setCasNo(detailDO == null ? null : detailDO.getCasno())
                    .setSecondCategoryId(detailDO == null ? null : detailDO.getSecondCategoryId())
                    .setSecondCategoryName(detailDO == null ? null : detailDO.getSecondCategoryName());
            if(isEachProductEachCode){
                dto.setBatches(this.wrapperBatches(barCodeList));
                if(outputBarcodeQrImg){
                    // 深圳湾实验室，输出二维码
                    dto.getBatches().forEach(item->{
                        if(UniqueBarCodeTypeEnum.ORG.getCode().equals(item.getType())){
                            try{
                                item.setBarCodeQrImg(QrCodeUtil.getBase64(item.getBarCode(), 120, 120));
                            }catch (Exception e){
                                logger.error("生成二维码图片失败", e);
                            }
                        }
                    });
                }
            }else {
                // 获取getter
                List<Function<OrderUniqueBarCodeDTO, ?>> functionList = this.getBatchModeNeedAggFieldGetter();
                dto.setBatches(this.wrapperBatches(this.groupBatchesByField(barCodeList, functionList)));
            }
            collect.add(dto);
        });

        List<OrderDetailBatchesVO> voList = collect.stream().map(OrderUniqueBarCodeTranslator::detailBatchesDTO2VO).collect(Collectors.toList());
        if(isEachProductEachCode){
            List<String> gasBottleBarcodes = voList.stream().map(OrderDetailBatchesVO::getBatches).flatMap(List::stream).map(BatchesBarCodeVO::getGasBottleBarcode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(gasBottleBarcodes)){
                List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(gasBottleBarcodes);
                Map<String, GasBottleVO> gasBottleCodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
                voList.forEach(item->{
                    if(CollectionUtils.isNotEmpty(item.getBatches())){
                        item.getBatches().forEach(batch-> batch.setGasBottle(gasBottleCodeIdentityMap.get(batch.getGasBottleBarcode())));
                    }
                });
            }
        }

        OrderBatchesVO result = new OrderBatchesVO();
        result.setOrderDetailBathes(voList);
        return result;
    }

    /**
     * 生成订单二维码信息
     *
     * @param orderNo  订单号
     * @param typeList 一物一码类型 {@link UniqueBarCodeTypeEnum}
     */
    public Boolean generateOrderBarCode(String orderNo, List<Integer> typeList) {
        Preconditions.hasLength(orderNo, "订单号不能为空！");
        List<OrderUniqueBarCodeDTO> byOrderNo = findByOrderNo(orderNo, typeList);
        if (CollectionUtils.isNotEmpty(byOrderNo)) {
            return true;
        }
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, "订单不存在！");
        List<OrderDetailDO> detailDOS = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        Set<Integer> firstCategoryIdSet = detailDOS.stream().map(OrderDetailDO::getFirstCategoryId).collect(Collectors.toSet());
        // 深圳湾订单，商品类型是危险化学品，需要调用外部接口生成二维码
        if (Objects.equals(OrgEnum.SHEN_ZHEN_WAN_SHI_YAN_SHI.getValue(), orderMasterDO.getFuserid())
                && firstCategoryIdSet.contains(CategoryConstant.DANGEROUS_ID)) {
            // 二次校验
            List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOS = findByOrderNo(orderNo, typeList);
            if (CollectionUtils.isNotEmpty(orderUniqueBarCodeDTOS)) {
                return true;
            }
            final String uniqueKey = "generateOrderBarCode:" + orderNo;
            cacheClient.controlRepeatOperation(uniqueKey, 5, "商品二维码生成中，请稍后再试！");
            generateGoodsQrCodeBySZWSYS(orderMasterDO.getId());
            cacheClient.removeCache(uniqueKey);
        }
        return true;
    }

    /**
     * 打印修改日志
     * @param prefix 打印前缀
     * @param batchDataList 批次数据
     * @param functionList 列表数据
     */
    private void logSuppModifyBatches(String prefix,List<OrderUniqueBarCodeDTO> batchDataList, List<Function<OrderUniqueBarCodeDTO, ?>> functionList){
        if(CollectionUtils.isEmpty(batchDataList)){
            logger.info(prefix + "没有批次数据");
        }
        List<OrderUniqueBarCodeDTO> groupedBatches = this.groupBatchesByField(batchDataList, functionList);
        Map<Integer, List<OrderUniqueBarCodeDTO>> orderDetailIdGroupedBatchesMap = DictionaryUtils.groupBy(groupedBatches, OrderUniqueBarCodeDTO::getOrderDetailId);
        StringBuilder stringBuilder = new StringBuilder().append(prefix);
        for(List<OrderUniqueBarCodeDTO> group : orderDetailIdGroupedBatchesMap.values()){
            stringBuilder.append("  ").append(group.get(0).getProductName()).append(":\n");
            for(OrderUniqueBarCodeDTO item : group){
                if(StringUtils.isNotBlank(item.getBatches())){
                    stringBuilder.append("      ").append("批号:").append(item.getBatches()).append(",");
                }
                if(StringUtils.isNotBlank(item.getExpiration())){
                    stringBuilder.append("有效期:").append(item.getExpiration()).append(",");
                }
                if(StringUtils.isNotBlank(item.getBatches())){
                    stringBuilder.append("生产厂家:").append(item.getManufacturer()).append(",");
                }
                if(item.getProductionDate() != null){
                    stringBuilder.append("生产日期:").append(DateUtils.format("yyyy-MM-dd", item.getProductionDate())).append(",");
                }
                stringBuilder.append("数量:").append(item.getTotal()).append(";\n");
            }
            stringBuilder.append("\n");
        }
        logger.info(stringBuilder.toString());
    }
}
