package com.ruijing.store.order.base.minor.mapper;

import com.ruijing.store.order.base.minor.model.RefCouponPurchaserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @Date 2020/4/2 0002 17:18
 * @Version 1.0
 * @Desc:描述
 */
@Mapper
public interface RefCouponPurchaserDOMapper {
    int deleteByPrimaryKey(String id);

    int insert(RefCouponPurchaserDO record);

    int insertSelective(RefCouponPurchaserDO record);

    RefCouponPurchaserDO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(RefCouponPurchaserDO record);

    int updateByPrimaryKey(RefCouponPurchaserDO record);


}