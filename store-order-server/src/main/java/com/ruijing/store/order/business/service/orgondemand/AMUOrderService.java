package com.ruijing.store.order.business.service.orgondemand;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.upload.client.FileUploadClient;
import com.ruijing.fundamental.upload.client.FileUploadResp;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.general.dto.FundCardSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderAggregationResultDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerParamDTO;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturnByDept;
import com.ruijing.store.order.base.excel.dto.OrderPrintDetailDTO;
import com.ruijing.store.order.base.excel.dto.ProductPrintDetailDTO;
import com.ruijing.store.order.base.minor.mapper.OrderRemarkMapper;
import com.ruijing.store.order.base.minor.model.OrderRemark;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.business.service.GoodsReturnService;
import com.ruijing.store.order.business.service.OrderExportService;
import com.ruijing.store.order.business.service.orderexport.cellstyle.CellStyle;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.hms.HmsOrderGWController;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.department.MiniDepartmentDTO;
import com.ruijing.store.user.api.enums.department.DepartmentTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description: Army Medical University 陆军军医大学 个性化订单需求
 * @DateTime: 2021/8/4 14:35
 */
@Service
public class AMUOrderService {

    private final String APPROVAL_PASS = "审批通过";

    @Resource
    private OrderRemarkMapper orderRemarkMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderExportService orderExportService;

    @Resource
    private GoodsReturnService goodsReturnService;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    /**
     * 配置的陆军军医 根部门id，现网默认40309，测试环境是29079
     */
    @PearlValue(key="AMU_ROOT_DEPT_ID", defaultValue = "40309")
    private static Integer ROOT_DEPT_ID;

    /**
     * 配置默认的单位
     */
    @PearlValue(key="AMU_ORG_ID", defaultValue = "118")
    private static Integer ORG_ID;

    /**
     * 需要去除的测试供应商id列表，宏兴兴旺天府
     */
    private static final List<Integer> EXCLUDE_SUPP_ID_LIST = New.list(655, 58094, 60780);

    private static final Logger LOGGER = LoggerFactory.getLogger(AMUOrderService.class);

    /**
     * 按部门层级导出订单金额的 总入口
     * @param request
     * @return 明细[0]与汇总[1]两个url，
     */
    public List<String> exportDeptOrderAmount(OrderListRequest request) {
        // 获取根部门逻辑, 单位逻辑
        List<Integer> deptIdList = request.getDeptIdList();
        Integer rootDeptId = CollectionUtils.isNotEmpty(deptIdList) ? rootDeptId = deptIdList.get(0) : ROOT_DEPT_ID;
        Integer orgId = request.getOrgId() != null ? request.getOrgId() : ORG_ID;
        // 构建 所有的采购部门id 以及他们对应的id->dept 的map
        List<MiniDepartmentDTO> allChildTree = userClient.getAllChildTree(orgId, rootDeptId);
        List<Integer> purchaseDeptIdList = New.list();
        Map<Integer, MiniDepartmentDTO> deptIdDeptMap = new HashMap<>();
        for (MiniDepartmentDTO dept : allChildTree) {
            if (dept != null && Objects.equals(DepartmentTypeEnum.PURCHASING.getValue(), dept.getDepartmentType())) {
                purchaseDeptIdList.add(dept.getId());
            }
            deptIdDeptMap.put(dept.getId(), dept);
        }
        List<Integer> excludeStatusList = New.list(OrderStatusEnum.Close.getValue(),
                OrderStatusEnum.SupplierApplyToCancel.getValue(),
                OrderStatusEnum.PurchaseApplyToCancel.getValue(),
                OrderStatusEnum.ORDER_SPLIT_UP.getValue());

        // 退货的信息，当前时间、部门、去除状态、去除测试的供应商、聚合的维度
        CompletableFuture<List<GoodsReturnByDept>> sumReturnAmountByDeptFuture = AsyncExecutor.callAsync(() ->
                orderMasterMapper.sumReturnAmountByDept(orgId,
                        DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, request.getStartDate()),
                        DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, request.getEndDate()),
                        excludeStatusList,
                        EXCLUDE_SUPP_ID_LIST)
        ).exceptionally(throwable -> {
            throw new IllegalStateException("获取部门维度的退货总额失败，请检查代码");
        });

        // 查找所有采购部门信息(时间、部门、去除部分状态、去除测试供应商、指明聚合的维度）
        StatisticsManagerParamDTO paramDTO = new StatisticsManagerParamDTO();
        paramDTO.setStartTime(request.getStartDate());
        paramDTO.setEndTime(request.getEndDate());
        paramDTO.setNotStatusList(excludeStatusList);
        paramDTO.setOrgId(orgId);
        paramDTO.setAggField(OrderSearchFieldEnum.DEPARTMENT_ID);
        paramDTO.setDepartmentIds(purchaseDeptIdList);
        paramDTO.setExcludeSuppIdList(EXCLUDE_SUPP_ID_LIST);
        paramDTO.setSpecies(OrderSpeciesEnum.NORMAL.getValue());
        paramDTO.setTopSize(10000);
        paramDTO.setPageSize(0);
        List<OrderAggregationResultDTO> aggResList = orderSearchBoostService.aggOrderAmountAndCount(paramDTO);

        // 退货map构造
        List<GoodsReturnByDept> sumReturnAmountByDeptId = New.list();
        try {
            sumReturnAmountByDeptId = sumReturnAmountByDeptFuture.get();
        } catch (Exception e) {
            Preconditions.isTrue(false, "获取部门维度的退货总额失败，请检查代码");
        }
        Preconditions.notEmpty(sumReturnAmountByDeptId, "获取部门维度的退货总额失败或此月份无单据，请检查代码和确认此单位订单情况");
        Map<Integer, Double> deptIdReturnAmountMap = sumReturnAmountByDeptId.stream().collect(Collectors.toMap(GoodsReturnByDept::getDeptId, GoodsReturnByDept::getSumReturnAmount, (ov, nv) -> nv));

        // 父部门的总计；
        Map<Integer, BigDecimal> parentIdAmountMap = new HashMap<>();
        for (OrderAggregationResultDTO aggRes : aggResList) {
            Double returnAmount = deptIdReturnAmountMap.get(aggRes.getAggFieldId().intValue());
            MiniDepartmentDTO dept = deptIdDeptMap.get(aggRes.getAggFieldId().intValue());
            BigDecimal curDeptAmountAfterReturn = BigDecimal.valueOf(aggRes.getAmount() - returnAmount);
            if (parentIdAmountMap.containsKey(dept.getParentId())) {
                BigDecimal sumUpRes = parentIdAmountMap.get(dept.getParentId()).add(curDeptAmountAfterReturn);
                parentIdAmountMap.put(dept.getParentId(), sumUpRes);
            } else {
                parentIdAmountMap.put(dept.getParentId(), curDeptAmountAfterReturn);
            }
        }
        // 导出主流程
        List<String> resUrlList = this.exportDeptAmount(request, deptIdDeptMap, aggResList, parentIdAmountMap, deptIdReturnAmountMap);
        return resUrlList;
    }

    /**
     * 部门导出主流程
     * @param request 可有可无的，主要用于构造导出的名字，需要请求的时间而已
     * @param deptIdDeptMap 部门id和minidepartment的对应关系
     * @param aggResList 聚合搜索的结果，deptid和amount的对应关系
     * @param parentIdAmountMap 构造的parentid和amount的对应关系
     * @return [明细导出url，汇总导出url]
     */
    private List<String> exportDeptAmount(OrderListRequest request, Map<Integer, MiniDepartmentDTO> deptIdDeptMap,
                                          List<OrderAggregationResultDTO> aggResList, Map<Integer, BigDecimal> parentIdAmountMap,
                                          Map<Integer, Double> deptIdReturnAmountMap) {
        ExcelWriter writer = null;
        String fileSuffix = request.getStartDate().split(" ")[0] + "_" + request.getEndDate().split(" ")[0];
        File fileDetail = new File("部门订单采购部门明细_"+ fileSuffix + ".xlsx");
        File fileSummary = new File("部门订单管理部门汇总_"+ fileSuffix + ".xlsx");
        String detailSheetName = "订单明细";
        String summarySheetName = "订单汇总";
        List<String> resUrlList = null;
        Transaction transaction = Cat.newTransaction("AMUOrderService", "exportDeptOrderAmount");
        try {
            // 导出明细，去除退货
            writer = EasyExcelFactory.write(fileDetail).build();
            WriteSheet sheet = EasyExcelFactory.writerSheet(detailSheetName)
                    .head(this.deptDetailStatHeader())
                    .registerWriteHandler(CellStyle.getCellStyleStrategy())
                    .build();

            writer.write(this.constructDeptDetailList(aggResList, deptIdDeptMap, deptIdReturnAmountMap), sheet);
            writer.finish();

            // 导出汇总，母单位的统计中已经去除退货了
            writer = EasyExcelFactory.write(fileSummary).build();
            sheet = EasyExcelFactory.writerSheet(summarySheetName)
                    .head(this.deptSummaryStatHeader())
                    .registerWriteHandler(CellStyle.getCellStyleStrategy())
                    .build();
            writer.write(this.constructDeptSummaryList(parentIdAmountMap, deptIdDeptMap), sheet);
            writer.finish();
            // 返回两个url,不存表，明细
            resUrlList = New.listWithCapacity(2);
            FileUploadClient fileUploadClient = UploadFileClient.getFileUploadClientById(UploadFileClient.BUYER_ORDER_LIST_ID);
            FileUploadResp fileUploadResp = fileUploadClient.uploadFile(fileDetail);
            Preconditions.isTrue(fileUploadResp.isSuccess(), fileUploadResp.getMsg());
            String detailUrl = fileUploadResp.getAbsolutePath();
            resUrlList.add(detailUrl);
            // 汇总
            fileUploadResp = fileUploadClient.uploadFile(fileSummary);
            Preconditions.isTrue(fileUploadResp.isSuccess(), fileUploadResp.getMsg());
            String summaryUrl = fileUploadResp.getAbsolutePath();
            resUrlList.add(summaryUrl);
            transaction.setSuccess();
        } catch (Exception e) {
            transaction.setStatus(CatUtils.buildStackInfo("部门明细汇总导出异常！", e));
        } finally {
            if (writer != null) {
                writer.finish();
            }
            if (fileDetail != null) {
                fileDetail.delete();
            }
            if (fileSummary != null) {
                fileSummary.delete();
            }
            transaction.complete();
        }
        return resUrlList;
    }

    /**
     * 构造 采购部门-管理部门-有效金额
     * @param aggResList
     * @param deptIdDeptMap
     * @return
     */
    private List<List<Object>> constructDeptDetailList(List<OrderAggregationResultDTO> aggResList,
                                                       Map<Integer, MiniDepartmentDTO> deptIdDeptMap,
                                                       Map<Integer, Double> deptIdReturnAmountMap) {
        aggResList.sort(Comparator.comparing(OrderAggregationResultDTO::getAmount).reversed());
        List<List<Object>> resList = New.listWithCapacity(aggResList.size());
        for (OrderAggregationResultDTO aggRes : aggResList) {
            List<Object> curLine = New.list();
            int curDeptId = aggRes.getAggFieldId().intValue();
            Double returnAmount = deptIdReturnAmountMap.get(curDeptId);
            MiniDepartmentDTO purchaseDept = deptIdDeptMap.get(curDeptId);
            MiniDepartmentDTO parentDept = deptIdDeptMap.get(purchaseDept.getParentId());
            curLine.add(purchaseDept == null ? null : purchaseDept.getName());
            curLine.add(parentDept == null ? null : parentDept.getName());
            curLine.add(BigDecimal.valueOf(aggRes.getAmount() - returnAmount).setScale(2, BigDecimal.ROUND_HALF_UP));
            resList.add(curLine);
        }
        return resList;
    }

    /**
     * 构造 序号-教研室名称-总金额
     * @param parentIdAmountMap
     * @param deptIdDeptMap
     * @return
     */
    private List<List<Object>> constructDeptSummaryList(Map<Integer, BigDecimal> parentIdAmountMap,
                                                       Map<Integer, MiniDepartmentDTO> deptIdDeptMap) {
        List<Pair<Integer, BigDecimal>> parentIdAmountList = New.list();
        for (Map.Entry<Integer, BigDecimal> entry : parentIdAmountMap.entrySet()) {
            parentIdAmountList.add(Pair.of(entry.getKey(), entry.getValue()));
        }
        // 排序
        Collections.sort(parentIdAmountList, (o1, o2) -> o2.getRight().compareTo(o1.getRight()));

        List<List<Object>> resList = New.listWithCapacity(parentIdAmountList.size());
        for (int i = 0; i < parentIdAmountList.size(); i++) {
            Pair<Integer, BigDecimal> parentIdAmount = parentIdAmountList.get(i);
            List<Object> curLine = New.list();
            curLine.add(i+1);
            curLine.add(deptIdDeptMap.get(parentIdAmount.getLeft()) == null ? null : deptIdDeptMap.get(parentIdAmount.getLeft()).getName());
            curLine.add(parentIdAmount.getRight());
            resList.add(curLine);
        }
        return resList;
    }


    private List<List<String>> deptDetailStatHeader() {
        return New.list(
                New.list("采购组"),
                New.list("管理部门") ,
                New.list("订单有效金额"));
    }

    private List<List<String>> deptSummaryStatHeader() {
        return New.list(
                New.list("序号"),
                New.list("教研室名称"),
                New.list("总金额（元）"));
    }
}
