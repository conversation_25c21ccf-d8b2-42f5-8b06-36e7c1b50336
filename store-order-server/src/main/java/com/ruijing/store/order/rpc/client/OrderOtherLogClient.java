package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.order.base.log.dto.OrderDockingLogDTO;
import com.reagent.order.base.log.service.OrderOtherLogRpcService;
import com.reagent.order.base.order.dto.OrderFileOperationLogDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogBatchQueryRequestDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogRequestDTO;
import com.reagent.order.base.order.service.OrderFileOperationLogRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 订单其他业务日志
 *
 * <AUTHOR>
 */
@ServiceClient
public class OrderOtherLogClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderOtherLogRpcService orderOtherLogRpcService;

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderFileOperationLogRpcService orderFileOperationLogRpcService;

    /**
     * 记录对接日志
     *
     * @param orderNo    订单号
     * @param orgCode    机构号
     * @param paramInfo  入参
     * @param resultInfo 结果
     * @param operation  操作
     * @param result     结果
     */
    public void createOrderDockingLog(String orderNo, String orgCode, String paramInfo, String resultInfo, String operation, String result) {
        OrderDockingLogDTO orderDockingLogDTO = new OrderDockingLogDTO();
        orderDockingLogDTO.setDockingNumber(orderNo);
        orderDockingLogDTO.setOrgCode(orgCode);
        orderDockingLogDTO.setParamInfo(StringUtils.truncate(paramInfo, 200));
        orderDockingLogDTO.setExtraInfo(resultInfo);
        orderDockingLogDTO.setResult(result);
        // 处理调用失败回调的报文过长的问题, 截取长度800
        if (StringUtils.isNotBlank(resultInfo) && resultInfo.length() > 800) {
            orderDockingLogDTO.setExtraInfo(resultInfo.substring(0, 800));
        }
        // 处理调用失败回调的报文过长的问题, 截取长度800
        if (StringUtils.isNotBlank(result) && result.length() > 200) {
            orderDockingLogDTO.setResult(result.substring(0, 200));
        }
        orderDockingLogDTO.setOperation(operation);
        this.saveDockingLog(orderDockingLogDTO);
    }

    @ServiceLog(description = "进入保存财务日志方法", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void saveDockingLog(OrderDockingLogDTO logDTO) {
        RemoteResponse response = orderOtherLogRpcService.insertOrderDockingLog(logDTO);
        Preconditions.isTrue(response.isSuccess(), "保存财务日志失败!" + JsonUtils.toJson(logDTO));

    }

    @ServiceLog(description = "批量查询订单文件操作日志", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderFileOperationLogDTO> batchQueryFileLog(OrderFileOperationLogBatchQueryRequestDTO requestDTO) {
        Preconditions.notNull(requestDTO, "批量查询请求参数不能为空");
        List<Integer> orderIds = requestDTO.getOrderIds();
        List<Integer> logIds = requestDTO.getLogIds();
        // 校验不允许同时通过orderIds和logIds查询
        Preconditions.isTrue(!(CollectionUtils.isNotEmpty(orderIds) && CollectionUtils.isNotEmpty(logIds)), "不能同时指定订单ID和日志ID进行查询");

        List<OrderFileOperationLogDTO> resultList = New.list();

        // 按订单ID查询
        if (CollectionUtils.isNotEmpty(orderIds)) {
            // 分批查询
            List<List<Integer>> batchOrderIds = Lists.partition(orderIds, 200);
            for (List<Integer> batchOrderId : batchOrderIds) {
                OrderFileOperationLogBatchQueryRequestDTO batchRequest = new OrderFileOperationLogBatchQueryRequestDTO();
                batchRequest.setOrderIds(New.list(batchOrderId));
                RemoteResponse<List<OrderFileOperationLogDTO>> response = orderFileOperationLogRpcService.batchQuery(batchRequest);
                Preconditions.isTrue(response.isSuccess(), response.getMsg());

                if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                    resultList.addAll(response.getData());
                }
            }
        }

        // 按日志ID查询
        if (CollectionUtils.isNotEmpty(logIds)) {
            // 分批查询
            List<List<Integer>> batchLogIds = Lists.partition(logIds, 200);
            for (List<Integer> batchLogId : batchLogIds) {
                OrderFileOperationLogBatchQueryRequestDTO batchRequest = new OrderFileOperationLogBatchQueryRequestDTO();
                batchRequest.setLogIds(New.list(batchLogId));
                RemoteResponse<List<OrderFileOperationLogDTO>> response = orderFileOperationLogRpcService.batchQuery(batchRequest);
                Preconditions.isTrue(response.isSuccess(), response.getMsg());

                if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                    resultList.addAll(response.getData());
                }
            }
        }
        return resultList;
    }

    /**
     * 批量保存订单文件操作日志
     *
     * @param fileOperationLogs 批量保存请求参数
     * @return 是否保存成功
     */
    @ServiceLog(description = "批量保存订单文件操作日志,单次200", operationType = OperationType.WRITE)
    public void batchSaveFileLog(List<OrderFileOperationLogRequestDTO> fileOperationLogs) {
        Preconditions.notNull(fileOperationLogs, "批量保存请求参数不能为空");
        RemoteResponse<Boolean> response = orderFileOperationLogRpcService.batchSave(fileOperationLogs);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

}
