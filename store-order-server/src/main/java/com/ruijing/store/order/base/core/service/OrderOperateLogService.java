package com.ruijing.store.order.base.core.service;

import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOperationLogVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.PurchaseOrderAllRelateLogVO;

import java.util.List;

/*
 * 订单操作日志相关接口
 */
public interface OrderOperateLogService {

    /**
     * 批量获取订单操作日志记录
     *
     * @param orderMasterList
     * @return
     */
    List<OrderOperationLogVO> batchOrderOperationLog(List<OrderMasterDO> orderMasterList);

    /**
     * 查询订单关联的所有业务日志
     */
    List<PurchaseOrderAllRelateLogVO> listOrderAllRelateLog(List<Integer> orderIdList);
    
    /**
     * 查询单个订单关联的所有业务日志
     * 
     * @param orderId 订单ID
     * @return 订单关联的所有业务日志
     */
    List<PurchaseOrderAllRelateLogVO> getOrderAllRelateLog(Integer orderId);

    /**
     * 获取订单操作日志记录
     */
    List<OrderOperationLogVO> orderOperationLog(Integer orderId, String orderNo);

    /**
     * 获取代配送日志
     *
     * @param orderId 订单id
     * @return 操作日志
     */
    List<OrderOperationLogVO> getDeliveryProxyOprLog(Integer orderId);

    /**
     * @description: 根据订单信息获取供应商或采购人取消操作日志
     * @date: 2021/1/14 19:22
     * @author: zengyanru
     */
    List<OrderApprovalInfoVO> getOrderApprovalInfo(OrderMasterDO orderMaster);

}
