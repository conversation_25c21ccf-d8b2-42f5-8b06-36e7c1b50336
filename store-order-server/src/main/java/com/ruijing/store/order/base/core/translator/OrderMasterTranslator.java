package com.ruijing.store.order.base.core.translator;

import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.constant.OrderDateConstant;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @description: 订单主表 转换类
 * @author: zhuk
 * @create: 2019-07-08 10:26
 **/
public class OrderMasterTranslator {


    /**
     * orderMasterDO 转DTO
     * @param orderMasterDO
     * @return
     */
    public static OrderMasterDTO orderMasterDO2OrderMasterDTO(OrderMasterDO orderMasterDO){
        if (orderMasterDO == null){
            return null;
        }
        OrderMasterDTO orderMasterDTO = new OrderMasterDTO();
        orderMasterDTO.setId(orderMasterDO.getId());
        orderMasterDTO.setFmasterguid(orderMasterDO.getFmasterguid());
        orderMasterDTO.setFtbuyappid(orderMasterDO.getFtbuyappid());
        orderMasterDTO.setForderno(orderMasterDO.getForderno());
        orderMasterDTO.setForderdate(orderMasterDO.getForderdate());
        orderMasterDTO.setFbuyerid(orderMasterDO.getFbuyerid());
        orderMasterDTO.setFbuyercode(orderMasterDO.getFbuyercode());
        orderMasterDTO.setFbuyername(orderMasterDO.getFbuyername());
        orderMasterDTO.setFbuyeremail(orderMasterDO.getFbuyeremail());
        orderMasterDTO.setFbuyercontactman(orderMasterDO.getFbuyercontactman());
        orderMasterDTO.setFbuyertelephone(orderMasterDO.getFbuyertelephone());
        orderMasterDTO.setFbuydepartmentid(orderMasterDO.getFbuydepartmentid());
        orderMasterDTO.setFbuydepartment(orderMasterDO.getFbuydepartment());
        orderMasterDTO.setFsuppid(orderMasterDO.getFsuppid());
        orderMasterDTO.setFsuppcode(orderMasterDO.getFsuppcode());
        orderMasterDTO.setFsuppname(orderMasterDO.getFsuppname());
        orderMasterDTO.setFbiderdeliveryplace(orderMasterDO.getFbiderdeliveryplace());
        orderMasterDTO.setForderamounttotal(orderMasterDO.getForderamounttotal());
        orderMasterDTO.setFundStatus(orderMasterDO.getFundStatus());
        orderMasterDTO.setFailedReason(orderMasterDO.getFailedReason());
        orderMasterDTO.setStatus(orderMasterDO.getStatus());
        orderMasterDTO.setFconfirmdate(orderMasterDO.getFconfirmdate());
        orderMasterDTO.setFconfirmmanid(orderMasterDO.getFconfirmmanid());
        orderMasterDTO.setFconfirmman(orderMasterDO.getFconfirmman());
        orderMasterDTO.setFcanceldate(orderMasterDO.getFcanceldate());
        orderMasterDTO.setFcancelmanid(orderMasterDO.getFcancelmanid());
        orderMasterDTO.setFcancelman(orderMasterDO.getFcancelman());
        orderMasterDTO.setFdeliverydate(orderMasterDO.getFdeliverydate());
        orderMasterDTO.setFdeliverymanid(orderMasterDO.getFdeliverymanid());
        orderMasterDTO.setFdeliveryman(orderMasterDO.getFdeliveryman());
        orderMasterDTO.setFlastreceivedate(orderMasterDO.getFlastreceivedate());
        orderMasterDTO.setFlastreceivemanid(orderMasterDO.getFlastreceivemanid());
        orderMasterDTO.setFlastreceiveman(orderMasterDO.getFlastreceiveman());
        orderMasterDTO.setFassessdate(orderMasterDO.getFassessdate());
        orderMasterDTO.setFassessmanid(orderMasterDO.getFassessmanid());
        orderMasterDTO.setFassessman(orderMasterDO.getFassessman());
        orderMasterDTO.setProjectid(orderMasterDO.getProjectid());
        orderMasterDTO.setProjectnumber(orderMasterDO.getProjectnumber());
        orderMasterDTO.setProjecttitle(orderMasterDO.getProjecttitle());
        orderMasterDTO.setFuserid(orderMasterDO.getFuserid());
        orderMasterDTO.setFusercode(orderMasterDO.getFusercode());
        orderMasterDTO.setFusername(orderMasterDO.getFusername());
        orderMasterDTO.setStatementId(orderMasterDO.getStatementId());
        orderMasterDTO.setFcancelreason(orderMasterDO.getFcancelreason());
        orderMasterDTO.setFrefuseCancelReason(orderMasterDO.getFrefuseCancelReason());
        orderMasterDTO.setShutDownDate(orderMasterDO.getShutDownDate());
        orderMasterDTO.setDeliveryInfo(orderMasterDO.getDeliveryInfo());
        orderMasterDTO.setDeliveryNo(orderMasterDO.getDeliveryNo());
        orderMasterDTO.setReturnAmount(orderMasterDO.getReturnAmount());
        orderMasterDTO.setFrefuseCancelDate(orderMasterDO.getFrefuseCancelDate());
        orderMasterDTO.setFdeliveryid(orderMasterDO.getFdeliveryid());
        orderMasterDTO.setBidOrderId(orderMasterDO.getBidOrderId());
        orderMasterDTO.setOrderType(orderMasterDO.getOrderType());
        orderMasterDTO.setReceivePicUrls(orderMasterDO.getReceivePicUrls());
        orderMasterDTO.setTpiProjectId(orderMasterDO.getTpiProjectId());
        orderMasterDTO.setOriginalAmount(orderMasterDO.getOriginalAmount());
        orderMasterDTO.setInventoryStatus(orderMasterDO.getInventoryStatus());
        orderMasterDTO.setSpecies(orderMasterDO.getSpecies());
        orderMasterDTO.setUpdateTime(orderMasterDO.getUpdateTime());
        orderMasterDTO.setInStateTime(orderMasterDO.getInStateTime());
        orderMasterDTO.setPurchaseRootinType(orderMasterDO.getPurchaseRootinType());
        orderMasterDTO.setCarryFee(orderMasterDO.getCarryFee());
        orderMasterDTO.setInvoiceTitleId(orderMasterDO.getInvoiceTitleId());
        orderMasterDTO.setInvoiceTitle(orderMasterDO.getInvoiceTitle());
        orderMasterDTO.setInvoiceTitleNumber(orderMasterDO.getInvoiceTitleNumber());
        orderMasterDTO.setFundType(orderMasterDO.getFundType());
        orderMasterDTO.setFundTypeName(orderMasterDO.getFundTypeName());
        orderMasterDTO.setCreateTime(orderMasterDO.getCreateTime());
        orderMasterDTO.setFlowId(orderMasterDO.getFlowId());
        orderMasterDTO.setStatementStatus(orderMasterDO.getStatementStatus());
        orderMasterDTO.setOldFlag(OrderDateConstant.isOldOrderForView(orderMasterDTO.getFusercode(),orderMasterDTO.getForderdate()));
        orderMasterDTO.setDeptParentId(orderMasterDO.getDeptParentId());
        orderMasterDTO.setDeptParentName(orderMasterDO.getDeptParentName());
        orderMasterDTO.setFinishDate(orderMasterDO.getFinishDate());
        return orderMasterDTO;
    }


    /**
     * orderMasterDTO 转DO
     * @param orderMasterDTO
     * @return
     */
    public static OrderMasterDO dtoToOrderMasterDO(OrderMasterDTO orderMasterDTO){
        if (orderMasterDTO == null){
            return null;
        }
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(orderMasterDTO.getId());
        orderMasterDO.setFmasterguid(orderMasterDTO.getFmasterguid());
        orderMasterDO.setFtbuyappid(orderMasterDTO.getFtbuyappid());
        orderMasterDO.setForderno(orderMasterDTO.getForderno());
        orderMasterDO.setForderdate(orderMasterDTO.getForderdate());
        orderMasterDO.setFbuyerid(orderMasterDTO.getFbuyerid());
        orderMasterDO.setFbuyercode(orderMasterDTO.getFbuyercode());
        orderMasterDO.setFbuyername(orderMasterDTO.getFbuyername());
        orderMasterDO.setFbuyeremail(orderMasterDTO.getFbuyeremail());
        orderMasterDO.setFbuyercontactman(orderMasterDTO.getFbuyercontactman());
        orderMasterDO.setFbuyertelephone(orderMasterDTO.getFbuyertelephone());
        orderMasterDO.setFbuydepartmentid(orderMasterDTO.getFbuydepartmentid());
        orderMasterDO.setFbuydepartment(orderMasterDTO.getFbuydepartment());
        orderMasterDO.setFsuppid(orderMasterDTO.getFsuppid());
        orderMasterDO.setFsuppcode(orderMasterDTO.getFsuppcode());
        orderMasterDO.setFsuppname(orderMasterDTO.getFsuppname());
        orderMasterDO.setFbiderdeliveryplace(orderMasterDTO.getFbiderdeliveryplace());
        orderMasterDO.setForderamounttotal(orderMasterDTO.getForderamounttotal());
        orderMasterDO.setFundStatus(orderMasterDTO.getFundStatus());
        orderMasterDO.setFailedReason(orderMasterDTO.getFailedReason());
        orderMasterDO.setStatus(orderMasterDTO.getStatus());
        orderMasterDO.setFconfirmdate(orderMasterDTO.getFconfirmdate());
        orderMasterDO.setFconfirmmanid(orderMasterDTO.getFconfirmmanid());
        orderMasterDO.setFconfirmman(orderMasterDTO.getFconfirmman());
        orderMasterDO.setFcanceldate(orderMasterDTO.getFcanceldate());
        orderMasterDO.setFcancelmanid(orderMasterDTO.getFcancelmanid());
        orderMasterDO.setFcancelman(orderMasterDTO.getFcancelman());
        orderMasterDO.setFdeliverydate(orderMasterDTO.getFdeliverydate());
        orderMasterDO.setFdeliverymanid(orderMasterDTO.getFdeliverymanid());
        orderMasterDO.setFdeliveryman(orderMasterDTO.getFdeliveryman());
        orderMasterDO.setFlastreceivedate(orderMasterDTO.getFlastreceivedate());
        orderMasterDO.setFlastreceivemanid(orderMasterDTO.getFlastreceivemanid());
        orderMasterDO.setFlastreceiveman(orderMasterDTO.getFlastreceiveman());
        orderMasterDO.setFassessdate(orderMasterDTO.getFassessdate());
        orderMasterDO.setFassessmanid(orderMasterDTO.getFassessmanid());
        orderMasterDO.setFassessman(orderMasterDTO.getFassessman());
        orderMasterDO.setProjectid(orderMasterDTO.getProjectid());
        orderMasterDO.setProjectnumber(orderMasterDTO.getProjectnumber());
        orderMasterDO.setProjecttitle(orderMasterDTO.getProjecttitle());
        orderMasterDO.setFuserid(orderMasterDTO.getFuserid());
        orderMasterDO.setFusercode(orderMasterDTO.getFusercode());
        orderMasterDO.setFusername(orderMasterDTO.getFusername());
        orderMasterDO.setStatementId(orderMasterDTO.getStatementId());
        orderMasterDO.setFcancelreason(orderMasterDTO.getFcancelreason());
        orderMasterDO.setFrefuseCancelReason(orderMasterDTO.getFrefuseCancelReason());
        orderMasterDO.setShutDownDate(orderMasterDTO.getShutDownDate());
        orderMasterDO.setDeliveryInfo(orderMasterDTO.getDeliveryInfo());
        orderMasterDO.setDeliveryNo(orderMasterDTO.getDeliveryNo());
        orderMasterDO.setReturnAmount(orderMasterDTO.getReturnAmount());
        orderMasterDO.setFrefuseCancelDate(orderMasterDTO.getFrefuseCancelDate());
        orderMasterDO.setFdeliveryid(orderMasterDTO.getFdeliveryid());
        orderMasterDO.setBidOrderId(orderMasterDTO.getBidOrderId());
        orderMasterDO.setOrderType(orderMasterDTO.getOrderType());
        orderMasterDO.setReceivePicUrls(orderMasterDTO.getReceivePicUrls());
        orderMasterDO.setTpiProjectId(orderMasterDTO.getTpiProjectId());
        orderMasterDO.setOriginalAmount(orderMasterDTO.getOriginalAmount());
        orderMasterDO.setInventoryStatus(orderMasterDTO.getInventoryStatus());
        orderMasterDO.setSpecies(orderMasterDTO.getSpecies());
        orderMasterDO.setUpdateTime(orderMasterDTO.getUpdateTime());
        orderMasterDO.setInStateTime(orderMasterDTO.getInStateTime());
        orderMasterDO.setPurchaseRootinType(orderMasterDTO.getPurchaseRootinType());
        orderMasterDO.setCarryFee(orderMasterDTO.getCarryFee());
        orderMasterDO.setInvoiceTitleId(orderMasterDTO.getInvoiceTitleId());
        orderMasterDO.setInvoiceTitle(orderMasterDTO.getInvoiceTitle());
        orderMasterDO.setInvoiceTitleNumber(orderMasterDTO.getInvoiceTitleNumber());
        orderMasterDO.setFundType(orderMasterDTO.getFundType());
        orderMasterDO.setFundTypeName(orderMasterDTO.getFundTypeName());
        orderMasterDO.setFlowId(orderMasterDTO.getFlowId());
        orderMasterDO.setDeptParentId(orderMasterDTO.getDeptParentId());
        orderMasterDO.setDeptParentName(orderMasterDTO.getDeptParentName());
        return orderMasterDO;
    }
    
    /**
     * 订单转换对接管理平台DTO
     * @param request
     * @return
     */
    public static ThirdPartOrderMasterDTO doToThirdPartOrderDTO(OrderMasterDO request) {
        ThirdPartOrderMasterDTO item = new ThirdPartOrderMasterDTO();
        item.setId(request.getId());
        item.setBuyAppId(request.getFtbuyappid());
        item.setOrderNo(request.getForderno());
        item.setOrderDate(request.getForderdate());
        item.setBuyerId(request.getFbuyerid());
        item.setBuyerName(request.getFbuyername());
        item.setBuyerMail(request.getFbuyeremail());
        item.setBuyerContactMan(request.getFbuyercontactman());
        item.setBuyerTelephone(request.getFbuyertelephone());
        item.setDepartmentId(request.getFbuydepartmentid());
        item.setDepartmentName(request.getFbuydepartment());
        item.setSupplierId(request.getFsuppid());
        item.setSupplierCode(request.getFsuppcode());
        item.setSupplierName(request.getFsuppname());
        item.setDeliveryPlace(request.getFbiderdeliveryplace());
        item.setOrderTotalPrice(request.getForderamounttotal());
        item.setFundStatus(request.getFundStatus());
        item.setFailedReason(request.getFailedReason());
        item.setCurStatus(request.getStatus());
        item.setUpdatedStatus(request.getStatus());
        item.setConfirmDate(request.getFconfirmdate());
        item.setConfirmManId(request.getFconfirmmanid());
        item.setConfirmMan(request.getFconfirmman());
        item.setCancelDate(request.getFcanceldate());
        item.setCancelManId(request.getFcancelmanid());
        item.setCancelMan(request.getFcancelman());
        item.setDeliverDate(request.getFdeliverydate());
        item.setDeliveryManId(request.getFdeliverymanid());
        item.setDeliveryMan(request.getFdeliveryman());
        item.setAcceptDate(request.getFlastreceivedate());
        item.setAcceptManId(request.getFlastreceivemanid());
        item.setAcceptMan(request.getFlastreceiveman());
        item.setOrgId(request.getFuserid());
        item.setOrgCode(request.getFusercode());
        item.setOrgName(request.getFusername());
        item.setStatementId(request.getStatementId());
        item.setCancelReason(request.getFrefuseCancelReason());
        item.setRefuseCancelReason(request.getFrefuseCancelReason());
        item.setShutDownDate(request.getShutDownDate());
        item.setDeliveryInfo(request.getDeliveryInfo());
        item.setDeliveryNo(request.getDeliveryNo());
        item.setReturnTotalPrice(request.getReturnAmount());
        item.setRefuseCancelDate(request.getFrefuseCancelDate());
        item.setBidId(request.getBidOrderId());
        item.setOrderType(request.getOrderType());
        item.setAcceptPicUrls(request.getReceivePicUrls());
        item.setOriginalAmount(request.getOriginalAmount());
        item.setInventoryStatus(request.getInventoryStatus());
        item.setSpecies(request.getSpecies());
        item.setInStateTime(request.getInStateTime());
        item.setCarryFee(request.getCarryFee());
        item.setInvoiceTitleId(request.getInvoiceTitleId());
        item.setInvoiceTitle(request.getInvoiceTitle());
        item.setInvoiceTitleNumber(request.getInvoiceTitleNumber());
        item.setFundType(request.getFundType());
        item.setFundTypeName(request.getFundTypeName());

        return item;
    }

    /**
     * 将 OrderMasterSearchDTO 转换为 OrderMasterDO
     *
     * @param searchDTO 订单搜索DTO
     * @return 订单主表DO
     */
    public static OrderMasterDO toOrderMasterDO(OrderMasterSearchDTO searchDTO) {
        if (Objects.isNull(searchDTO)) {
            return null;
        }
        
        OrderMasterDO masterDO = new OrderMasterDO();
        masterDO.setId(searchDTO.getId());
        masterDO.setForderno(searchDTO.getForderno());
        masterDO.setFbuydepartmentid(searchDTO.getFbuydepartmentid());
        masterDO.setFbuydepartment(searchDTO.getFbuydepartment());
        masterDO.setFsuppname(searchDTO.getFsuppname());
        masterDO.setFsuppid(searchDTO.getFsuppid());
        masterDO.setForderamounttotal(Objects.isNull(searchDTO.getForderamounttotal()) ? null : BigDecimal.valueOf(searchDTO.getForderamounttotal()));
        masterDO.setOrderType(searchDTO.getOrderType());
        masterDO.setFuserid(searchDTO.getFuserid());
        masterDO.setSpecies(Objects.isNull(searchDTO.getSpecies()) ? null : searchDTO.getSpecies().byteValue());
        masterDO.setFtbuyappid(searchDTO.getFtbuyappid());
        masterDO.setProjectnumber(searchDTO.getProjectNumber());
        masterDO.setFbuyercontactman(searchDTO.getFbuyercontactman());
        masterDO.setFusername(searchDTO.getFusername());
        masterDO.setFbiderdeliveryplace(searchDTO.getFbiderdeliveryplace());
        masterDO.setDeptParentId(searchDTO.getDepartmentParentId());
        masterDO.setDeptParentName(searchDTO.getDepartmentParentName());
        masterDO.setFbuyername(searchDTO.getFbuyername());
        masterDO.setStatus(searchDTO.getStatus());
        masterDO.setStatementId(searchDTO.getStatementId());
        masterDO.setStatementStatus(searchDTO.getStatementStatus());
        masterDO.setFbuyerid(searchDTO.getFbuyerid());
        masterDO.setFconfirmdate(searchDTO.getFconfirmdate());
        masterDO.setFdeliverydate(searchDTO.getFdeliverydate());
        masterDO.setUpdateTime(searchDTO.getUpdateTime());
        masterDO.setFbuyertelephone(searchDTO.getFbuyertelephone());
        masterDO.setFlastreceivedate(searchDTO.getFlastreceivedate());
        masterDO.setFlastreceiveman(searchDTO.getFlastreceiveman());
        masterDO.setBidOrderId(searchDTO.getBidOrderId());
        masterDO.setFcanceldate(searchDTO.getFcanceldate());
        masterDO.setFundStatus(searchDTO.getFundStatus());
        masterDO.setInventoryStatus(Objects.isNull(searchDTO.getInventoryStatus()) ? null : searchDTO.getInventoryStatus().byteValue());
        masterDO.setCarryFee(Objects.isNull(searchDTO.getCarryFee()) ? null : BigDecimal.valueOf(searchDTO.getCarryFee()));
        masterDO.setReturnAmount(searchDTO.getReturnAmount());
        
        return masterDO;
    }

    /**
     * 批量将 OrderMasterSearchDTO 转换为 OrderMasterDO
     *
     * @param searchDTOList 订单搜索DTO列表
     * @return 订单主表DO列表
     */
    public static List<OrderMasterDO> toOrderMasterDOList(List<OrderMasterSearchDTO> searchDTOList) {
        if (CollectionUtils.isEmpty(searchDTOList)) {
            return New.emptyList();
        }
        
        List<OrderMasterDO> masterDOList = New.listWithCapacity(searchDTOList.size());
        for (OrderMasterSearchDTO searchDTO : searchDTOList) {
            OrderMasterDO masterDO = toOrderMasterDO(searchDTO);
            if (Objects.nonNull(masterDO)) {
                masterDOList.add(masterDO);
            }
        }
        
        return masterDOList;
    }
}
