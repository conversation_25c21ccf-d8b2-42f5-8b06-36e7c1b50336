package com.ruijing.store.order.business.service;

import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.base.baseconfig.dto.OrganizationConfigDTO;

import java.util.List;

/**
 * @description 订单定时任务相关业务
 * <AUTHOR>
 */
public interface OrderMasterForScheduledService {
    /**
     * 通过用户机构配置和状态查询超时结算的订单
     * @param configs 用户结算配置
     * @param status  订单状态
     * @return
     */
    List<OrderTimeOutDTO> findTimeOutBalance(List<OrganizationConfigDTO> configs, List<Integer> status);

    /**
     * 通过用户机构配置和状态查询超时验收的订单
     * @param configs
     * @param status
     * @return
     */
    List<OrderTimeOutDTO> findTimeOutExamine(List<OrganizationConfigDTO> configs, List<Integer> status);
}
