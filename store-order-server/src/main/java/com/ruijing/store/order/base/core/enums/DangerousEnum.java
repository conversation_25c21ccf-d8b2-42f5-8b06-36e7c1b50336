package com.ruijing.store.order.base.core.enums;

/**
 * @description: 危化品类型枚举
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/12 19:26
 **/
public enum DangerousEnum {
    UN_DANGEROUS(1, "其他"),
    NORMAL_DANGEROUS(2, "其他危化品"),
    EASY_MAKE_DANGEROUS(3, "易制毒"),
    EASY_MAKE_BOMB(4, "易制爆"),
    HIGHLY_TOXIC(5, "剧毒品"),
    PURE_HEMP_DRUG(6, "精神/麻醉品"),
    CIVIL_EXPLOSIVES(7, "民用爆炸物品"),
    MEDICAL_TOXIC_PRODUCTS(8, "医用毒性品"),
    CONVENTIONAL_CHEMICALS(9, "常规化学品"),
    RADIOISOTOPE(10, "放射性同位素"),
    BACTERIAL_STRAIN(11, "菌毒株"),
    VIRUS(12, "病毒"),
    ANIMAL(13, "动物种类");

    private final Integer code;
    private final String name;

    DangerousEnum(Integer value, String name) {
        this.code = value;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(int code) {
        for (DangerousEnum e : DangerousEnum.values()) {
            if (e.getCode() == code) {
                return e.getName();
            }
        }
        // 默认返回其他类型危化品
        return UN_DANGEROUS.getName();
    }

}
