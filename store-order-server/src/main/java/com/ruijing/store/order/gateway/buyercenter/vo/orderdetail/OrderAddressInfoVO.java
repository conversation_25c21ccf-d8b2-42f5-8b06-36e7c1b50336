package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/12/14 10:12
 */
@RpcModel
public class OrderAddressInfoVO implements Serializable {

    private static final long serialVersionUID = -1117649852829610175L;

    @RpcModelProperty("是否修改过地址,true是，false否")
    private Boolean hasModifiedAddress;

    @RpcModelProperty("是否使用代配送，true是，false否")
    private Boolean useProxyDeliver;

    public Boolean getHasModifiedAddress() {
        return hasModifiedAddress;
    }

    public OrderAddressInfoVO setHasModifiedAddress(Boolean hasModifiedAddress) {
        this.hasModifiedAddress = hasModifiedAddress;
        return this;
    }

    public Boolean getUseProxyDeliver() {
        return useProxyDeliver;
    }

    public OrderAddressInfoVO setUseProxyDeliver(Boolean useProxyDeliver) {
        this.useProxyDeliver = useProxyDeliver;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderAddressInfoVO{");
        sb.append("hasModifiedAddress=").append(hasModifiedAddress);
        sb.append(", useProxyDeliver=").append(useProxyDeliver);
        sb.append('}');
        return sb.toString();
    }
}
