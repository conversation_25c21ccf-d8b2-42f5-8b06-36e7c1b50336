package com.ruijing.store.order.base.orderconfirm.dto.request;

import com.ruijing.store.order.base.orderconfirm.dto.OrderConfirmRecordDTO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date Created in 10:18 AM 2018/11/6.
 * @Description
 * @Modified
 */
public class OrderConfirmForTheRecordRequest implements Serializable {

    private List<OrderConfirmRecordDTO> dtos;

    public List<OrderConfirmRecordDTO> getDtos() {
        return dtos;
    }

    public void setDtos(List<OrderConfirmRecordDTO> dtos) {
        this.dtos = dtos;
    }

}
