package com.ruijing.store.order.sysu;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnLogDTO;
import com.ruijing.store.order.api.sysu.service.SYSUGoodsReturnRPCService;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnLogTranslator;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.util.DictionaryUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/4/21 14:22
 **/
@MSharpService
public class SYSUGoodsReturnRPCServiceImpl implements SYSUGoodsReturnRPCService {

    @Resource
    private CommonGoodsReturnService commonGoodsReturnService;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @Override
    @ServiceLog(description = "根据订单号生成退货单号RPC接口", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<GoodsReturnDTO> generateReturnNo(OrderBasicParamDTO request) {
        String orderNo = request.getOrderNo();
        Preconditions.isTrue(StringUtils.isNotBlank(orderNo), "生成退货单号失败，订单号为空");
        String returnNo = commonGoodsReturnService.createReturnNo(orderNo);
        GoodsReturnDTO result = new GoodsReturnDTO();
        result.setReturnNo(returnNo);
        return RemoteResponse.<GoodsReturnDTO>custom().setSuccess().setData(result);
    }

    @ServiceLog(description = "根据订单id查询退货日志", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<List<GoodsReturnLogDTO>> findByOrderId(OrderBasicParamDTO request) {
        List<Integer> orderIdList = request.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "orderIdList must not be null");
        List<GoodsReturn> returnIdList = goodsReturnMapper.findIdByOrderIdIn(orderIdList);
        if (CollectionUtils.isEmpty(returnIdList)) {
            return RemoteResponse.<List<GoodsReturnLogDTO>>custom().setSuccess().setData(New.list());
        }

        Map<Integer, Integer> returnIdOrderIdMap = DictionaryUtils.toMap(returnIdList, GoodsReturn::getId, GoodsReturn::getOrderId);
        List<GoodsReturnLogDO> logs = goodsReturnLogDOMapper.findByReturnIdIn(returnIdList.stream().map(GoodsReturn::getId).collect(Collectors.toList()));
        List<GoodsReturnLogDTO> collect = logs.stream().map(it -> {
            GoodsReturnLogDTO logDTO = GoodsReturnLogTranslator.doToDTO(it);
            logDTO.setOrderId(returnIdOrderIdMap.get(it.getReturnId()));
            return logDTO;
        }).collect(Collectors.toList());

        return RemoteResponse.<List<GoodsReturnLogDTO>>custom().setSuccess().setData(collect);
    }
}
