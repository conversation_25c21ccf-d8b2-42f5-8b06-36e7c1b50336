package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description:
 * @DateTime: 2021/9/14 15:27
 */
@RpcModel("订单相关提示-请求体")
public class OrderTipsRequest implements Serializable {

    private static final long serialVersionUID = -4616579138070402232L;

    /**
     * 配置代码
     */
    @RpcModelProperty("配置代码")
    private String code;

    /**
     * 提示类型字串
     */
    @RpcModelProperty("提示类型字串")
    private String tipsType;

    /**
     * 提示来源枚举值，10-采购人；20-供应商
     */
    @RpcModelProperty("提示来源枚举值，10-采购人；20-供应商")
    private Integer type;

    /**
     * 版本号（1.0.0）
     */
    @RpcModelProperty("版本号（1.0.0）")
    private String version;

    /**
     * 用户id，可不传（传了用这个不传用登录的）
     */
    @RpcModelProperty("用户id，可不传（传了用这个不传用登录的）")
    private Integer userId;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTipsType() {
        return tipsType;
    }

    public void setTipsType(String tipsType) {
        this.tipsType = tipsType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderTipsRequest{");
        sb.append("code='").append(code).append('\'');
        sb.append(", tipsType='").append(tipsType).append('\'');
        sb.append(", type=").append(type);
        sb.append(", version='").append(version).append('\'');
        sb.append(", userId=").append(userId);
        sb.append('}');
        return sb.toString();
    }
}
