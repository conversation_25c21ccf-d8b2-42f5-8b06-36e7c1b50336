package com.ruijing.store.order.gateway.print.dto.order;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-04-04 14:51
 * @description:
 **/
public class OrderPrintApprovalLogDTO  implements Serializable {

    private static final long serialVersionUID = 89055973526937935L;

    /**
     * 审批时间
     */
    @RpcModelProperty("审批时间")
    private Long date;

    @RpcModelProperty("审批人用户id")
    private Integer approverUserId;

    @RpcModelProperty("审批人拥有的角色名")
    private String approverUserRole;

    /**
     * 审批人名称
     */
    @RpcModelProperty("审批人名称")
    private String approver;

    /**
     * 操作类型
     */
    @RpcModelProperty("操作类型")
    private String operate;

    /**
     * 备注
     */
    @RpcModelProperty("备注")
    private String operateComment;

    /**
     * 审批等级 默认0
     */
    @RpcModelProperty("审批等级 默认0")
    private Integer approveLevel;

    /**
     * 审批结果
     */
    @RpcModelProperty("审批结果")
    private String result;

    /**
     * 审批电子签名图片url
     */
    @RpcModelProperty("审批电子签名图片url")
    private String approvePhotoUrl;

    public Long getDate() {
        return date;
    }

    public void setDate(Long date) {
        this.date = date;
    }

    public Integer getApproverUserId() {
        return approverUserId;
    }

    public OrderPrintApprovalLogDTO setApproverUserId(Integer approverUserId) {
        this.approverUserId = approverUserId;
        return this;
    }

    public String getApproverUserRole() {
        return approverUserRole;
    }

    public OrderPrintApprovalLogDTO setApproverUserRole(String approverUserRole) {
        this.approverUserRole = approverUserRole;
        return this;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public String getOperateComment() {
        return operateComment;
    }

    public void setOperateComment(String operateComment) {
        this.operateComment = operateComment;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getApprovePhotoUrl() {
        return approvePhotoUrl;
    }

    public void setApprovePhotoUrl(String approvePhotoUrl) {
        this.approvePhotoUrl = approvePhotoUrl;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderPrintApprovalLogDTO.class.getSimpleName() + "[", "]")
                .add("date=" + date)
                .add("approverUserRole='" + approverUserRole + "'")
                .add("approver='" + approver + "'")
                .add("operate='" + operate + "'")
                .add("operateComment='" + operateComment + "'")
                .add("approveLevel=" + approveLevel)
                .add("result='" + result + "'")
                .add("approvePhotoUrl='" + approvePhotoUrl + "'")
                .toString();
    }
}
