package com.ruijing.store.order.gateway.buyercenter.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: zhu<PERSON>
 * @date : 2020/12/28 上午11:09
 * @description: 统计订单交易量和交易额
 */
@RpcModel("统计订单交易量和交易额")
public class OrderTransactionCountVO implements Serializable {

    private static final long serialVersionUID = -8787492840283539170L;

    @RpcModelProperty("交易日期")
    @ExcelProperty("交易日期")
    @ColumnWidth(value = 30)
    private String transactionDate;

    @RpcModelProperty("订单量(单)")
    @ExcelProperty("订单量(单)")
    @ColumnWidth(value = 30)
    private Integer count;

    @RpcModelProperty("订单金额（元）")
    @ExcelProperty("订单金额（元）")
    @ColumnWidth(value = 30)
    private BigDecimal amount;

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return "OrderTransactionCountVO{" +
                "transactionDate='" + transactionDate + '\'' +
                ", count=" + count +
                ", amount=" + amount +
                '}';
    }
}
