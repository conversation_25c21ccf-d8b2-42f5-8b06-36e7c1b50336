package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/9/14 15:19
 */
@RpcModel("订单相关提示-返回体")
public class OrderTipsVO implements Serializable {

    private static final long serialVersionUID = -5309816390756240382L;

    /**
     * 配置项代码
     */
    @RpcModelProperty("配置项代码")
    private String code;

    /**
     * 提示内容
     */
    @RpcModelProperty("提示内容")
    private String content;

    /**
     * 跳转到的链接
     */
    @RpcModelProperty("跳转到的链接")
    private String url;

    /**
     * 持续时间
     */
    @RpcModelProperty("持续时间")
    private Integer duration;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderTipsVO{");
        sb.append("code='").append(code).append('\'');
        sb.append(", content='").append(content).append('\'');
        sb.append(", url='").append(url).append('\'');
        sb.append(", duration=").append(duration);
        sb.append('}');
        return sb.toString();
    }
}
