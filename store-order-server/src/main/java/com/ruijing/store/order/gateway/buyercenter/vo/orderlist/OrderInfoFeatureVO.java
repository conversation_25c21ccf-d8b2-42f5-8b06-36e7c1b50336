package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.base.orderlog.enums.OrderLogUserTypeEnum;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderAttachmentVO;
import com.ruijing.store.order.gateway.file.vo.UploadFileInfoVO;

import java.io.Serializable;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-08-07 17:22
 * @description: 订单详情界面信息
 */
public class OrderInfoFeatureVO implements Serializable {

    private static final long serialVersionUID = 6103141270094937870L;

    /**
     * 实验室名称，深圳湾目前在用
     */
    @RpcModelProperty("实验室名称")
    private String labName;

    @RpcModelProperty(value = "结算单ID", description = "跳转页面时若有汇总单id,优先使用汇总单跳转汇总单详情")
    private Integer settlementId;

    @RpcModelProperty(value = "汇总单ID", description = "跳转页面时若有汇总单id,优先使用汇总单跳转汇总单详情")
    private Long summaryId;

    @RpcModelProperty(value = "订单关闭/取消原因", description = "订单详情用")
    private String closeReason;

    @RpcModelProperty("同意取消订单时间")
    private String agreeCancelTime;

    @RpcModelProperty(value = "申请取消订单的用户类型",enumClass = OrderLogUserTypeEnum.class)
    private Integer cancelUserType;

    @RpcModelProperty(value = "实验数据网盘链接", description = "江西中医附院定制")
    private String experimentDataUrl;

    @RpcModelProperty(value = "关联关系说明附件",description = "中大/中肿有")
    private List<OrderAttachmentVO> relatedAttachments;
    @RpcModelProperty("验收审批驳回原因")
    private String receiveRejectReason;

    /**
     * 运营商核实流程附件--补充送货单
     */
    @RpcModelProperty("补充送货单")
    private List<UploadFileInfoVO> supplementaryDeliveryNotes;

    /**
     * 运营商核实流程附件--核实证明材料
     */
    @RpcModelProperty("核实证明材料")
    private List<UploadFileInfoVO> verifyMaterials;

    public String getAgreeCancelTime() {
        return agreeCancelTime;
    }

    public OrderInfoFeatureVO setAgreeCancelTime(String agreeCancelTime) {
        this.agreeCancelTime = agreeCancelTime;
        return this;
    }

    public Integer getCancelUserType() {
        return cancelUserType;
    }

    public OrderInfoFeatureVO setCancelUserType(Integer cancelUserType) {
        this.cancelUserType = cancelUserType;
        return this;
    }

    public String getExperimentDataUrl() {
        return experimentDataUrl;
    }

    public OrderInfoFeatureVO setExperimentDataUrl(String experimentDataUrl) {
        this.experimentDataUrl = experimentDataUrl;
        return this;
    }

    public String getLabName() {
        return labName;
    }

    public OrderInfoFeatureVO setLabName(String labName) {
        this.labName = labName;
        return this;
    }

    public List<UploadFileInfoVO> getSupplementaryDeliveryNotes() {
        return supplementaryDeliveryNotes;
    }

    public OrderInfoFeatureVO setSupplementaryDeliveryNotes(List<UploadFileInfoVO> supplementaryDeliveryNotes) {
        this.supplementaryDeliveryNotes = supplementaryDeliveryNotes;
        return this;
    }

    public List<UploadFileInfoVO> getVerifyMaterials() {
        return verifyMaterials;
    }

    public OrderInfoFeatureVO setVerifyMaterials(List<UploadFileInfoVO> verifyMaterials) {
        this.verifyMaterials = verifyMaterials;
        return this;
    }

    public Integer getSettlementId() {
        return settlementId;
    }

    public void setSettlementId(Integer settlementId) {
        this.settlementId = settlementId;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public List<OrderAttachmentVO> getRelatedAttachments() {
        return relatedAttachments;
    }

    public OrderInfoFeatureVO setRelatedAttachments(List<OrderAttachmentVO> relatedAttachments) {
        this.relatedAttachments = relatedAttachments;
        return this;
    }


    public String getReceiveRejectReason() {
        return receiveRejectReason;
    }

    public OrderInfoFeatureVO setReceiveRejectReason(String receiveRejectReason) {
        this.receiveRejectReason = receiveRejectReason;
        return this;
    }

    @Override
    public String toString() {
        return "OrderInfoFeatureVO{" +
                "labName='" + labName + '\'' +
                ", settlementId=" + settlementId +
                ", summaryId=" + summaryId +
                ", closeReason='" + closeReason + '\'' +
                ", agreeCancelTime='" + agreeCancelTime + '\'' +
                ", cancelUserType=" + cancelUserType +
                ", experimentDataUrl='" + experimentDataUrl + '\'' +
                ", relatedAttachments=" + relatedAttachments +
                ", receiveRejectReason='" + receiveRejectReason + '\'' +
                ", supplementaryDeliveryNotes=" + supplementaryDeliveryNotes +
                ", verifyMaterials=" + verifyMaterials +
                '}';
    }
}
