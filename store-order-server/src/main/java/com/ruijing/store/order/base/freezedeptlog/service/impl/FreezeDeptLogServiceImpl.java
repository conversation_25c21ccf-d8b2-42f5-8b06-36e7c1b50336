package com.ruijing.store.order.base.freezedeptlog.service.impl;

import com.ruijing.fundamental.cat.Cat;
import com.ruijing.store.order.base.freezedeptlog.dto.FreezeDeptLogDTO;
import com.ruijing.store.order.base.freezedeptlog.enums.IsDeletedEnum;
import com.ruijing.store.order.base.freezedeptlog.mapper.FreezeDeptLogDOMapper;
import com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO;
import com.ruijing.store.order.base.freezedeptlog.service.FreezeDeptLogService;
import com.ruijing.store.order.base.freezedeptlog.translator.FreezeDeptLogTranslator;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.util.ListUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/** 冻结订单课题组相关业务
 * <AUTHOR>
 */
@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class FreezeDeptLogServiceImpl implements FreezeDeptLogService {

    private static final String CAT_TYPE = "FreezeDeptLogServiceImpl";

    @Resource
    private FreezeDeptLogDOMapper freezeDeptLogDOMapper;

    @Override
    public List<FreezeDeptLogDTO> findAllByIsDelete(int isDeleted) {
        List<FreezeDeptLogDO> listDO = freezeDeptLogDOMapper.findAllByIsDeleted(isDeleted);
        List<FreezeDeptLogDTO> listDTO = new ArrayList<>(listDO.size());

        for (FreezeDeptLogDO freezeDeptLogDO : listDO) {
            listDTO.add(FreezeDeptLogTranslator.do2DTO(freezeDeptLogDO));
        }
        return listDTO;
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public int insertList(List<FreezeDeptLogDTO> list) {
        final String methodName = "insertList";
        if (CollectionUtils.isEmpty(list)) {
            Cat.logWarn(CAT_TYPE, methodName, "插入失败, 入参为空!");
            return 0;
        }

        List<FreezeDeptLogDO> listDO = new ArrayList<>(list.size());
        for (FreezeDeptLogDTO dto : list) {
            listDO.add(FreezeDeptLogTranslator.dto2DO(dto));
        }
        return freezeDeptLogDOMapper.insertList(listDO);
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public int updateDeleted(List<Long> ids) {
        final String methodName = "updateDeleted";
        if (CollectionUtils.isEmpty(ids)) {
            Cat.logWarn(CAT_TYPE, methodName, "冻结课题组失败, 没有课题组要冻结!");
            return 0;
        }
        // 批量逻辑删除使用的in操作, 所以要分批处理, 一次最多更新500条
        List<List<Long>> splitIds = ListUtils.splitCollection(ListUtils.getBatchSQLCapacity(), ids);
        for (List<Long> it : splitIds) {
            freezeDeptLogDOMapper.updateIsDeletedByIdIn(IsDeletedEnum.ALREADY_DELETED.getCode(), it);
        }
        return ids.size();
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public int updateDeletedByOrgIdAndDepartmentIdAndType(int orgId, int departmentId) {
        return freezeDeptLogDOMapper.updateDeletedByOrgIdAndDepId(orgId, departmentId);
    }

    @Override
    public List<FreezeDeptLogDTO> findByBetweenCreateDate(Date minDate, Date maxDate, int isDeleted) {
        List<FreezeDeptLogDO> freezeDepartmentList = freezeDeptLogDOMapper.findAllByCreationTimeBetweenAndHasDeleted(minDate, maxDate, isDeleted);
        if (CollectionUtils.isEmpty(freezeDepartmentList)) {
            return Collections.emptyList();
        }

        return freezeDepartmentList.stream().map(f -> FreezeDeptLogTranslator.do2DTO(f)).collect(Collectors.toList());
    }
}
