package com.ruijing.store.order.base.orderconfirm.dto.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 查询订单确认备案信息
 * @date 2023/11/8 23
 */
public class OrderConfirmRecordInfoRequest implements Serializable {

    @RpcModelProperty("订单id")
    private Integer orderId;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
}
