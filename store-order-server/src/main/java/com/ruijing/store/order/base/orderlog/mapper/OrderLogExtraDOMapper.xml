<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.orderlog.mapper.OrderLogExtraDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.orderlog.model.OrderLogExtraDO">
    <!--@mbg.generated-->
    <!--@Table t_order_log_extra-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="log_id" jdbcType="BIGINT" property="logId" />
    <result column="field" jdbcType="VARCHAR" property="field" />
    <result column="old_value" jdbcType="VARCHAR" property="oldValue" />
    <result column="new_value" jdbcType="VARCHAR" property="newValue" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, log_id, field, old_value, new_value
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_order_log_extra
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_order_log_extra
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogExtraDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_log_extra (log_id, field, old_value, 
      new_value)
    values (#{logId,jdbcType=BIGINT}, #{field,jdbcType=VARCHAR}, #{oldValue,jdbcType=VARCHAR}, 
      #{newValue,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogExtraDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_log_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logId != null">
        log_id,
      </if>
      <if test="field != null">
        field,
      </if>
      <if test="oldValue != null">
        old_value,
      </if>
      <if test="newValue != null">
        new_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logId != null">
        #{logId,jdbcType=BIGINT},
      </if>
      <if test="field != null">
        #{field,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null">
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        #{newValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogExtraDO">
    <!--@mbg.generated-->
    update t_order_log_extra
    <set>
      <if test="logId != null">
        log_id = #{logId,jdbcType=BIGINT},
      </if>
      <if test="field != null">
        field = #{field,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null">
        old_value = #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        new_value = #{newValue,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogExtraDO">
    <!--@mbg.generated-->
    update t_order_log_extra
    set log_id = #{logId,jdbcType=BIGINT},
      field = #{field,jdbcType=VARCHAR},
      old_value = #{oldValue,jdbcType=VARCHAR},
      new_value = #{newValue,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>