package com.ruijing.store.order.base.minor.service.impl;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.other.dto.OrderRemarkDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.minor.mapper.OrderRemarkMapper;
import com.ruijing.store.order.base.minor.model.OrderRemark;
import com.ruijing.store.order.base.minor.service.OrderRemarkService;
import com.ruijing.store.order.base.minor.translator.OrderRelateTranslator;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2019/10/18 10:19
 **/
@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class OrderRemarkServiceImpl implements OrderRemarkService {

    @Resource
    private OrderRemarkMapper orderRemarkMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Override
    public List<OrderRemarkDTO> findOrderRemarkByPrimaryKeys(List<OrderRemarkDTO> orderRemarkDTOS) {
        // dto转换为查询入参
        List<OrderRemark> list = OrderRelateTranslator.dto2OrderRemark(orderRemarkDTOS);
        List<OrderRemark> orderRemarkList = orderRemarkMapper.findAllByPrimaryKey(list);
        // 结果转换为dto
        List<OrderRemarkDTO> result = OrderRelateTranslator.orderRemark2DTO(orderRemarkList);
        return result;
    }

    @Override
    public int saveOrUpdateOrderRemark(OrderRemarkDTO dto) {
        Integer buyAppId = dto.getFtbuyappid();
        Integer suppId = dto.getFsuppid();
        OrderRemark record = orderRemarkMapper.selectByPrimaryKey(buyAppId, suppId);
        int affect = 0;
        if (record != null) {
            record.setRemark(dto.getRemark());
            affect = orderRemarkMapper.updateByPrimaryKey(record);
        } else {
            OrderRemark param = OrderRelateTranslator.dto2OrderRemark(dto);
            affect = orderRemarkMapper.insert(param);
        }
        return affect;
    }

    @Override
    public void saveBuyerRemark(Integer orderId, String remark) {
        remark = remark == null ? StringUtils.EMPTY : remark;
        BusinessErrUtil.notNull(orderId, ExecptionMessageEnum.DOCUMENT_TO_SAVE_NOT_EXIST);
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.DOCUMENT_TO_SAVE_NOT_EXIST);
        BaseOrderExtraDTO baseOrderExtraDTO = new BaseOrderExtraDTO();
        baseOrderExtraDTO.setOrderId(orderId);
        baseOrderExtraDTO.setOrderNo(orderMasterDO.getForderno());
        baseOrderExtraDTO.setOrgId(orderMasterDO.getFuserid());
        baseOrderExtraDTO.setExtraKey(OrderExtraEnum.REMARK.getValue());
        baseOrderExtraDTO.setExtraKeyDesc(OrderExtraEnum.REMARK.getDesc());
        baseOrderExtraDTO.setExtraValue(remark);
        orderExtraClient.saveList(New.list(baseOrderExtraDTO));
    }
}
