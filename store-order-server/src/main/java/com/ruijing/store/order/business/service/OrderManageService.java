package com.ruijing.store.order.business.service;

import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.fundcard.dto.ChangeFundCardCallbackResult;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderApprovalParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.base.other.dto.OrderUnFreezeRequestDTO;
import com.ruijing.store.order.api.gateway.dto.DeliveryNoteDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderMaterialCodeRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.TransactionCountRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * hms订单管理服务
 */
public interface OrderManageService {

    /**
     * 获取订单状态数量 和待入库数量
     * @param userId
     * @param orgId
     * @return
     */
    OrderStatusCountVO getOrderCountByStatus(Integer userId, Integer orgId);

    /**
     *
     * @param userId  采购人id
     * @param orgId     机构id
     * @return workbenchOrderAmountVO.DayAmount  今日订单金额
     *          workbenchOrderAmountVO.mouthAmount  本月订单金额
     */
    WorkbenchOrderAmountVO getMyOrderAmount(Integer userId, Integer orgId);

    /**
     * 导出订单交易金额统计
     * @param departmentIds
     * @param userId
     * @param orgId
     * @param request
     * @return
     * @throws IOException
     */
    String exportOrderTransaction(List<Integer> departmentIds, Integer userId, Integer orgId,
                                  TransactionCountRequest request);

    /**
     *
     * @param departmentIds  部门id
     * @param userId  用户id
     * @param orgId  组织id
     * @param request 入参
     * @return
     */
    List<OrderTransactionCountVO> getOrderTransaction(List<Integer> departmentIds, Integer userId, Integer orgId,
                                                      TransactionCountRequest request);

    /**
     * 订单列表--评价--订单信息加载
     * @param orderNo
     * @param userId
     * @return
     */
    OrderForCommentVO getOrderForComment(String orderNo, Integer userId);

    DeliveryNoteDTO assembleDeliveryNote(Integer orderId);

    /**
     * 订单解冻经费卡
     * @param orderId
     */
    void orderFundCardUnFreezeById(Integer orderId);

    /**
     * 订单解冻经费标准接口
     * @param orderUnFreezeRequestDTO
     */
    void orderFundCardUnFreeze(OrderUnFreezeRequestDTO orderUnFreezeRequestDTO);

    /**
     * 发送验收通知给江西肿瘤
     * @param orderId
     */
    void sendReceiptMessageForJiangZhong(Integer orderId);

    /**
     * 提交订单验收审批
     * @param orderApprovalParamDTO
     */
    void submitOrderApproval(OrderApprovalParamDTO orderApprovalParamDTO);

    /**
     * 多级验收审批通过， 完成验收审批后的事件
     * @param orderApprovalParamDTO 验收审批信息
     */
    void acceptApproveFlowPass(OrderApprovalParamDTO orderApprovalParamDTO);

    /**
     * 换卡成功 回调 方法实现
     * @param callbackRequest
     */
    void changeFundCardCallBack(CallbackRequest<ChangeFundCardCallbackResult> callbackRequest);

    /**
     * 验收审批换卡
     * @param orderApprovalParamDTO 验收审批换卡入参
     * @param order                 订单信息
     * @param fundCardCacheList     换卡缓存
     */
    void changeFundCard(OrderApprovalParamDTO orderApprovalParamDTO, OrderMasterDO order, List<OrderFundCardDTO> fundCardCacheList);

    /**
     * 验收审批驳回
     * @param orderApprovalParamDTO
     */
    void acceptanceApprovalRejection(OrderApprovalParamDTO orderApprovalParamDTO);

    /**
     * 验收审批通过
     * @param orderApprovalParamDTO
     * @param orderMasterDO
     */
    void acceptanceApprovalSuccess(OrderApprovalParamDTO orderApprovalParamDTO, OrderMasterDO orderMasterDO);

    /**
     * 成功入库 更新订单操作
     * @param inventoryStatus 入库状态
     * @param orgCode    组织code
     * @param orderMasterDO 订单
     */
    void wareHouseSuccess(Byte inventoryStatus, String orgCode, OrderMasterDO orderMasterDO);

    /**
     * 根据用户信息查找用户超时 结算/验收 订单
     * @param timeOutOrderParamsDTO 入参
     * @return
     */
    BasePageResponseDTO<OrderMasterTimeOutDTO> findTimeOutOrders(TimeOutOrderParamsDTO timeOutOrderParamsDTO);

    /**
     * 查询超时的订单
     *
     * @param departmentId     课题组id
     * @param timeOutConfigMap 超时配置
     * @param seletType        超时条件 0 -> 超时结算, 1 -> 超时验收
     * @return
     */
    SearchPageResultDTO<OrderMasterSearchDTO> getTimeOutOrdersBySelectType(int departmentId, Map<String, Integer> timeOutConfigMap, int seletType);

    /**
     * 根据 orgCode 获取超时相关配置
     * @param orgCode 配置字典 (configCode -> configvalue)
     * @return
     */
    Map<String, Integer> getTimeOutConfigMap(String orgCode);

    /**
     * 根据订单查找存储仓库列表
     * @param request   订单基本信息
     * @return          存储仓库列表
     */
    List<OrderStoreHouseVO> findStorageByOrderInfo(OrderBasicParamDTO request);

    /**
     * 订单解冻经费卡
     * @param orderMasterDO
     */
    void orderFundCardUnFreeze(OrderMasterDO orderMasterDO);

    /**
     * 推送订单信息到第三方单号对接平台
     * @param orderMasterDO             订单信息
     * @param orderDetailDOList         订单明细
     */
    void pushOrderToThirdPlatform(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList);

    /**
     * 异步校验风险订单
     * @param orderIdList
     */
    void asyncVerifyOrderRisk(List<Integer> orderIdList);

    /**
     * 推送订单信息到第三方单号对接平台
     * @param orderMasterDO             订单信息
     * @param orderDetailDOList         订单明细
     */
    void pushOrderToThirdPlatformByThunder(List<OrderMasterDO> orderMasterDO, List<OrderDetailDO> orderDetailDOList);

    /**
     * 批量释放经费
     *    只保证调解冻接口成功，部分单位解冻结果依赖回调结果
     * @param loginInfo 用户信息
     * @param idList 订单id
     */
    void batchUnfreezeFund(LoginUserInfoBO loginInfo, List<Integer> idList);

    /**
     * 条件批量查询最新一个物资编码
     * @param requestDTO
     * @return
     */
    List<OrderMaterialCodeDTO> latestMaterialCode(OrderMaterialCodeRequestDTO requestDTO);

    /**
     * 更新订单物资编码
     * @param orderMaterialCodeDTO
     * @return
     */
    Integer updateOrderMaterialCode(OrderMaterialCodeDTO orderMaterialCodeDTO);

    /**
     * 新增订单物资编码
     * @param orderMaterialCodeDTOList
     * @return
     */
    Integer insertMaterialCode(List<OrderMaterialCodeDTO> orderMaterialCodeDTOList);


    /**
     * store迁移-根据订单id检测商品类型，oms配置除服务类强制拍照验收时，采购人确认验收时调用
     *
     * @param param
     */
    RemoteResponse<Boolean> isServiceType(OrderBasicParamDTO param);
}
