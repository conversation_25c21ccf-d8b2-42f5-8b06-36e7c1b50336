package com.ruijing.store.order.gateway.oms.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.buyercenter.request.ChangeFundCardRequestDTO;

import java.io.Serializable;
import java.util.List;

@RpcModel("冻结/解冻经费请求体")
public class FixFundDataRequest implements Serializable {

    private static final long serialVersionUID = -8493520089244483866L;

    @RpcModelProperty("订单id")
    private List<Integer> ids;

    @RpcModelProperty("是否调用外部接口")
    private Boolean callInterfaceFlag;

    @RpcModelProperty("修正原因")
    private String reason;

    @RpcModelProperty("钉钉审批编号")
    private String dingTalkApprovalNumber;

    @RpcModelProperty(value = "冻结经费入参")
    private ChangeFundCardRequestDTO changeFundCardRequestDTO;

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDingTalkApprovalNumber() {
        return dingTalkApprovalNumber;
    }

    public void setDingTalkApprovalNumber(String dingTalkApprovalNumber) {
        this.dingTalkApprovalNumber = dingTalkApprovalNumber;
    }

    public Boolean getCallInterfaceFlag() {
        return callInterfaceFlag;
    }

    public void setCallInterfaceFlag(Boolean callInterfaceFlag) {
        this.callInterfaceFlag = callInterfaceFlag;
    }

    public ChangeFundCardRequestDTO getChangeFundCardRequestDTO() {
        return changeFundCardRequestDTO;
    }

    public void setChangeFundCardRequestDTO(ChangeFundCardRequestDTO changeFundCardRequestDTO) {
        this.changeFundCardRequestDTO = changeFundCardRequestDTO;
    }

    @Override
    public String toString() {
        return "FixFundDataRequest{" +
                "ids=" + ids +
                ", callInterfaceFlag=" + callInterfaceFlag +
                ", reason='" + reason + '\'' +
                ", dingTalkApprovalNumber='" + dingTalkApprovalNumber + '\'' +
                ", changeFundCardRequestDTO=" + changeFundCardRequestDTO +
                '}';
    }
}
