package com.ruijing.store.order.base.util;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderDetailReturnStatus;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.util.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description: 订单工具类
 * @author: zhongyulei
 * @create: 2019/12/27 11:39
 **/
public class OrderCommonUtils {

    public static final List<Integer> NOT_WAREHOUSE_INVENTORY_STATUS_LIST = New.list(InventoryStatusEnum.WAITING_FOR_STORE.getCode(),
            InventoryStatusEnum.WAITING_FOR_REVIEW.getCode(), InventoryStatusEnum.FAILED.getCode());

    /**
     * 省份分词词汇
     */
    public static final String PROVINCE_ANALYSIS = "省";

    /**
     * 自治区分词词汇
     */
    public static final String AUTONOMY_ANALYSIS = "区";

    /**
     * 市区分词词汇
     */
    public static final String CITY_ANALYSIS = "市";

    /**
     * 县分词词汇
     */
    public static final String COUNTY_ANALYSIS = "县";

    /**
     * 订单验收日期默认值
     */
    private static final String INIT_STATE_DATE = "1970-10-10 10:00:00";

    /**
     * 超时统计日期计算规则至少要补全1天
     */
    private static final double COMPLETION_DAY = 1.0;


    /**
     * 获取数据库的默认初始化时间
     * @return
     */
    public static Date getInitStateDate() {
        return DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, INIT_STATE_DATE);
    }

    /**
     * 检测订单是否结算超时
     * @param order
     * @param timeOutConfigMap
     * @return
     */
    public static boolean isStatementTimeOut(OrderMasterDO order, Map<String, Integer> timeOutConfigMap) {
        return isStatementTimeOut(order, New.emptyList(), timeOutConfigMap);
    }

    /**
     * 检测订单是否结算超时
     * @param order
     * @param detailList
     * @return
     */
    public static boolean isStatementTimeOut(OrderMasterDO order, List<OrderDetailDO> detailList, Map<String, Integer> timeOutConfigMap) {
        // 日期上是否超时结算
        boolean timeOut = dateStatementTimeOut(order, timeOutConfigMap);
        // 如果日期超时，则判断下是否有退货
        if (CollectionUtils.isNotEmpty(detailList) && timeOut) {
            // 如果存在退货中的订单，则订单不超时
            timeOut = detailList.stream().noneMatch(OrderCommonUtils::havingReturn);
        }
        // 线下单不会是结算超时
        if(OrderSpeciesEnum.OFFLINE.getValue().equals(order.getSpecies().intValue())){
            return false;
        }
        return timeOut;
    }

    /**
     * 是否结算超时中的验收审批或入库超时
     * @param status 订单状态
     * @param inventoryStatus 库房状态
     * @param isUseWareHouseSystem 是否使用库房系统
     * @return 是否
     */
    public static boolean isNotAcceptApproveOrWareHouse(Integer status, Integer inventoryStatus, boolean isUseWareHouseSystem) {
        return isUseWareHouseSystem && (OrderStatusEnum.OrderReceiveApproval.value.equals(status) ||
                (OrderStatusEnum.WaitingForStatement_1.value.equals(status) && NOT_WAREHOUSE_INVENTORY_STATUS_LIST.contains(inventoryStatus)));
    }

    /**
     * 日期上的结算超时
     * @param order
     * @return
     */
    public static boolean dateStatementTimeOut(OrderMasterDO order, Map<String, Integer> timeOutConfigMap) {
        // 收货时间
        Date flastreceivedate = order.getFlastreceivedate();
        // 验收开始时间
        Date inStateTime = order.getInStateTime();

        Date nowDate = new Date();
        if (INIT_STATE_DATE.equals(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, inStateTime))) {
            inStateTime = nowDate;
        }

        if (flastreceivedate == null) {
            flastreceivedate = nowDate;
        }

        // 退货的订单是否结算超时
        if (OrderStatusEnum.WaitingForStatement_1.getValue().equals(order.getStatus()) ||
                OrderStatusEnum.OrderReceiveApproval.getValue().equals(order.getStatus())) {
            if ((TimeUtil.timeApartDay(flastreceivedate.getTime(), inStateTime.getTime()) + COMPLETION_DAY) >= timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检测订单是否超时验收
     * @param order
     * @param timeOutConfigMap
     * @return
     */
    public static boolean isReceiveTimeOut(OrderMasterDO order, Map<String, Integer> timeOutConfigMap) {
        return isReceiveTimeOut(order, New.emptyList(), timeOutConfigMap);
    }

    /**
     * 检测订单是否超时验收
     * @param order
     * @param detailList
     * @param timeOutConfigMap
     * @return
     */
    public static boolean isReceiveTimeOut(OrderMasterDO order, List<OrderDetailDO> detailList, Map<String, Integer> timeOutConfigMap) {
        // 日期上是否超时结算
        boolean timeOut = dateReceiveTimeOut(order, timeOutConfigMap);
        if (CollectionUtils.isNotEmpty(detailList)) {
            // 如果存在退货中的订单，则订单不超时
            timeOut = !(detailList.stream().anyMatch(d -> havingReturn(d)));
        }
        return timeOut;
    }

    public static boolean dateReceiveTimeOut(OrderMasterDO order, Map<String, Integer> timeOutConfigMap) {
        // 发货时间
        Date deliveryDate = order.getFdeliverydate();
        // 收货时间
        Date lastReceiveDate = order.getFlastreceivedate();
        // 当前时间
        Date nowDate = new Date();
        if (deliveryDate == null) {
            deliveryDate = nowDate;
        }

        if (lastReceiveDate == null) {
            lastReceiveDate = nowDate;
        }

        // 退货的订单是否验收超时
        if (OrderStatusEnum.WaitingForReceive.getValue().equals(order.getStatus())) {
            if ((TimeUtil.timeApartDay(deliveryDate.getTime(), lastReceiveDate.getTime()) + COMPLETION_DAY) >= timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否包含退货中的商品
     * @param detail
     * @return
     */
    public static boolean havingReturn(OrderDetailDO detail) {
        Integer returnStatus = detail.getReturnStatus();
        if (OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode().equals(returnStatus)
                || OrderDetailReturnStatus.AGREETORETURN.getCode().equals(returnStatus)
                || OrderDetailReturnStatus.REFUSEDTORETURN.getCode().equals(returnStatus)
                || OrderDetailReturnStatus.RETURNEDGOODS.getCode().equals(returnStatus)) {
            return true;
        }

        return false;
    }

    /**
     * 订单配送地址分词，广州省广州市会被分词成 "广州省"，"广州市"
     * @param address   地址文案
     * @param analyzer  分词器
     * @return          分词结果
     */
    public static String matchOrderDeliveryAddress(String address, String analyzer) {
        if (StringUtils.isBlank(address)) {
            return StringUtils.EMPTY;
        }
        Preconditions.notNull(analyzer, "analyzer must not null");
        int provinceIdx = address.indexOf(PROVINCE_ANALYSIS);
        int autonomyIdx = address.indexOf(AUTONOMY_ANALYSIS);
        int cityIdx = address.indexOf(CITY_ANALYSIS);
        int countIdx = address.indexOf(COUNTY_ANALYSIS);
        switch (analyzer) {
            case PROVINCE_ANALYSIS:
                if (provinceIdx < 0) {
                    break;
                }
                return address.substring(0, ++provinceIdx);
            case AUTONOMY_ANALYSIS:
                if (autonomyIdx < 0) {
                    break;
                }
                return address.substring(0, ++autonomyIdx);
            case CITY_ANALYSIS:
                if (provinceIdx >= 0 && cityIdx >= 0) {
                    return address.substring(++provinceIdx, ++cityIdx);
                }

                if (autonomyIdx >= 0 && cityIdx >= 0) {
                    return address.substring(++autonomyIdx, ++cityIdx);
                }
            case COUNTY_ANALYSIS:
                if (countIdx < 0 && autonomyIdx >= 0) {
                    return address.substring(++cityIdx, ++autonomyIdx);
                }

                if (cityIdx >= 0 && countIdx >= 0) {
                    return address.substring(++cityIdx, ++countIdx);
                }

                if (provinceIdx >= 0 && countIdx >= 0) {
                    return address.substring(++provinceIdx, ++countIdx);
                }

                if (autonomyIdx >= 0 && countIdx >= 0) {
                    return address.substring(++autonomyIdx, ++countIdx);
                }
            default:
                return StringUtils.EMPTY;
        }
        return StringUtils.EMPTY;
    }

    /**
     * getOrgCodeByOrgId, check exception handler
     * @return
     */
    public static String getOrgCodeByOrgId(Integer orgId) {
        try {
            return Optional.ofNullable(OrgEnum.getOrgEnumById(orgId)).map(OrgEnum::getCode).orElse(StringUtils.EMPTY);
        } catch (Exception e) {
            // ignore, test 环境有枚举里没维护的医院，需要
        }
        return StringUtils.EMPTY;
    }

    /**
     * 生成子订单单号
     * 算法：
     * 初始订单生成的子订单号为 ${orderNo} + 'A'...'B'...
     * 譬如 orderNo = DC202111232693201, sequence = 3, 那么生成的子订单号为 [DC202111232693201A, DC202111232693201B, DC202111232693201C]
     * 如果是在子订单上再拆分，依据当前已拆分的后缀字母最大位做起始编码, 并保留初始自订单, 则最终子订单号 ${orderNo} + charAt(latest character)...
     * 譬如 数据库一共存在[DC202111232693201A, DC202111232693201B, DC202111232693201C]这几个已拆单号, 需要将最大位编码做入参 orderNo = DC202111232693201C, sequence = 3,
     * 那么生成的子订单号为 [DC202111232693201C, DC202111232693201D, DC202111232693201E]
     * @param orderNo   母订单单号
     * @param sequence  拆分的数量，也是递增序数
     * @return          子订单数组
     */
    public static String[] generateChildOrder(String orderNo, int sequence) {
        String[] children = new String[sequence];
        if (orderNo == null || orderNo.trim().length() == 0) {
            return new String[]{};
        }
        int length = orderNo.length();
        String lastCharacter = orderNo.substring(length - 1, length);
        char c = lastCharacter.charAt(0);
        // 如果在 ASCII 码 A-Z区间，则返回下一个字母
        if (isUpperCaseWithSingle(lastCharacter)) {
            orderNo = orderNo.substring(0, orderNo.length() - 1);
            for (int i = 0; i < sequence; i++) {
                children[i] = orderNo + (char) (c + i + 1);
            }
        }

        // 如果在 ASCII 码 0-9区间，则从'A'开始
        if (c > 47 && c < 58) {
            for (int i = 0; i < sequence; i++) {
                children[i] = orderNo + (char) (65 + i);
            }
        }
        return children;
    }

    public static String[] generateChildOrderByMaximumCharacter(List<String> orderNoList, String parentOrderNo, int sequence) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return new String[]{};
        }
        String maximumOrderNo = orderNoList.stream().max(String::compareTo).get();
        String[] orderNoSequence = generateChildOrder(maximumOrderNo, sequence - 1);
        String[] children = new String[sequence];
        children[0] = parentOrderNo;
        System.arraycopy(orderNoSequence, 0, children, 1, orderNoSequence.length);
        return children;
    }

    /**
     * 是否大写字母, 只支持单个element
     * @param element
     * @return
     */
    public static boolean isUpperCaseWithSingle(String element) {
        if (element == null) return false;
        if (element.length() > 1) return false;
        return isUpperCase(element, 0);
    }

    /**
     * 是否大写字母
     * @param element
     * @return
     */
    public static boolean isUpperCase(String element, int index) {
        if (element == null) return false;
        if (index > element.length() - 1) return false;
        char c = element.charAt(index);
        return c > 64 && c < 91;
    }

    /**
     * 是否子单
     * @param orderNo
     * @return y/n
     */
    public static boolean isSplitOrder(String orderNo) {
        int length = orderNo.length();
        if (length < 1) return false;
        String lastCharacter = orderNo.substring(length - 1, length);
        return isUpperCaseWithSingle(lastCharacter);
    }
}
