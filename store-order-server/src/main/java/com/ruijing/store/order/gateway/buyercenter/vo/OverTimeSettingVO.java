package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: ch<PERSON><PERSON><PERSON><PERSON>g
 * @createTime: 2024-04-26 14:32
 * @description: 更改配置前的统计配置
 **/

@RpcModel(value = "管理中心-订单管理-超时订单-更改配置前的统计配置")
public class OverTimeSettingVO implements Serializable {

    private static final long serialVersionUID = -5644819768491850131L;

    @RpcModelProperty("组织ID")
    private Integer orgId;

    @RpcModelProperty("验收天数")
    private Integer acceptDay;

    @RpcModelProperty("结算天数")
    private Integer balanceDay;

    public OverTimeSettingVO() {
    }

    public OverTimeSettingVO(Integer orgId, Integer acceptDay, Integer balanceDay) {
        this.orgId = orgId;
        this.acceptDay = acceptDay;
        this.balanceDay = balanceDay;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OverTimeSettingVO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getAcceptDay() {
        return acceptDay;
    }

    public OverTimeSettingVO setAcceptDay(Integer acceptDay) {
        this.acceptDay = acceptDay;
        return this;
    }

    public Integer getBalanceDay() {
        return balanceDay;
    }

    public OverTimeSettingVO setBalanceDay(Integer balanceDay) {
        this.balanceDay = balanceDay;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OverTimeSettingVO.class.getSimpleName() + "[", "]")
                .add("orgId=" + orgId)
                .add("acceptDay=" + acceptDay)
                .add("balanceDay=" + balanceDay)
                .toString();
    }
}
