package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.apply.dto.application.ApplyRefBusinessPriceDTO;
import com.ruijing.store.apply.dto.application.ApplyRefBusinessPriceQueryDTO;
import com.ruijing.store.apply.service.application.ApplyRefBusinessPriceService;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.approval.api.enums.ApprovalDockingTypeEnum;
import com.ruijing.store.approval.api.enums.FlowRoleAccessEnum;
import com.ruijing.store.approval.api.service.ApprovalTaskService;
import com.ruijing.store.approval.api.service.ApproveFlowRefRoleAccessService;
import com.ruijing.store.approval.api.service.ApproveFlowService;
import com.ruijing.store.approval.api.service.PurchaseApprovalLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 采购申请相关client
 */
@ServiceClient
public class PurchaseApprovalLogClient {

    private static final String CAT_TYPE = "PurchaseApprovalLogClient";

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseApprovalLogClient.class);

    /**
     * 采购审批日志留下的坑，审批日志长度最长是495
     */
    private static final int MAX_COMMENT_LEN = 495;

    @MSharpReference(remoteAppkey = "store-approval-service")
    private PurchaseApprovalLogService purchaseApprovalLogService;

    @MSharpReference(remoteAppkey="store-apply-service")
    private ApplyRefBusinessPriceService applyRefBusinessPriceService;

    @MSharpReference(remoteAppkey = "store-approval-service", timeout = "15000")
    private ApprovalTaskService approvalTaskService;

    @MSharpReference(remoteAppkey = "store-approval-service")
    private ApproveFlowRefRoleAccessService approveFlowRefRoleAccessService;

    @MSharpReference(remoteAppkey = "store-approval-service")
    private ApproveFlowService approveFlowService;

    /**
     * 查询商品价格变动信息
     * @param applyRefBusinessPriceQueryDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<ApplyRefBusinessPriceDTO> listApplyRefBusinessPrice(ApplyRefBusinessPriceQueryDTO applyRefBusinessPriceQueryDTO) {
        Assert.notNull(applyRefBusinessPriceQueryDTO, "查询对象为空");
        RemoteResponse<List<ApplyRefBusinessPriceDTO>> listRemoteResponse = applyRefBusinessPriceService.listApplyRefBusinessPrice(applyRefBusinessPriceQueryDTO);
        Assert.isTrue(listRemoteResponse.isSuccess(), "查询商品价格变动信息失败");
        List<ApplyRefBusinessPriceDTO> applyRefBusinessPriceDTOS = listRemoteResponse.getData();
        return applyRefBusinessPriceDTOS;
    }

    /**
     * 根据采购单id获取采购单日志
     * @param appId
     * @return
     */
    @ServiceLog(description = "根据采购单id获取采购单日志", serviceType = ServiceType.COMMON_SERVICE)
    public List<PurchaseApprovalLogDTO> getApprovalLogById(Integer appId) {
        Preconditions.isTrue(appId != null, "查询失败，appId为空");
        Set<Integer> appIdSet = New.set(appId);

        RemoteResponse<List<PurchaseApprovalLogDTO>> response = purchaseApprovalLogService.listApprovalLog(appIdSet);
        Preconditions.isTrue(response.isSuccess(), "根据采购单id获取采购单日志异常：" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    /**
     * 根据采购申请Id和审批等级查询审批人id
     * @param appId
     * @param approveLevel
     * @return
     */
    public Integer getApprovalIdForLevel(Integer appId, Integer approveLevel) {
        List<PurchaseApprovalLogDTO> approvalLogList = this.getApprovalLogById(appId);
        if (CollectionUtils.isNotEmpty(approvalLogList)) {
            for (PurchaseApprovalLogDTO approvalLogDTO : approvalLogList) {
                if (approveLevel.equals(approvalLogDTO.getApproveLevel())) {
                    return approvalLogDTO.getApproverId();
                }
            }
        }

        return 0;
    }

    @ServiceLog(description = "根据采购单id批量查询采购审批日志", serviceType = ServiceType.COMMON_SERVICE)
    public List<PurchaseApprovalLogDTO> getApprovalLogByIdList(Set<Integer> applicationIdList) {
        final RemoteResponse<List<PurchaseApprovalLogDTO>> response = purchaseApprovalLogService.listApprovalLog(applicationIdList);
        Preconditions.isTrue(response.isSuccess(), "查询失败：" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "批量查询采购单审批日志", serviceType = ServiceType.COMMON_SERVICE)
    public List<PurchaseApprovalLogDTO> getApprovalLogByIdListBatch(Set<Integer> applicationIdList) {
        if (CollectionUtils.isEmpty(applicationIdList)) {
            return New.emptyList();
        }
        List<PurchaseApprovalLogDTO> result = New.list();
        List<List<Integer>> batchList = Lists.partition(New.list(applicationIdList), 200);

        for (List<Integer> batch : batchList) {
            List<PurchaseApprovalLogDTO> batchResult = getApprovalLogByIdList(New.set(batch));
            if (CollectionUtils.isNotEmpty(batchResult)) {
                result.addAll(batchResult);
            }
        }

        return result;
    }

    public Map<Integer, List<PurchaseApprovalLogDTO>> getApprovalLogMapByIdList(Set<Integer> applicationIdList) {
        final List<PurchaseApprovalLogDTO> approvalLogList = this.getApprovalLogByIdList(applicationIdList);
        final Map<Integer, List<PurchaseApprovalLogDTO>> result = approvalLogList.stream().collect(Collectors.groupingBy(PurchaseApprovalLogDTO::getApplicationId));

        return result;
    }

    @ServiceLog(description = "根据采购单id,审批结果描述，对接单类型批量查询采购审批日志", serviceType = ServiceType.COMMON_SERVICE)
    public List<PurchaseApprovalLogDTO> getApprovalLogByResult(Set<Integer> appIdSet, String resultDesc, ApprovalDockingTypeEnum dockType) {
        Preconditions.isTrue(appIdSet!=null && resultDesc!=null && dockType!=null,"查询审批日志入参不能为空");
        RemoteResponse<List<PurchaseApprovalLogDTO>> approvalLogList = purchaseApprovalLogService.listApprovalLogByResult(appIdSet,resultDesc,dockType);
        Preconditions.notNull(approvalLogList,"查询审批日志接口异常");
        return approvalLogList.getData();
    }

    /**
     * 保存采购单日志信息
     *
     * @param purchaseApprovalLog 日志DTO
     * @return 受影响行数
     */
    @ServiceLog(description = "保存采购单日志信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public int saveApprovalLog(PurchaseApprovalLogDTO purchaseApprovalLog) {
        RemoteResponse<Integer> response = purchaseApprovalLogService.saveApprovalLog(purchaseApprovalLog);
        Preconditions.isTrue(response.isSuccess(), "保存采购单日志信息异常:" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    public int addApprovalLog(Integer applicationId, String approve, Integer approveLevel, Integer approveId, String approveResult, String comment) {
        PurchaseApprovalLogDTO log = new PurchaseApprovalLogDTO();
        log.setDockId(applicationId);
        log.setApprover(approve);
        log.setApproveLevel(approveLevel);
        log.setApproverId(approveId);
        log.setResult(approveResult);
        log.setApproveTime(new Date());
        log.setComment(StringUtils.abbreviate(comment, MAX_COMMENT_LEN));

        return this.saveApprovalLog(log);
    }

    /**
     * 根据用户和权限枚举查询审批流id列表
     * @param userId
     * @param orgId
     * @param flowRoleAccessEnum
     * @return
     */
    public List<Integer> findFlowIdsForUser(Integer userId, Integer orgId, FlowRoleAccessEnum flowRoleAccessEnum) {
        RemoteResponse<List<Integer>> response = approveFlowService.findFlowIdsForUser(userId, orgId, flowRoleAccessEnum);
        String errorMsg = response == null ? "" : response.getMsg();
        Preconditions.isTrue(response != null && response.isSuccess(), "根据用户和权限枚举查询审批流id列表," + errorMsg);
        return response.getData();
    }
}
