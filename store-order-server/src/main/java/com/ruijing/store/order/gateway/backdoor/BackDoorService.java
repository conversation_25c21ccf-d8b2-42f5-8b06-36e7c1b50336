package com.ruijing.store.order.gateway.backdoor;

import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.gateway.backdoor.request.BackDoorRequest;
import com.ruijing.store.order.gateway.backdoor.request.SaveElectronicSignRequest;

import java.util.Date;
import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-03-07 16:01
 * @description: 后门接口服务
 **/
public interface BackDoorService {

    /**
     * 重新冻结
     */
    void refreezeOrder(List<String> orderNoList);

    void compensateWalletCharge(OrderBasicParamDTO request);

    void unfreezeZdfyServiceOrder(Integer startId);

    /**
     * 中山六院重新冻结
     * @param updateFundStatusFlag 是否更新经费状态
     * @param lastOrderDate 处理截止时间
     * @param testOrderNos 指定处理的订单号(为空则处理所有符合条件的订单)
     * @param excludeOrderNos 需要排除的订单号(为空则不排除)
     */
    void zhongShanLiuYuanRefreeze(Boolean updateFundStatusFlag, Date lastOrderDate, List<String> testOrderNos, List<String> excludeOrderNos);

    void fixAbnormalAddress(OrderBasicParamDTO request);

    /**
     * 更新审批人订单id
     * @param backDoorRequest
     */
    void updateApprovedUserOrderIds(BackDoorRequest backDoorRequest);

    /**
     * 保存验收审批电子签
     * @param request
     */
    void saveAcceptElectronicSign(SaveElectronicSignRequest request);


    /**
     * 针对厦门妇幼，福建肿瘤 对待发货下的订单进行旧数据兼容（补充填写商品信息字段）
     */
    void fillProductInfo();
}
