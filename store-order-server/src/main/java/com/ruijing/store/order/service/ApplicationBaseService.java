package com.ruijing.store.order.service;

import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.MoneyControlOperatingEnum;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: zhangzhifeng
 * @date: 2021-05-07 18:26
 **/

public interface ApplicationBaseService {


    /**
     * 采购管控--金额统计
     *   同一课题组同一商品名称同一自然月采购金额累计不能超过X万
     *
     * @param orderMasterDO 订单信息
     * @param cardId 经费卡id 如果不传则会去查表 t_ref_fundcard_order
     * @param operation 操作类型 从ApplyManageOperationEnum中获取 --> 需要区分 关闭订单、商品退货、换卡释放(暨大)两种业务区别
     * @param goodsReturnInfoDetailVOS 退货详情信息
     */
    Boolean updateApplyManageProductUsage(OrderMasterDO orderMasterDO, Integer operation, String cardId, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS);

    /**
     * 根据单据类型获取采购单或竞价单的创建时间(用于解冻、采购金额统计)
     * @param orderMasterDO
     * @return
     */
    Date getCreateTimeByOrderType(OrderMasterDO orderMasterDO);
}
