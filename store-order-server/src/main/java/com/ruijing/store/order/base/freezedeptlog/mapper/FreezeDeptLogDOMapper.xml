<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.freezedeptlog.mapper.FreezeDeptLogDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO">
    <!--@mbg.generated-->
    <!--@Table t_freeze_dept_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="dep_id" jdbcType="INTEGER" property="depId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_deleted" jdbcType="INTEGER" property="hasDeleted" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, dep_id, `type`, is_deleted, creation_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_freeze_dept_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_freeze_dept_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_freeze_dept_log (org_id, dep_id, `type`,
    is_deleted, creation_time, update_time
    )
    values (#{orgId,jdbcType=INTEGER}, #{depId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER},
    #{hasDeleted,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_freeze_dept_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="depId != null">
        dep_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="hasDeleted != null">
        is_deleted,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="depId != null">
        #{depId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="hasDeleted != null">
        #{hasDeleted,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO">
    <!--@mbg.generated-->
    update t_freeze_dept_log
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="depId != null">
        dep_id = #{depId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="hasDeleted != null">
        is_deleted = #{hasDeleted,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO">
    <!--@mbg.generated-->
    update t_freeze_dept_log
    set org_id = #{orgId,jdbcType=INTEGER},
    dep_id = #{depId,jdbcType=INTEGER},
    `type` = #{type,jdbcType=INTEGER},
    is_deleted = #{hasDeleted,jdbcType=INTEGER},
    creation_time = #{creationTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2019-10-29-->
  <select id="findAllByIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_freeze_dept_log
        where is_deleted=#{isDeleted,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2019-10-29-->
  <insert id="insertList">
    INSERT INTO t_freeze_dept_log(
    id,
    org_id,
    dep_id,
    type,
    is_deleted,
    creation_time,
    update_time
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.id,jdbcType=BIGINT},
      #{element.orgId,jdbcType=INTEGER},
      #{element.depId,jdbcType=INTEGER},
      #{element.type,jdbcType=INTEGER},
      #{element.hasDeleted,jdbcType=INTEGER},
      #{element.creationTime,jdbcType=TIMESTAMP},
      #{element.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2019-10-30-->
  <update id="updateIsDeletedByIdIn">
    update t_freeze_dept_log
    set is_deleted=#{updatedIsDeleted,jdbcType=INTEGER}
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2019-11-27-->
  <update id="updateDeletedByOrgIdAndDepId">
    update t_freeze_dept_log
    set `is_deleted` = 1
    where org_id=#{orgId,jdbcType=INTEGER} and dep_id=#{depId,jdbcType=INTEGER}
    and is_deleted = 0
  </update>

<!--auto generated by MybatisCodeHelper on 2020-01-30-->
  <select id="findAllByCreationTimeBetweenAndHasDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_freeze_dept_log
        where 1 = 1
        <if test="minCreationTime != null">
          and creation_time <![CDATA[>]]> #{minCreationTime,jdbcType=TIMESTAMP}
        </if>

        <if test="maxCreationTime != null">
          and creation_time <![CDATA[<]]> #{maxCreationTime,jdbcType=TIMESTAMP}
        </if>

        <if test="hasDeleted != null">
          and is_deleted=#{hasDeleted,jdbcType=INTEGER}
        </if>
    </select>

<!--auto generated by MybatisCodeHelper on 2021-01-21-->
  <select id="findFreezeByOrgIdAndDepId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_freeze_dept_log
    where org_id=#{orgId,jdbcType=INTEGER}
    <if test="deptIds != null and deptIds.size() > 0">
      and dep_id in
      <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
        #{deptId, jdbcType=INTEGER}
      </foreach>
    </if>
    and is_deleted = 0
  </select>
</mapper>