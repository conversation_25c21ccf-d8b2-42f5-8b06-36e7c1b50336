package com.ruijing.store.order.rpc.client;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.reagent.bid.api.base.bidmaster.dto.BidMasterDTO;
import com.reagent.store.control.api.directory.dto.DirectoryRequestDTO;
import com.reagent.store.control.api.directory.dto.DirectoryResultDTO;
import com.reagent.store.control.api.directory.service.DirectoryRPCService;
import com.reagent.store.control.api.purchaselimit.dto.*;
import com.reagent.store.control.api.purchaselimit.enums.PurchaseLimitOrderTypeEnum;
import com.reagent.store.control.api.purchaselimit.enums.PurchaseLimitStaticBusinessTypeEnum;
import com.reagent.store.control.api.purchaselimit.service.ApplyPurchaseLimitRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.exception.CodeException;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-07-06 10:16
 * @description:
 **/
@ServiceClient
public class CategoryDirectoryClient {

    @MSharpReference
    private DirectoryRPCService directoryRpcService;

    @MSharpReference
    private ApplyPurchaseLimitRPCService applyPurchaseLimitRpcService;

    @Resource
    private BidClient bidClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    /**
     * 单位-是否开启政采目录缓存
     */
    private final Cache<String, Boolean> orgCodeCategoryDirectoryOnMap = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).build();

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取政采目录")
    public List<DirectoryResultDTO> getCategoryDirectoryByIds(List<Integer> idList){
        DirectoryRequestDTO directoryRequestDTO = new DirectoryRequestDTO();
        directoryRequestDTO.setDirectoryIds(idList);
        RemoteResponse<List<DirectoryResultDTO>> response = directoryRpcService.getCategoryDirectoryByIds(directoryRequestDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    /**
     * 是否开启政采目录
     * @param orgCode 机构代码
     * @param throwIfGetFail 获取失败是否抛出异常。发生但不抛出异常则返回false
     * @return 是否开启
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "是否开启政采目录")
    public boolean isCategoryDirectoryOn(String orgCode, boolean throwIfGetFail){
        return this.isCategoryDirectoryOn(orgCode, throwIfGetFail, false);
    }

    /**
     * 是否开启政采目录
     * @param orgCode 机构代码
     * @param throwIfGetFail 获取失败是否抛出异常。发生但不抛出异常则返回false
     * @param refreshCache 是否无视缓存获取并刷新
     * @return 是否开启
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "是否开启政采目录")
    public boolean isCategoryDirectoryOn(String orgCode, boolean throwIfGetFail, boolean refreshCache){
        Boolean isOn = null;
        if(!refreshCache){
            isOn = orgCodeCategoryDirectoryOnMap.getIfPresent(orgCode);
            if(isOn != null){
                return isOn;
            }
        }

        try{
            ChangePurchaseLimitDTO changePurchaseLimitDTO = new ChangePurchaseLimitDTO();
            changePurchaseLimitDTO.setOrgCode(orgCode);
            RemoteResponse<Boolean> response = applyPurchaseLimitRpcService.isOpenPurchaseLimit(changePurchaseLimitDTO);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            isOn = response.getData();
            if(isOn != null){
                orgCodeCategoryDirectoryOnMap.put(orgCode, isOn);
            }
        }catch (Exception e){
            if(throwIfGetFail){
                throw e;
            }
        }
        return Boolean.TRUE.equals(isOn);
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "取消政采目录统计", operationType = OperationType.WRITE)
    public void cancelOrderStatistics(Integer orgId, Integer orderId){
        this.cancelOrderStatistics(orgId, New.list(orderId));
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "取消政采目录统计（整个订单）", operationType = OperationType.WRITE)
    public void cancelOrderStatistics(Integer orgId, List<Integer> orderIdList){
        this.cancelOrderStatistics(orgId, orderIdList, null);
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "取消政采目录统计(针对某个经费卡做返还)", operationType = OperationType.WRITE)
    public void cancelOrderStatistics(Integer orgId, List<Integer> orderIdList, List<String> fundCardIdList){
        PurchaseLimitCancelStatisDTO purchaseLimitCancelStatisDTO = new PurchaseLimitCancelStatisDTO();
        purchaseLimitCancelStatisDTO.setOrgId(orgId);
        purchaseLimitCancelStatisDTO.setOrderIdList(orderIdList);
        // 经费卡为空，则返还整个订单
        purchaseLimitCancelStatisDTO.setFundCardIds(fundCardIdList);
        RemoteResponse<Boolean> response = applyPurchaseLimitRpcService.cancelOrderStatistics(purchaseLimitCancelStatisDTO);
        Preconditions.isTrue(response.isSuccess() && Boolean.TRUE.equals(response.getData()), response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "政采目录统计", operationType = OperationType.WRITE)
    public void saveOverStatistics(List<OrderMasterDO> orderMasterDOList, List<OrderDetailDO> orderDetailDOList, List<RefFundcardOrderDTO> fundCardDTOList, Integer userId){
        List<OrderMasterDO> appOrderList = orderMasterDOList.stream().filter(orderMasterDO -> orderMasterDO.getFtbuyappid() != null).collect(Collectors.toList());
        List<OrderMasterDO> bidOrderList = orderMasterDOList.stream().filter(orderMasterDO -> orderMasterDO.getBidOrderId() != null).collect(Collectors.toList());

        // 获取上游单据创建时间
        Map<Long, Date> appIdDateMap = new HashMap<>(appOrderList.size());
        Map<String, Date> bidNoDateMap = new HashMap<>(bidOrderList.size());
        if(CollectionUtils.isNotEmpty(appOrderList)){
            List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseClient.findByMasterId(appOrderList.stream().map(OrderMasterDO::getFtbuyappid).collect(Collectors.toList()));
            appIdDateMap = DictionaryUtils.toMap(applicationMasterDTOList, ApplicationMasterDTO::getId, ApplicationMasterDTO::getCreateTime);
        }
        if(CollectionUtils.isNotEmpty(bidOrderList)){
            List<BidMasterDTO> bidMasterDTOList = bidClient.findBidMasterByBidNoList(bidOrderList.stream().map(OrderMasterDO::getBidOrderId).collect(Collectors.toList()));
            bidNoDateMap = DictionaryUtils.toMap(bidMasterDTOList, BidMasterDTO::getBidNo, BidMasterDTO::getBidDate);
        }

        Map<Integer, List<OrderDetailDO>> orderIdDetailList = DictionaryUtils.groupBy(orderDetailDOList, OrderDetailDO::getFmasterid);
        Map<Integer, List<RefFundcardOrderDTO>> orderFundCardListMap = DictionaryUtils.groupBy(fundCardDTOList, refFundcardOrderDTO -> Integer.parseInt(refFundcardOrderDTO.getOrderId()));
        // 拼接数据
        PurchaseLimitStaticListDTO purchaseLimitStaticListDTO = new PurchaseLimitStaticListDTO();
        purchaseLimitStaticListDTO.setUserId(userId);
        purchaseLimitStaticListDTO.setOrgId(orderMasterDOList.get(0).getFuserid());
        Map<Long, Date> finalAppIdDateMap = appIdDateMap;
        Map<String, Date> finalBidNoDateMap = bidNoDateMap;
        List<PurchaseLimitStaticDTO> purchaseLimitStaticDTOList = orderMasterDOList.stream().map(orderMasterDO -> {
            List<OrderDetailDO> matchDetailList = orderIdDetailList.get(orderMasterDO.getId());
            PurchaseLimitStaticDTO purchaseLimitStaticDTO = new PurchaseLimitStaticDTO();
            purchaseLimitStaticDTO.setBusinessId(orderMasterDO.getId());
            purchaseLimitStaticDTO.setBusinessTypeEnum(PurchaseLimitStaticBusinessTypeEnum.ORDER);
            purchaseLimitStaticDTO.setOrderNo(orderMasterDO.getForderno());

            Date upperBizCreateDate = orderMasterDO.getFtbuyappid() != null ? finalAppIdDateMap.get(orderMasterDO.getFtbuyappid().longValue()) : finalBidNoDateMap.get(orderMasterDO.getBidOrderId());
            List<PurchaseLimitStaticDetailDTO> purchaseLimitStaticDetailDTOList = matchDetailList.stream().map(orderDetailDO -> {
                PurchaseLimitStaticDetailDTO purchaseLimitStaticDetailDTO = new PurchaseLimitStaticDetailDTO();
                purchaseLimitStaticDetailDTO.setCateName(orderDetailDO.getFclassification());
                purchaseLimitStaticDetailDTO.setCategoryId(orderDetailDO.getCategoryid());
                purchaseLimitStaticDetailDTO.setDate(upperBizCreateDate);
                purchaseLimitStaticDetailDTO.setDirectoryId(orderDetailDO.getCategoryDirectoryId());
                purchaseLimitStaticDetailDTO.setPrice(orderDetailDO.getFbidprice());
                purchaseLimitStaticDetailDTO.setProductId(orderDetailDO.getProductSn());
                purchaseLimitStaticDetailDTO.setProductName(orderDetailDO.getFgoodname());
                return purchaseLimitStaticDetailDTO;
            }).collect(Collectors.toList());
            purchaseLimitStaticDTO.setDetails(purchaseLimitStaticDetailDTOList);

            List<RefFundcardOrderDTO> matchFundCardList = orderFundCardListMap.get(orderMasterDO.getId());
            List<PurchaseLimitStaticFundCardDTO> purchaseLimitStaticFundCardDTOList = matchFundCardList.stream().map(item->{
                PurchaseLimitStaticFundCardDTO purchaseLimitStaticFundCardDTO = new PurchaseLimitStaticFundCardDTO();
                purchaseLimitStaticFundCardDTO.setCardId(RefFundcardOrderTranslator.getLastLevelCardId(item));
                purchaseLimitStaticFundCardDTO.setMoney(this.getFreezeAmount(item));
                return purchaseLimitStaticFundCardDTO;
            }).collect(Collectors.toList());
            purchaseLimitStaticDTO.setFundCardInfoDTOS(purchaseLimitStaticFundCardDTOList);
            return purchaseLimitStaticDTO;
        }).collect(Collectors.toList());
        purchaseLimitStaticListDTO.setPurchaseLimitStaticDTOList(purchaseLimitStaticDTOList);

        // 调用
        RemoteResponse<Boolean> response = applyPurchaseLimitRpcService.purchaseControlStatisticsBatch(purchaseLimitStaticListDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        BusinessErrUtil.isTrue(Boolean.TRUE.equals(response.getData()), response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "政采限额校验")
    public void checkPurchaseLimitForDetails(List<OrderMasterDO> orderMasterDOList, List<OrderDetailDO> orderDetailDOList, List<RefFundcardOrderDTO> fundCardDTOList){
        Map<Integer, List<OrderDetailDO>> orderIdDetailList = DictionaryUtils.groupBy(orderDetailDOList, OrderDetailDO::getFmasterid);
        Map<Integer, List<RefFundcardOrderDTO>> orderFundCardListMap = DictionaryUtils.groupBy(fundCardDTOList, refFundcardOrderDTO -> Integer.parseInt(refFundcardOrderDTO.getOrderId()));

        List<PurchaseLimitOrderInfoDTO> purchaseLimitOrderInfoDTOList = orderMasterDOList.stream().map(orderMasterDO -> {
            List<OrderDetailDO> matchDetailList = orderIdDetailList.get(orderMasterDO.getId());

            PurchaseLimitOrderInfoDTO purchaseLimitOrderInfoDTO = new PurchaseLimitOrderInfoDTO();
            purchaseLimitOrderInfoDTO.setOrderNo(orderMasterDO.getForderno());
            purchaseLimitOrderInfoDTO.setBuyerId(orderMasterDO.getFbuyerid());
            purchaseLimitOrderInfoDTO.setOrgId(orderMasterDO.getFuserid());
            purchaseLimitOrderInfoDTO.setOrgCode(orderMasterDO.getFusercode());
            purchaseLimitOrderInfoDTO.setOrderTypeEnum(ProcessSpeciesEnum.NORMAL.getValue().equals(orderMasterDO.getSpecies().intValue()) ? PurchaseLimitOrderTypeEnum.ONLINE_ORDER_TYPE : PurchaseLimitOrderTypeEnum.OFFLINE_ORDER_TYPE);

            List<PurchaseLimitOrderDetailDTO> purchaseLimitOrderDetailDTOList = matchDetailList.stream().map(detail->{
                PurchaseLimitOrderDetailDTO purchaseLimitOrderDetailDTO = new PurchaseLimitOrderDetailDTO();
                purchaseLimitOrderDetailDTO.setCateName(detail.getFclassification());
                purchaseLimitOrderDetailDTO.setCategoryId(detail.getCategoryid());
                purchaseLimitOrderDetailDTO.setDirectoryId(detail.getCategoryDirectoryId());
                purchaseLimitOrderDetailDTO.setOrderdate(new Timestamp(orderMasterDO.getForderdate().getTime()));
                purchaseLimitOrderDetailDTO.setProductId(detail.getProductSn());
                purchaseLimitOrderDetailDTO.setProductName(detail.getFgoodname());
                purchaseLimitOrderDetailDTO.setTotalPrice(detail.getFbidamount());
                return purchaseLimitOrderDetailDTO;
            }).collect(Collectors.toList());
            purchaseLimitOrderInfoDTO.setOrderDetailDTOList(purchaseLimitOrderDetailDTOList);

            List<RefFundcardOrderDTO> matchFundCardList = orderFundCardListMap.get(orderMasterDO.getId());
            List<PurchaseLimitFundCardInfoDTO> purchaseLimitFundCardInfoDTOList = matchFundCardList.stream().map(card->{
                PurchaseLimitFundCardInfoDTO purchaseLimitFundCardInfoDTO = new PurchaseLimitFundCardInfoDTO();
                purchaseLimitFundCardInfoDTO.setCardId(RefFundcardOrderTranslator.getLastLevelCardId(card));
                purchaseLimitFundCardInfoDTO.setMoney(this.getFreezeAmount(card));
                return purchaseLimitFundCardInfoDTO;
            }).collect(Collectors.toList());
            purchaseLimitOrderInfoDTO.setFundCardList(purchaseLimitFundCardInfoDTOList);
            return purchaseLimitOrderInfoDTO;
        }).collect(Collectors.toList());

        // 没有批量接口，循环调用进行校验
        for(PurchaseLimitOrderInfoDTO purchaseLimitOrderInfoDTO : purchaseLimitOrderInfoDTOList){
            RemoteResponse<Boolean> response = applyPurchaseLimitRpcService.checkPurchaseLimitForDetails(purchaseLimitOrderInfoDTO);
            Preconditions.isTrue(response.isSuccess() && Boolean.TRUE.equals(response.getData()), new CodeException(CodeException.SHOW_DIALOG_CODE, response.getMsg()));
        }
    }

    /**
     * 冻结/换卡失败，政采目录回滚。
     * 目前策略是冻结及换卡前，先将新卡需要占用的额度统计了，不对旧卡处理。如果失败则释放占用额度。
     * 冻结：失败了只需要释放将要冻结的卡的额度
     * 换卡：失败了释放最终换到的卡的额度，成功了释放旧卡额度，达到最终一致性。
     *      但是由于政采目录额度计算需要根据冻结金额和商品占比计算，如果换卡前后新旧卡存在同一张卡，需要重新计算整张单的统计额度
     *
     * @param orderList 订单数据
     * @param orderDetailDOList 订单详情
     * @param oldCardList 旧卡，冻结业务传空
     * @param newCardList 新卡
     * @param userId 操作人
     */
    @ServiceLog(description = "冻结/换卡失败，政采目录统计回滚", operationType = OperationType.WRITE)
    public void rollbackSaveStatisticsWhenFail(List<OrderMasterDO> orderList, List<OrderDetailDO> orderDetailDOList, List<RefFundcardOrderDO> oldCardList, List<RefFundcardOrderDTO> newCardList, Integer userId){
        List<Integer> orderIdList = orderList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        Integer orgId = orderList.get(0).getFuserid();
        if(CollectionUtils.isEmpty(oldCardList)){
            // 没有旧卡，那就是冻结失败导致的，回滚单据的所有统计项
            this.cancelOrderStatistics(orgId, orderIdList);
        }
        List<String> oldCardIdList = oldCardList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        List<String> newCardIdList = newCardList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        // 是否新旧换卡数据都有相同卡的数据
        List<String> sameCardIdList = newCardIdList.stream().filter(oldCardIdList::contains).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(sameCardIdList)){
            // 如果新旧卡都有相同数据，需要重新做统计。由管控服务去重新计算管控额度。
            // PS:鉴于比较少换多张卡的业务，这里暂时没有考虑特殊情况，如下：
            // 1.cancel成功了，刚好有别的单用了额度导致save超支了怎么办？
            // 2.如果cancel成功了，但是save它失败了怎么处理？
            this.cancelOrderStatistics(orgId, orderIdList);
            this.saveOverStatistics(orderList, orderDetailDOList, oldCardList.stream().map(RefFundcardOrderTranslator::doToDto).collect(Collectors.toList()), userId);
        }else {
            // 只需要取消新卡的管控即可
            this.cancelOrderStatistics(orgId, orderIdList, newCardIdList);
        }
    }

    /**
     * 获取冻结金额
     */
    private BigDecimal getFreezeAmount(RefFundcardOrderDTO card){
        return card.getUsemoney() != null ? card.getUsemoney() : card.getFreezeAmount();
    }
}
