package com.ruijing.store.order.gateway.print.dto.order;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/2/28 15:34
 * @description
 */
@RpcModel("订单打印数据")
public class OrderPrintDataDTO implements Serializable {

    private static final long serialVersionUID = -5928373296349562835L;
    @RpcModelProperty("打印订单数据列表")
    private List<OrderPrintDataItemDTO> orderMasterList;

    public List<OrderPrintDataItemDTO> getOrderMasterList() {
        return orderMasterList;
    }

    public void setOrderMasterList(List<OrderPrintDataItemDTO> orderMasterList) {
        this.orderMasterList = orderMasterList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderPrintDataDTO.class.getSimpleName() + "[", "]")
                .add("orderMasterList=" + orderMasterList)
                .toString();
    }
}
