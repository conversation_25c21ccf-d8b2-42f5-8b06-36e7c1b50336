package com.ruijing.store.order.business.service.impl.strategy;

import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广东药科的验收策略
 */
@Service(OrderAcceptConstant.GUANG_DONG_YAO_KE_DA_XUE_ACCEPT)
public class GuangDongYaoKeOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval, boolean platformWorkFunds, boolean unRelateOrderData) {
        // 收货后, 配置了验收审批的单优先级最高
        //1 验收--完成模式,2 验收--审批模式 ,3 验收--（待）结算 ，0 -- 无匹配模式报错

        // 配置了验收审批
        if (isAcceptApproval) {
            return 2;
        }

        boolean isDistributeStatement = OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderMasterDO.getFundStatus());
        //自结算使用 验收--完成 模式
        if (isDistributeStatement) {
            return 1;
        }

        return 3;
    }
}
