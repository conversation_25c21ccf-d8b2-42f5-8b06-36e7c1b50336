package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/20 11:51
 * @description 订单对接状态VO
 */
@RpcModel("订单管理-我的订单列表订单主表信息的订单推送状态")
public class OrderPushEventStatusVO implements Serializable {

    private static final long serialVersionUID = 5150767666513179206L;
    
    /**
     * 订单推送事件类型
     */
    @RpcModelProperty("订单推送事件类型")
    private Integer orderPushEventType;

    /**
     * 订单推送事件状态
     */
    @RpcModelProperty("订单推送事件状态")
    private Integer orderPushEventStatus;

    /**
     * 失败原因
     */
    @RpcModelProperty("失败原因")
    private String failReason;

    public Integer getOrderPushEventType() {
        return orderPushEventType;
    }

    public void setOrderPushEventType(Integer orderPushEventType) {
        this.orderPushEventType = orderPushEventType;
    }

    public Integer getOrderPushEventStatus() {
        return orderPushEventStatus;
    }

    public void setOrderPushEventStatus(Integer orderPushEventStatus) {
        this.orderPushEventStatus = orderPushEventStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderPushEventStatusVO{");
        sb.append("orderPushEventType=").append(orderPushEventType);
        sb.append(", orderPushEventStatus=").append(orderPushEventStatus);
        sb.append(", failReason='").append(failReason).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
