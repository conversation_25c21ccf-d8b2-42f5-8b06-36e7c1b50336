package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.wms.api.service.WmsRuleRpcService;

/**
 * @description:
 * @author: zhong<PERSON><PERSON>i
 * @create: 2021/5/13 11:23
 **/
@ServiceClient
public class WmsRuleRpcServiceClient {

    @MSharpReference(remoteAppkey = "store-wms-service", timeout = "30000")
    private WmsRuleRpcService wmsRuleRpcService;

    @ServiceLog(description = "获取新库房配置", serviceType = ServiceType.RPC_CLIENT)
    public Boolean getNewWareHouseConfig(Integer orgId) {
        Preconditions.notNull(orgId, "获取新库房配置失败, orgId为空");
        ApiResult<Boolean> response = wmsRuleRpcService.autoInbound(orgId);
        Preconditions.isTrue(response.successful(), "获取新库房配置失败" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }
}