package com.ruijing.store.order.base.minor.mapper;

import com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/3 0003 11:15
 * @Version 1.0
 * @Desc:描述
 */
public interface ProductDescriptionSnapshotMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductDescriptionSnapshotDO record);

    int insertSelective(ProductDescriptionSnapshotDO record);

    ProductDescriptionSnapshotDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductDescriptionSnapshotDO record);

    int updateByPrimaryKey(ProductDescriptionSnapshotDO record);

    int insertList(@Param("list")List<ProductDescriptionSnapshotDO> list);

    /**
     * @description: 通过订单详情id，订单来源类型， 商品id（sn）获取商品快照描述
     * @date: 2021/1/6 10:04
     * @author: zengyan<PERSON>
     * @param businessId
     * @param businessType
     * @param productId
     * @return java.util.List<com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO>
     */
    List<ProductDescriptionSnapshotDO> findProductDescByIds(@Param("businessId")String businessId, @Param("businessType")Integer businessType, @Param("productId")Long productId);
}