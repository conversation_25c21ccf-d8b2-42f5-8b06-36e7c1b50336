package com.ruijing.store.order.other.callback.impl;

import com.reagent.order.dto.response.OrderEventPushResultResponseDTO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.other.callback.PushInvoiceCallbackService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/9/5 16:21
 * @description
 */
@Service
public class PushInvoiceCallbackServiceImpl implements PushInvoiceCallbackService {

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    /**
     * 处理回调
     *
     * @param orderEventPushResultResponseDTO 回调入参
     */
    @Override
    @ServiceLog(description = "推送发票回调", operationType = OperationType.WRITE)
    public void handleCallBack(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO) {
        String orderNo = orderEventPushResultResponseDTO.getOrderNo();
        OrderMasterDO orderMaster = orderMasterMapper.findByForderno(orderNo);
        // 记录发票推送状态日志
        Integer approveStatus;
        switch (orderEventPushResultResponseDTO.getOrderEventStatusEnum()) {
            case COMPLETE:
                approveStatus = OrderApprovalEnum.PUSH_INVOICE_SUCCESS.getValue();
                break;
            case FAILED:
                approveStatus = OrderApprovalEnum.PUSH_INVOICE_FAIL.getValue();
                break;
            default:
                approveStatus = OrderApprovalEnum.PUSH_INVOICE.getValue();
                break;
        }
        orderApprovalLogService.saveApprovalLog(orderMaster.getId(), approveStatus, DockingConstant.SYSTEM_OPERATOR_ID, orderEventPushResultResponseDTO.getFailReason());
    }
}
