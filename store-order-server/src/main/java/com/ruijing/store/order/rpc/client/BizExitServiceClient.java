package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import com.ruijing.store.wms.api.service.BizExitService;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date Created in 15:31 2020/2/7.
 * @Modified
 * @Description
 */
@ServiceClient
public class BizExitServiceClient {

    private static final String CAT_TYPE = "BizExitServiceClient";

    @MSharpReference(remoteAppkey = "store-wms-service")
    private BizExitService bizExitService;

    /**
     * 根据出库单id获取出库单详情
     *
     * @param outWarehouseApplicationId 出库申请单Id
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public BizWarehouseExitDTO queryExitById(Integer outWarehouseApplicationId) {
        String handlerName = "queryExitById";
        ApiResult<BizWarehouseExitDTO> response = bizExitService.queryExitById(outWarehouseApplicationId);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + outWarehouseApplicationId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 根据订单号获取出库申请单列表
     *
     * @param orderNo 订单号
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseExitDTO> queryExitByOrderNo(String orderNo) {
        Preconditions.hasText(orderNo, "入参为空");
        String handlerName = "queryExitByOrderNo";
        ApiResult<List<BizWarehouseExitDTO>> response = bizExitService.queryExitByOrderNo(orderNo);
        if (response.successful()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + orderNo + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * @description: 根据订单号批量获取出库申请单
     * @date: 2021/3/17 9:44
     * @author: zengyanru
     * @param orderNoList
     * @return java.util.List<com.ruijing.store.wms.api.dto.BizWarehouseExitDTO>
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据订单号批量获取出库申请单")
    public List<BizWarehouseExitDTO> queryExitByOrderNoList(List<String> orderNoList) {
        Preconditions.notEmpty(orderNoList, "queryExitByOrderNoList入参为空");
        ApiResult<List<BizWarehouseExitDTO>> listApiResult = bizExitService.queryExitByOrderNoList(orderNoList);
        Preconditions.notNull(listApiResult, "RPC方法BizExitService.queryExitByOrderNoList返回值为空\n入参：orderNoList="+orderNoList);
        Preconditions.isTrue(listApiResult.successful(), "RPC方法BizExitService.queryExitByOrderNoList返回失败\n入参：orderNoList="+orderNoList);
        return listApiResult.getData() == null ? Collections.emptyList() : listApiResult.getData();
    }
}
