package com.ruijing.store.order.base.minor.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.ThirdPartyCallbackInfoDTO;
import com.ruijing.store.order.base.minor.mapper.ThirdPartyCallbackInfoMapper;
import com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO;
import com.ruijing.store.order.base.minor.service.ThirdPartyCallbackInfoService;
import com.ruijing.store.order.base.minor.translator.ThirdPartyCallbackInfoTranslator;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 第三方回调信息业务处理
 * @author: zhong<PERSON><PERSON>i
 * @create: 2019/9/30 14:42
 **/
@Service
@ServiceLog
public class ThirdPartyCallbackInfoServiceImpl implements ThirdPartyCallbackInfoService {

    @Resource
    private ThirdPartyCallbackInfoMapper thirdPartyCallbackInfoMapper;

    @Override
    @ServiceLog( operationType = OperationType.WRITE)
    public RemoteResponse insertCallbackInfo(ThirdPartyCallbackInfoDTO infoDTO) {
        ThirdPartyCallbackInfoDO info = ThirdPartyCallbackInfoTranslator.dto2DO(infoDTO);
        int result = thirdPartyCallbackInfoMapper.insertSelective(info);
        return RemoteResponse.custom().setData(result).setSuccess().build();
    }

    @Override
    @ServiceLog( operationType = OperationType.WRITE)
    public RemoteResponse updateCallbackInfoById(ThirdPartyCallbackInfoDTO infoDTO) {
        ThirdPartyCallbackInfoDO info = ThirdPartyCallbackInfoTranslator.dto2DO(infoDTO);
        int result = thirdPartyCallbackInfoMapper.updateByPrimaryKeySelective(info);
        return RemoteResponse.custom().setData(result).setSuccess().build();
    }

    @Override
    public RemoteResponse insertCallbackInfo(String msg, String orgCode, String num) {
        ThirdPartyCallbackInfoDTO thirdPartCallBack = new ThirdPartyCallbackInfoDTO();
        thirdPartCallBack.setMessage(StringUtils.substring(msg, 0, 245));
        thirdPartCallBack.setOrgcode(orgCode);
        thirdPartCallBack.setNum(num);
        return insertCallbackInfo(thirdPartCallBack);
    }
}
