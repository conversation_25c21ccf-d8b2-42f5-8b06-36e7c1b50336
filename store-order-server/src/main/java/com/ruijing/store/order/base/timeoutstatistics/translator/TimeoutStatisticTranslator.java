package com.ruijing.store.order.base.timeoutstatistics.translator;

import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO;

/**
 * @description: DO 和 DTO 的转换类
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/30 9:35
 **/
public class TimeoutStatisticTranslator {

    public static TimeoutStatisticsDTO do2DTO(TimeoutStatisticsDO statisticsDO) {
        TimeoutStatisticsDTO dto = new TimeoutStatisticsDTO();
        dto.setId(statisticsDO.getId());
        dto.setOrgId(statisticsDO.getOrgId());
        dto.setDepId(statisticsDO.getDepId());
        dto.setType(statisticsDO.getType());
        dto.setAmount(statisticsDO.getAmount());
        dto.setCreationTime(statisticsDO.getCreationTime());
        dto.setUpdateTime(statisticsDO.getUpdateTime());
        dto.setOldConfigDay(statisticsDO.getOldConfigDay());
        dto.setOldConfigAmount(statisticsDO.getOldConfigAmount());

        return dto;
    }

    public static TimeoutStatisticsDO dto2DO(TimeoutStatisticsDTO dto) {
        TimeoutStatisticsDO statisticsDO = new TimeoutStatisticsDO();
        statisticsDO.setId(dto.getId());
        statisticsDO.setOrgId(dto.getOrgId());
        statisticsDO.setDepId(dto.getDepId());
        statisticsDO.setType(dto.getType());
        statisticsDO.setAmount(dto.getAmount());
        statisticsDO.setCreationTime(dto.getCreationTime());
        statisticsDO.setUpdateTime(dto.getUpdateTime());
        statisticsDO.setOldConfigDay(dto.getOldConfigDay());
        statisticsDO.setOldConfigAmount(dto.getOldConfigAmount());

        return statisticsDO;
    }
}
