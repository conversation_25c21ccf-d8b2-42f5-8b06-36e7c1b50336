package com.ruijing.store.order.business.service;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.dto.OrderExportQueryDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.upload.client.FileUploadClient;
import com.ruijing.fundamental.upload.client.FileUploadResp;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderIdInvoiceTitleDO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoQueryDTO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoResponseDTO;
import com.ruijing.store.order.base.excel.dto.OrderExportRequestDTO;
import com.ruijing.store.order.rpc.client.InvoiceClient;
import com.ruijing.store.order.rpc.client.OrderExportClient;
import com.ruijing.store.order.rpc.client.UploadFileClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.invoicetitle.InvoiceTitleDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/6 11:24
 * @Description
 **/
@Service
public class OrderExportService {

    @Resource
    private OrderExportClient orderExportClient;


    @Resource
    private UserClient userClient;


    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private InvoiceClient invoiceClient;

    /**
     * 经费卡层级深度
     */
    private static final Integer MAX_FUND_CARD_LEVEL = 3;

    /**
     * @description: 查找订单导出后的列表
     * @date: 2021/1/6 11:34
     * @author: zengyanru
     * @param orderExcelInfoQueryDTO
     * @param rjSessionInfo
     * @return com.reagent.order.base.order.dto.BasePageResultDTO<com.ruijing.store.order.base.excel.dto.OrderExcelInfoResponseDTO>
     */
    @ServiceLog(description = "查找订单导出后的列表", serviceType = ServiceType.COMMON_SERVICE)
    public BasePageResultDTO<OrderExcelInfoResponseDTO> findOrderExcelList(OrderExportQueryDTO orderExportQueryDTO, OrderExcelInfoQueryDTO orderExcelInfoQueryDTO, RjSessionInfo rjSessionInfo) {
        if (orderExportQueryDTO == null) {
            orderExportQueryDTO = new OrderExportQueryDTO();
        }
        orderExportQueryDTO.setPageNo(orderExcelInfoQueryDTO.getPageNo());
        orderExportQueryDTO.setPageSize(orderExcelInfoQueryDTO.getPageSize());
        orderExportQueryDTO.setExportDateStart(orderExcelInfoQueryDTO.getStartDate());
        orderExportQueryDTO.setExportDateEnd(orderExcelInfoQueryDTO.getEndDate());
        orderExportQueryDTO.setStatus(orderExcelInfoQueryDTO.getStatus());
        orderExportQueryDTO.setUserId(rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue());
        orderExportQueryDTO.setOrgId(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER));
        BasePageResultDTO<OrderExportDTO> remotePageResult = orderExportClient.findOrderExportList(orderExportQueryDTO);
        BasePageResultDTO<OrderExcelInfoResponseDTO> basePageResultDTO = this.getBasePageResultDTO(remotePageResult);
        return basePageResultDTO;
    }

    /**
     * 封装分页列表对象
     * @param remotePageResult
     * @return
     */
    @ServiceLog(description = "封装分页列表对象", serviceType = ServiceType.COMMON_SERVICE)
    public BasePageResultDTO<OrderExcelInfoResponseDTO> getBasePageResultDTO(BasePageResultDTO<OrderExportDTO> remotePageResult) {
        BasePageResultDTO<OrderExcelInfoResponseDTO> basePageResultDTO = new BasePageResultDTO<>();
        basePageResultDTO.setPageNo(remotePageResult.getPageNo());
        basePageResultDTO.setPageSize(remotePageResult.getPageSize());
        basePageResultDTO.setTotal(remotePageResult.getTotal());
        List<OrderExcelInfoResponseDTO> orderExcelInfoResponseDTOList = New.list();
        List<OrderExportDTO> orderExportDTOList = remotePageResult.getData();
        if (CollectionUtils.isNotEmpty(orderExportDTOList)) {
            for (OrderExportDTO orderExportDTO : orderExportDTOList) {
                OrderExcelInfoResponseDTO orderExcelInfoResponseDTO = new OrderExcelInfoResponseDTO();
                orderExcelInfoResponseDTO.setId(orderExportDTO.getId());
                orderExcelInfoResponseDTO.setFileName(orderExportDTO.getFileName());
                orderExcelInfoResponseDTO.setExportDate(orderExportDTO.getExportDate());
                orderExcelInfoResponseDTO.setUserId(orderExportDTO.getUserId());
                orderExcelInfoResponseDTO.setUserName(orderExportDTO.getUserName());
                orderExcelInfoResponseDTO.setStatus(orderExportDTO.getStatus());
                orderExcelInfoResponseDTO.setFileType(orderExportDTO.getFileType());
                orderExcelInfoResponseDTO.setFileUrl(orderExportDTO.getFileUrl());
                orderExcelInfoResponseDTOList.add(orderExcelInfoResponseDTO);
            }
        }
        orderExcelInfoResponseDTOList = orderExcelInfoResponseDTOList.stream().sorted(Comparator.comparing(OrderExcelInfoResponseDTO::getExportDate).reversed()).collect(Collectors.toList());
        basePageResultDTO.setData(orderExcelInfoResponseDTOList);
        return basePageResultDTO;
    }

    /**
     * 单条（边）链路，匹配后不显示子节点
     * @param fundCardDTO 当前轮询到的订单对应经费卡id-的最顶级经费卡对象
     * @param fundCardId 当前轮询到的订单对应的经费卡id
     */
    private void setFundCardInfoTrace(FundCardDTO fundCardDTO, String fundCardId, Map<Integer, String> levelNameMap,
                                      Map<Integer, String> levelCodeMap) {
        Integer level = fundCardDTO.getLevel();
        levelNameMap.put(level, fundCardDTO.getName());
        levelCodeMap.put(level, fundCardDTO.getCode());
        if (Objects.equals(fundCardDTO.getId(), fundCardId)) {
            // 将大于此level的内容全部删除
            if (level < MAX_FUND_CARD_LEVEL) {
                for (Integer i = (level + 1); i <= MAX_FUND_CARD_LEVEL; i++) {
                    levelNameMap.remove(i);
                }
            }
            throw new IllegalStateException("运行至此满足条件，强制退出");
        } else {
            List<FundCardDTO> subFundCardList = fundCardDTO.getFundCardDTOs() == null ? New.list() : fundCardDTO.getFundCardDTOs();
            for (FundCardDTO subFundCard : subFundCardList) {
                this.setFundCardInfoTrace(subFundCard, fundCardId, levelNameMap, levelCodeMap);
            }
        }
    }

    /**
     * 通过多叉树一条边的记录，补全当前订单的某张经费卡的链路信息
     * @param levelNameListMap
     * @param levelCodeListMap
     * @param levelNameMap
     * @param levelCodeMap
     */
    private void fundCardTraceFinalOp(Map<Integer, List<String>> levelNameListMap, Map<Integer, List<String>> levelCodeListMap,
                                      Map<Integer, String> levelNameMap, Map<Integer, String> levelCodeMap) {
        for (Integer i = 0; i < MAX_FUND_CARD_LEVEL; i++) {
            if (levelNameMap.get(i+1) != null) {
                levelNameListMap.get(i+1).add(levelNameMap.get(i+1));
            }
            if (levelCodeMap.get(i+1) != null) {
                levelCodeListMap.get(i+1).add(levelCodeMap.get(i+1));
            }
        }
        return;
    }

    /**
     * 获取某个层级的经费卡id（订单存的经费卡id）与全层级经费卡的对应关系
     * @param fundcardIdList
     * @param cardInfoList
     * @return
     */
    private Map<String, FundCardDTO> getCardIdFundCardMap(List<String> fundcardIdList, List<FundCardDTO> cardInfoList) {
        Map<String, FundCardDTO> cardIdInfoMap = new HashMap<>();
        // 存在现在有的经费卡的设置，没有的就跳过(先暴力实现)
        for (FundCardDTO fundCardDTO : cardInfoList) {
            // 一级
            if (fundcardIdList.contains(fundCardDTO.getId())) {
                cardIdInfoMap.put(fundCardDTO.getId(), fundCardDTO);
                continue;
            }
            // 二级
            List<FundCardDTO> secondFundCardList = fundCardDTO.getFundCardDTOs();
            if (CollectionUtils.isNotEmpty(secondFundCardList)) {
                for (FundCardDTO secondCard : secondFundCardList) {
                    if (fundcardIdList.contains(secondCard.getId())) {
                        cardIdInfoMap.put(secondCard.getId(), fundCardDTO);
                    }
                }
                // 三级
                for (FundCardDTO secondCard : secondFundCardList) {
                    List<FundCardDTO> thirdFundCardList = secondCard.getFundCardDTOs();
                    if (CollectionUtils.isNotEmpty(thirdFundCardList)) {
                        for (FundCardDTO thirdCard : thirdFundCardList) {
                            if (fundcardIdList.contains(thirdCard.getId())) {
                                cardIdInfoMap.put(thirdCard.getId(), fundCardDTO);
                            }
                        }
                    }
                }
            }
        }
        return cardIdInfoMap;
    }

    /**
     * 递归查询经费卡并且设值(打印整棵树)
     * @param levelNameListMap
     * @param levelCodeListMap
     * @param fundCardDTO
     */
    private void recurSetNameCodeList(Map<Integer, List<String>> levelNameListMap, Map<Integer, List<String>> levelCodeListMap, FundCardDTO fundCardDTO) {
        if (fundCardDTO == null) {
            return;
        }
        // 当前数据处理
        Integer level = fundCardDTO.getLevel();
        String name = fundCardDTO.getName();
        String code = fundCardDTO.getCode();
        if (StringUtils.isNotBlank(name)) {
            levelNameListMap.get(level).add(name);
        }
        if (StringUtils.isNotBlank(code)) {
            levelCodeListMap.get(level).add(code);
        }
        List<FundCardDTO> subFundCardList = fundCardDTO.getFundCardDTOs();
        if (subFundCardList == null) {
            return;
        }
        for (FundCardDTO cardDTO : subFundCardList) {
            recurSetNameCodeList(levelNameListMap, levelCodeListMap, cardDTO);
        }
    }

    /**
     * 递归查询经费卡并且设值(特殊单位，当前为温医和仁济)
     * @param levelNameListMap
     * @param levelCodeListMap
     * @param fundCardDTO
     */
    private void recurSetNameCodeListSpecialOrg(Map<Integer, List<String>> levelNameListMap, Map<Integer, List<String>> levelCodeListMap, FundCardDTO fundCardDTO) {
        if (fundCardDTO == null) {
            return;
        }
        // 当前数据处理
        Integer level = fundCardDTO.getLevel();
        String name = fundCardDTO.getName();
        String code = "";
        if (Objects.equals(1, level)) {
            code = fundCardDTO.getCode();
        } else if (Objects.equals(2, level)) {
            code = fundCardDTO.getFirstLevelCode() + "-" + fundCardDTO.getSecondLevelCode();
        } else if (Objects.equals(3, level)) {
            code = fundCardDTO.getFirstLevelCode() + "-" + fundCardDTO.getSecondLevelCode() + "-" + fundCardDTO.getThirdLevelCode();
        }
        levelNameListMap.get(level).add(name);
        levelCodeListMap.get(level).add(code);

        List<FundCardDTO> subFundCardList = fundCardDTO.getFundCardDTOs();
        if (subFundCardList == null) {
            return;
        }
        for (FundCardDTO cardDTO : subFundCardList) {
            recurSetNameCodeListSpecialOrg(levelNameListMap, levelCodeListMap, cardDTO);
        }
    }

    /**
     * @description: 专为发票抬头构造
     * @date: 2021/3/26 17:17
     * @author: zengyanru
     * @param orgId
     * @param orderIdList
     * @return java.util.Map<java.lang.Integer,java.lang.String>
     */
    private Map<Integer, String> constructIdInvoiceTitleMap(Integer orgId, List<Integer> orderIdList) {
        Map<Integer, String> idInvoiceTitleMap = null;
        if (CollectionUtils.isEmpty(orderIdList)) {
            return idInvoiceTitleMap;
        }
        if (Objects.equals(orgId, OrgEnum.JIANG_SU_SHENG_ZHONG_YI_YUAN.getValue())) {
            List<OrderIdInvoiceTitleDO> idInvoiceTitleList = orderMasterMapper.findIdAndInvoiceTitleByIdIn(orderIdList);
            if (CollectionUtils.isEmpty(idInvoiceTitleList)) {
                return idInvoiceTitleMap;
            }
            // 通过发票id找发票title
            List<Integer> invoiceTitileIdList = idInvoiceTitleList.stream().map(OrderIdInvoiceTitleDO::getInvoiceTitleId).collect(Collectors.toList());
            List<InvoiceTitleDTO> invoiceTitleByIdList = invoiceClient.findInvoiceTitleByIdList(invoiceTitileIdList);
            Map<Integer, String> invoiceIdTitleMap = new HashMap<>();
            for (InvoiceTitleDTO invoiceTitleDTO : invoiceTitleByIdList) {
                invoiceIdTitleMap.put(invoiceTitleDTO.getId(), invoiceTitleDTO.getTitle());
            }
            // 根据订单id-发票抬头id 查找发票抬头
            idInvoiceTitleMap = new HashMap<>();
            for (OrderIdInvoiceTitleDO idInvoiceInfo : idInvoiceTitleList) {
                String curTitle = invoiceIdTitleMap.get(idInvoiceInfo.getInvoiceTitleId());
                idInvoiceTitleMap.put(idInvoiceInfo.getId(), curTitle);
            }
        }
        return idInvoiceTitleMap;
    }

    /**
     * @description: 根据条目id删除已导出的条目
     * @date: 2021/1/6 14:44
     * @author: zengyanru
     * @param orderExportRequestDTO
     * @return java.lang.Boolean
     */
    @ServiceLog(description = "根据条目id删除已导出的条目", serviceType = ServiceType.COMMON_SERVICE)
    public Boolean deleteExportedFile(RjSessionInfo rjSessionInfo, OrderExportRequestDTO orderExportRequestDTO, boolean hmsFlag) {
        Preconditions.notNull(orderExportRequestDTO, "入参不能为空");
        Preconditions.notNull(orderExportRequestDTO.getId(), "请传入id");
        Integer id = orderExportRequestDTO.getId();

        OrderExportDTO orderExportDTO = orderExportClient.findById(id);
        if (rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER) == null) {
            LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
            rjSessionInfo.setUserId(loginInfo.getUserId().longValue());
        }
        BusinessErrUtil.isTrue(orderExportDTO.getUserId() != null && Objects.equals(orderExportDTO.getUserId().longValue(), rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER)), ExecptionMessageEnum.CANNOT_DELETE_OTHER_USER_INFO);
        if (orderExportDTO != null && StringUtils.isNotBlank(orderExportDTO.getFileUrl())) {
            String fileUrl = orderExportDTO.getFileUrl();
            FileUploadClient fileUploadClient = UploadFileClient.getFileUploadClientById(hmsFlag ? UploadFileClient.ORDER_LIST_ID : UploadFileClient.BUYER_ORDER_LIST_ID);
            FileUploadResp fileUploadResp = fileUploadClient.deleteFiles(New.list(fileUrl));
            Preconditions.isTrue(fileUploadResp.isSuccess(), fileUploadResp.getMsg());
        }
        orderExportClient.deleteOrderExportInfoById(id);
        return true;
    }
}
