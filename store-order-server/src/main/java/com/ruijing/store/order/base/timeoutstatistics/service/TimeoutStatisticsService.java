package com.ruijing.store.order.base.timeoutstatistics.service;

import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.ordermaster.dto.GoodsReturnParamDTO;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;

import java.util.List;
import java.util.Map;

/**
 * @description: 超时统计相关业务
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/23 14:09
 **/
public interface TimeoutStatisticsService {
    /**
     * 批量插入统计数据
     * @param list
     * @return
     */
    int insertBatch(List<TimeoutStatisticsDTO> list);

    /**
     * 清表操作
     * @return
     */
    int deleteAll();

    /**
     * 查找所有超时订单数据
     * @return
     */
    List<TimeoutStatisticsDTO> findAll();

    /**
     * 根据课题组/部门id 和 超时类型 查找超时记录
     * @param orgId         医院/机构 id
     * @param departmentId  部门/课题组 id
     * @param type          超时类型
     * @return
     */
    TimeoutStatisticsDTO findByDepartmentIdAndType(int orgId, int departmentId, int type);

    /**
     * 更新计数
     * @param dto
     * @return
     */
    int updateAmountById(TimeoutStatisticsDTO dto);

    /**
     * 删除记录
     * @param id
     * @return
     */
    int deleteById(long id);

    /**
     * 根据退货商品信息增加超时订单统计数据
     * @param count              增加数量
     * @param params             商品信息
     * @param timeOutConfigMap   机构配置信息
     */
    boolean executeTimeOutStatisticsIncrease(Integer count, GoodsReturnParamDTO params, Map<String, Integer> timeOutConfigMap);

    /**
     * 根据退货商品信息减少超时订单统计数据
     * @param count              减少数量
     * @param params             商品信息
     * @param timeOutConfigMap   机构配置信息
     */
    boolean executeTimeOutStatisticsDecrease(Integer count, GoodsReturnParamDTO params, Map<String, Integer> timeOutConfigMap);

    /**
     * 减少超时订单统计数据
     * @param count         减少数量
     * @param orgId         医院机构 id
     * @param orgCode       医院机构 code
     * @param departmentId  课题组 id
     * @param timeOutType   超时类型
     * @return
     */
    void executeTimeOutStatisticsDecrease(Integer count,
                                          Integer orgId,
                                          String orgCode,
                                          Integer departmentId,
                                          TimeOutBusinessType timeOutType);

}
