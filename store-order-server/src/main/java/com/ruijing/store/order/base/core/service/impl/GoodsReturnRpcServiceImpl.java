package com.ruijing.store.order.base.core.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnInvalidedEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.goodsreturn.GoodsReturnRpcService;
import com.ruijing.store.order.api.base.goodsreturn.dto.*;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2020/7/20 4:15 下午
 */
@MSharpService
public class GoodsReturnRpcServiceImpl implements GoodsReturnRpcService {

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogMapper;

    @Resource
    private SupplierGoodsReturnService supplierGoodsReturnService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private UserClient userClient;
    
    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @Override
    @ServiceLog(description = "插入退货单记录", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> insertList(List<GoodsReturnDTO> goodsReturnList) {
        Assert.notEmpty(goodsReturnList, "插入失败，退货单入参为空！");

        List<GoodsReturn> goodsReturns = goodsReturnList.stream().map(GoodsReturnTranslator::dto2GoodsReturn).collect(Collectors.toList());
        Integer affect = goodsReturnMapper.insertList(goodsReturns);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect).build();
    }

    @Override
    @ServiceLog(description = "通过detailId数组查询退货数据")
    public RemoteResponse<List<GoodsReturnDTO>> findByDetailIdList(GoodsReturnRequestDTO request) {
        List<Integer> detailIdList = request.getDetailIdList();
        Preconditions.notEmpty(detailIdList, "查询失败！detailId集合不能为空！");
        Preconditions.isTrue(detailIdList.size() < 101, "查询失败！单次查询的数量不能超过100！");
        List<GoodsReturn> byDetailIdIn = goodsReturnMapper.findByDetailIdIn(detailIdList);

        List<GoodsReturnDTO> result = byDetailIdIn.stream().map(GoodsReturnTranslator::doToDto).collect(Collectors.toList());
        return RemoteResponse.<List<GoodsReturnDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "通过Id数组查询退货数据")
    public RemoteResponse<List<GoodsReturnDTO>> findByIdList(GoodsReturnRequestDTO request) {
        List<Integer> idList = request.getIdList();
        Preconditions.notEmpty(idList, "查询失败！Id集合不能为空！");
        Preconditions.isTrue(idList.size() < 101, "查询失败！单次查询的数量不能超过100！");
        List<GoodsReturn> byDetailIdIn = goodsReturnMapper.findByIdIn(idList);

        List<GoodsReturnDTO> result = byDetailIdIn.stream().map(GoodsReturnTranslator::doToDto).collect(Collectors.toList());
        return RemoteResponse.<List<GoodsReturnDTO>>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<List<GoodsReturnDTO>> findByOrderIdList(GoodsReturnRequestDTO request) {
        List<Integer> orderIdList = request.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "查询失败！orderId集合不能为空！");
        Preconditions.isTrue(orderIdList.size() < 101, "查询失败！单次查询的数量不能超过100！");
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByOrderIds(orderIdList);
        Stream<GoodsReturn> stream = goodsReturns.stream();
        if(CollectionUtils.isNotEmpty(request.getGoodsReturnStatusList())){
            stream = stream.filter(goodsReturn -> request.getGoodsReturnStatusList().contains(goodsReturn.getGoodsReturnStatus()));
        }
        List<GoodsReturnDTO> result = stream.map(GoodsReturnTranslator::doToDto).collect(Collectors.toList());
        return RemoteResponse.<List<GoodsReturnDTO>>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<List<GoodsReturnDTO>> findByReturnNoList(GoodsReturnRequestDTO request) {
        List<String> returnNoList = request.getReturnNoList();
        Preconditions.notEmpty(returnNoList, "查询失败！集合不能为空！");
        Preconditions.isTrue(returnNoList.size() < 101, "查询失败！单次查询的数量不能超过100！");
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByReturnNoIn(returnNoList);
        List<GoodsReturnDTO> result = goodsReturns.stream().map(GoodsReturnTranslator::doToDto).collect(Collectors.toList());
        return RemoteResponse.<List<GoodsReturnDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "通过orderId和退货状态查询退货单信息", serviceType = ServiceType.COMMON_SERVICE)
    public RemoteResponse<Integer> cancelGoodsReturn(GoodsReturnRequestDTO request) {
        List<Integer> orderIdList = request.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "退货单撤销失败！订单id不为空！");
        Preconditions.isTrue(orderIdList.size() < 101, "撤销失败, 单次操作数量不可超过100");
        int affect = goodsReturnMapper.updateGoodsReturnStatusByOrderIdIn(GoodsReturnStatusEnum.CANCEL_REQUEST.getCode(), GoodsReturnInvalidedEnum.INVALIDED.getCode(), orderIdList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    @ServiceLog(description = "通过orderId和退货状态查询退货单信息", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Integer> goodsReturnSuccessCallBack(GoodsReturnRequestDTO request) {
        List<Integer> orderIdList = request.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "退货回调失败，订单id不可为空！");
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        Preconditions.notEmpty(orderMasterDOList, "退货回调失败，查无订单信息！");
        Map<Integer, OrderMasterDO> orderIdIdentityMap = DictionaryUtils.toMap(orderMasterDOList, OrderMasterDO::getId, Function.identity());

        Integer orgId = orderMasterDOList.get(0).getFuserid();
        OrganizationDTO organizationDTO = userClient.getOrgById(orgId);
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(orderIdList);
        Preconditions.notEmpty(goodsReturnList, "退货回调失败，查无退货单信息！");
        boolean validOrderReturn = goodsReturnList.stream().allMatch(g -> orderIdIdentityMap.containsKey(g.getOrderId()));
        Preconditions.isTrue(validOrderReturn, "退货回调失败，退货单关联订单信息非法！");

        for (GoodsReturn goodsReturn : goodsReturnList) {
            OrderMasterDO orderMasterDO = orderIdIdentityMap.get(goodsReturn.getOrderId());
            // 中大解冻回调后退货处理成功
            supplierGoodsReturnService.returnSuccess(goodsReturn, organizationDTO, orderMasterDO);
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(orderIdList.size());
    }

    /**
     * 查询退货统计信息（统计时间范围内金额等功能）
     *
     * @param request
     * @return
     */
    @Override
    @ServiceLog(description = "查询退货统计信息（统计时间范围内金额等功能）", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<GoodsReturnStatResultDTO> goodsReturnStats(GoodsReturnStatRequestDTO request) {
        Preconditions.isTrue(request.getStartTime() != null && request.getEndTime() != null && CollectionUtils.isNotEmpty(request.getOrgIdList()), "查询退货统计信息入参时间范围和orgId不可为空");
        Date startDate = new Date(request.getStartTime());
        Date endDate = new Date(request.getEndTime());
        List<Integer> operationList = request.getFilterReturnOperationList();

        // 先从日志表查询出退货单id, 再查询退货单
        List<Integer> returnIdList = goodsReturnLogMapper.findReturnIdByOperationAndUpdateTime(operationList, startDate, endDate);
        List<GoodsReturn> returnGoodsStatList = goodsReturnMapper.findByIdInAndOrgIdIn(returnIdList, request.getOrgIdList());

        // 如果指定了其他参数（如入库状态，则需要查询订单，控制返回的值）TODO：后续改并行
        Integer inputInventStatus = request.getInventoryStatus();
        Map<Integer, Byte> masterInventoryStatusMap = new HashMap<>();
        if (inputInventStatus != null) {
            List<Integer> orderIdList = returnGoodsStatList.stream().map(GoodsReturn::getOrderId).collect(Collectors.toList());
            List<OrderMasterDO> orderMasterList = orderMasterMapper.findByIdIn(orderIdList);
            masterInventoryStatusMap = orderMasterList.stream().collect(Collectors.toMap(OrderMasterDO::getId, OrderMasterDO::getInventoryStatus));
        }

        BigDecimal returnAmountTotal = BigDecimal.ZERO;
        for (GoodsReturn goodsReturn : returnGoodsStatList) {
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
            Integer curOrderInventStatus = masterInventoryStatusMap.get(goodsReturn.getOrderId()) == null ? null : masterInventoryStatusMap.get(goodsReturn.getOrderId()).intValue();
            if ((inputInventStatus == null) || (Objects.equals(curOrderInventStatus, inputInventStatus))) {
                for (GoodsReturnInfoDetailVO returnDetail : goodsReturnInfoDetailVOList) {
                    returnAmountTotal = returnAmountTotal.add(returnDetail.getAmount());
                }
            }
        }
        GoodsReturnStatResultDTO result = new GoodsReturnStatResultDTO();
        result.setReturnAmount(returnAmountTotal.setScale(2, RoundingMode.HALF_UP));
        return RemoteResponse.<GoodsReturnStatResultDTO>custom().setData(result).setSuccess();
    }

    @Override
    public RemoteResponse<List<GoodsReturnDTO>> goodsReturnInfoStats(GoodsReturnStatRequestDTO request) {
        Preconditions.isTrue(request.getStartTime() != null && request.getEndTime() != null && CollectionUtils.isNotEmpty(request.getOrgIdList()), "查询退货统计信息入参时间范围和orgId不可为空");
        Date startDate = new Date(request.getStartTime());
        Date endDate = new Date(request.getEndTime());
        List<Integer> operationList = request.getFilterReturnOperationList();

        // 先从日志表查询出退货单id, 再查询退货单
        List<Integer> returnIdList = goodsReturnLogMapper.findReturnIdByOperationAndUpdateTime(operationList, startDate, endDate);
        if (CollectionUtils.isEmpty(returnIdList)) {
            return RemoteResponse.<List<GoodsReturnDTO>>custom().setData(Collections.emptyList()).setFailure("查无数据");
        }
        List<GoodsReturn> returnGoodsStatList = goodsReturnMapper.findByIdInAndOrgIdIn(returnIdList, request.getOrgIdList());
        List<GoodsReturnDTO> collect = returnGoodsStatList.stream().map(GoodsReturnTranslator::doToDto).collect(Collectors.toList());

        // 如果指定了其他参数（如入库状态，则需要查询订单，控制返回的值
        Integer inputInventStatus = request.getInventoryStatus();
        if (inputInventStatus != null) {
            Set<Integer> orderIdSet = returnGoodsStatList.stream().map(GoodsReturn::getOrderId).collect(Collectors.toSet());
            List<OrderMasterDO> orderMasterList = orderMasterMapper.findByIdIn(orderIdSet);
            final Set<Integer> ids = orderMasterList.stream().filter(it -> it.getInventoryStatus() != null).filter(it -> inputInventStatus.equals(it.getInventoryStatus().intValue())).map(OrderMasterDO::getId).collect(Collectors.toSet());
            collect = collect.stream().filter(it -> ids.contains(it.getOrderId())).collect(Collectors.toList());
        }

        return RemoteResponse.<List<GoodsReturnDTO>>custom().setData(collect).setSuccess();
    }

    @Override
    public RemoteResponse<List<GoodsReturnLogDTO>> listGoodsReturnLog(GoodsReturnLogRequestDTO goodsReturnLogRequestDTO) {
        List<Integer> returnIds = goodsReturnLogRequestDTO.getReturnIds();
        List<GoodsReturnLogDO> goodsReturnLogDOList = goodsReturnLogDOMapper.findByReturnIdIn(returnIds);
        List<GoodsReturnLogDTO> goodsReturnLogDTOS = goodsReturnLogDOList.stream().map(goodsReturnLogDO -> {
            GoodsReturnLogDTO goodsReturnLogDTO = new GoodsReturnLogDTO();
            goodsReturnLogDTO.setId(goodsReturnLogDO.getId());
            goodsReturnLogDTO.setReturnId(goodsReturnLogDO.getReturnId());
            goodsReturnLogDTO.setImagesURL(goodsReturnLogDO.getImagesURL());
            goodsReturnLogDTO.setOperationType(goodsReturnLogDO.getOperationType());
            goodsReturnLogDTO.setOperatorType(goodsReturnLogDO.getOperatorType());
            goodsReturnLogDTO.setOperatorName(goodsReturnLogDO.getOperatorName());
            goodsReturnLogDTO.setRemark(goodsReturnLogDO.getRemark());
            goodsReturnLogDTO.setCreateTime(goodsReturnLogDO.getCreateTime());
            goodsReturnLogDTO.setUpdateTime(goodsReturnLogDO.getUpdateTime());
            return goodsReturnLogDTO;
        }).collect(Collectors.toList());
        return RemoteResponse.success(goodsReturnLogDTOS);
    }

    @Override
    public RemoteResponse<Boolean> cancelGoodsReturnByThunder(GoodsReturnRequestDTO goodsReturnRequestDTO) {
        Preconditions.isTrue(goodsReturnRequestDTO.getReturnNoList().size() <= 100,"传入的退货单号数据不能大于100");
        GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO = new GoodsReturnBaseRequestDTO();
        goodsReturnBaseRequestDTO.setReturnNoList(goodsReturnRequestDTO.getReturnNoList());
        boolean isSuccess = buyerGoodsReturnService.cancelGoodsReturnByThunder(goodsReturnBaseRequestDTO);
        return RemoteResponse.<Boolean>custom().setData(isSuccess).setSuccess();
    }

    /**
     * 强制取消订单退货申请
     *
     * @param goodsReturnCancelRequstDTO 退货取消参数
     * @return 是否成功
     */
    @Override
    @ServiceLog(description = "强制取消订单退货申请", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> forceCancelGoodsReturn(GoodsReturnCancelRequstDTO goodsReturnCancelRequstDTO) {
        List<Integer> orderIdList = goodsReturnCancelRequstDTO.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "传入的订单id不能为空");
        BusinessErrUtil.isTrue(orderIdList.size() <= 100, ExecptionMessageEnum.MAX_100_RETURN_REQUESTS);
        boolean isSuccess = buyerGoodsReturnService.forceCancelGoodsReturn(goodsReturnCancelRequstDTO);
        return RemoteResponse.success(isSuccess);
    }
}
