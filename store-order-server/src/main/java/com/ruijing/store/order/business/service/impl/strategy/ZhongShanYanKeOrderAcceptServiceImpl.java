package com.ruijing.store.order.business.service.impl.strategy;


import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @description: 中山眼科的验收策略
 * @author: zhongyulei
 * @create: 2022-01-17 11:55
 */
@Service(OrderAcceptConstant.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN_ACCEPT)
public class ZhongShanYanKeOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Resource
    private WaitingStatementService waitingStatementService;

    /**
     * 计算入库模式
     * 
     * @param orderMasterDO    订单主表信息
     * @param receiptConfigMap 验收配置
     * @param orderDetailList  订单商品详情
     * @return 入库模式
     */
    @Override
    public InWarehouseModeEnum calculateInWarehouseMode(OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList) {
        // 先推到待结算, 发起结算后才入库
        waitingStatementService.pushWaitingStatement(orderMasterDO.getFusercode(), New.list(orderMasterDO.getId()), InventoryStatusEnum.WAITING_FOR_STORE.getCode());
        // 写死待入库
        return InWarehouseModeEnum.WAITING_INBOUND;
    }
}