package com.ruijing.store.order.base.util;

import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.ruijing.fundamental.common.collections.New;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @author: Liwenyu
 * @create: 2024-03-12 09:47
 * @description:
 */
public class FundCardUtils {

    /**
     * 获取所有层级的卡id-卡映射
     * @param allLevelCards 所有父层级卡
     * @return 所有层级的卡id-卡映射
     */
    public static Map<String, FundCardDTO> getAllLevelCardIdCardMap(List<FundCardDTO> allLevelCards){
        Map<String, FundCardDTO> allLevelCardIdCardMap = New.map();
        fillAllLevelCardIdCardMap(allLevelCards, allLevelCardIdCardMap);
        return allLevelCardIdCardMap;
    }

    /**
     * 获取第一层级卡
     * @param currentCard 当前经费卡
     * @param fullLevelCardIdCardMap 完全等级的cardId-card映射，请通过com.ruijing.store.order.base.util.FundCardUtils#getAllLevelCardIdCardMap(java.util.List)获取
     * @return 获取第一层级的卡
     */
    public static FundCardDTO getFirstLevelCard(FundCardDTO currentCard, Map<String, FundCardDTO> fullLevelCardIdCardMap){
        FundCardDTO projectCard = currentCard;
        while (projectCard.getParentId() != null){
            FundCardDTO parentCard = fullLevelCardIdCardMap.get(projectCard.getParentId());
            if(parentCard == null){
                break;
            }
            projectCard = parentCard;
        }
        return projectCard;
    }



    private static void fillAllLevelCardIdCardMap(List<FundCardDTO> currentLevelCards, Map<String, FundCardDTO> allLevelCardIdCardMap){
        if(CollectionUtils.isEmpty(currentLevelCards)){
            return;
        }
        for(FundCardDTO card : currentLevelCards){
            if(card == null){
                continue;
            }
            allLevelCardIdCardMap.put(card.getId(), card);
            fillAllLevelCardIdCardMap(card.getFundCardDTOs(), allLevelCardIdCardMap);
        }
    }
}
