package com.ruijing.store.order.gateway.fundcard.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethodParam;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.annotation.Method;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.mhttp.annotation.Mapping;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderFundCardResponseDTO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.gateway.fundcard.request.FundCardSpecialRequestDTO;
import com.ruijing.store.order.gateway.fundcard.request.OrderFundCardListParam;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardUnfreezeService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/5/21 12:48
 * @Description: 订单经费卡业务
 */
@MSharpService(isGateway = "true")
@RpcApi(value = "order经费卡服务", description = "order经费卡服务")
@RpcMapping("/orderFundCard")
public class OrderFundCardGWController {

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Resource
    private OrderFundCardUnfreezeService orderFundCardUnfreezeService;

    @RpcMethod("重新冻结/扣减经费")
    @RpcMapping("/freezeFundCard")
    public RemoteResponse<Integer> freezeFundCard(OrderBasicParamDTO request) {
        Integer affect = refFundcardOrderService.orderFundCardFreeze(request);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    /**
     * 暨大，修改经费卡
     *
     * @param request 修改的经费卡
     * @return
     */
    @RpcMethod("暨大，修改经费卡")
    @RpcMapping("/changeCards")
    public RemoteResponse changeFundCard(FundCardSpecialRequestDTO request) {
        return orderFundCardService.saveFundCardCache(request);
    }

    @RpcMethod("解绑经费卡/释放经费")
    @RpcMapping("/unfreezeFundCard")
    public RemoteResponse<Boolean> unfreezeFundCard(OrderBasicParamDTO request) {
        boolean successful = orderFundCardService.unfreezeFundCard(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(successful);
    }

    /**
     * 查找课题组的经费卡
     *
     * @param rjSessionInfo
     * @param param
     * @return
     */
    @RpcMapping("/getOrderFundCards")
    @RpcMethod(value = "查找课题组的经费卡")
    public RemoteResponse<List<OrderFundCardResponseDTO>> findFundCard(RjSessionInfo rjSessionInfo, OrderFundCardListParam param) {
        return RemoteResponse.success(orderFundCardService.findFundCardByDepartmentId(rjSessionInfo, param));
    }

    @RpcMapping("/reUnfreezeFundCard")
    @RpcMethod(value = "重新释放经费",notes = "订单列表重新解冻按钮调用")
    public RemoteResponse<Boolean> reUnfreezeFundCard(OrderBasicParamDTO request) {
        boolean successful = orderFundCardService.reUnfreezeFundCard(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(successful);
    }

    @Mapping("/changeOrderToSelfStatement")
    @Method(value = "修改订单为自结算", description = "修改订单为自结算(南方医科大学用)")
    public RemoteResponse<Boolean> changeOrderToSelfStatement(RjSessionInfo rjSessionInfo,
                                                              @RpcMethodParam(includePropertyNames = {"orderId"}) OrderBasicParamDTO request){
        orderFundCardUnfreezeService.unFreezeAndChangeOrderToSelfStatement(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER),
                rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue(),
                request.getOrderId());
        return RemoteResponse.success();
    }
}


