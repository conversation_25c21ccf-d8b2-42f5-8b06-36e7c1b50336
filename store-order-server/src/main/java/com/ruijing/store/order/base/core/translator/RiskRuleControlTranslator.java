package com.ruijing.store.order.base.core.translator;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.risk.control.enums.LimitControlOperateTypeEnum;
import com.ruijing.fundamental.risk.control.riskrule.dto.purchaseLimitControl.*;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.goodsreturn.vo.RiskRuleLimitCheckErrorInfoVO;
import com.ruijing.store.goodsreturn.vo.RiskRuleLimitErrorInfoVO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Name: RiskControlTranslator
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/5/22
 */
public class RiskRuleControlTranslator {


    /**
     * 订单生成采购限额管控请求参数
     *
     * @param orderMasterDO
     * @param goodsReturnInfoDetailVOList
     * @param orderDetailDOList
     * @return
     */
    public static LimitControlOrderParamDTO order2LimitControlOrderParam(OrderMasterDO orderMasterDO, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList, List<OrderDetailDO> orderDetailDOList, List<RefFundcardOrderDTO> refFundCardByOrderIdList) {
        LimitControlOrderParamDTO paramDTO = new LimitControlOrderParamDTO();
        List<OrderLimitParamDTO> orderLimitParamDTOList = new ArrayList<>();
        paramDTO.setOrgId(orderMasterDO.getFuserid());
        // 订单商品信息
        List<ProductInfoDTO> productInfoDTOS = New.list();
        Map<Integer, OrderDetailDO> idOrderDetailMap = orderDetailDOList.stream().collect(Collectors.toMap(OrderDetailDO::getId, Function.identity()));
        BigDecimal returnAmount = null;
        // 退货情况，通过退货单和订单详情获取（区分部分退货情况）
        if (CollectionUtils.isNotEmpty(goodsReturnInfoDetailVOList)) {
            returnAmount = BigDecimal.ZERO;
            for (GoodsReturnInfoDetailVO goodsReturnInfoDetailVO : goodsReturnInfoDetailVOList) {
                OrderDetailDO orderDetailDO = idOrderDetailMap.get(Integer.parseInt(goodsReturnInfoDetailVO.getDetailId()));
                ProductInfoDTO productInfoDTO = goodsReturn2ProductInfoDTO(orderDetailDO, goodsReturnInfoDetailVO);
                returnAmount = returnAmount.add(productInfoDTO.getPurchaseAmount());
                productInfoDTOS.add(productInfoDTO);
            }
        } else {
            // 取消订单情况。通过订单详情获取
            productInfoDTOS = orderDetailDOList.stream()
                    .map(RiskRuleControlTranslator::OrderDetailDO2ProductInfoDTO)
                    .collect(Collectors.toList());
        }
        List<FundCardParamDTO> fundCardParamDTOS = New.emptyList();
        if (CollectionUtils.isNotEmpty(refFundCardByOrderIdList)) {
            if(refFundCardByOrderIdList.size() > 1 || returnAmount == null){
                // 多经费卡，或非退货，只能整单退
                fundCardParamDTOS = refFundCardByOrderIdList.stream()
                        .map(refFundcardOrderDTO -> refFundcardOrderDTO2FundCardParamDTO(refFundcardOrderDTO, orderMasterDO))
                        .collect(Collectors.toList());
            }else {
                fundCardParamDTOS = New.list(refFundcardOrderDTO2FundCardParamDTO(refFundCardByOrderIdList.get(0), orderMasterDO));
                fundCardParamDTOS.get(0).setUseAmount(returnAmount);
            }
        }
        // 订单信息
        OrderLimitParamDTO orderLimitParamDTO = orderMaster2OrderLimitParamDTO(orderMasterDO, productInfoDTOS, fundCardParamDTOS);
        orderLimitParamDTOList.add(orderLimitParamDTO);
        paramDTO.setOrderLimitParamDTOList(orderLimitParamDTOList);
        return paramDTO;
    }

    /**
     * 经费卡信息转换
     *
     * @param refFundcardOrderDTO
     * @param orderMasterDO
     * @return
     */
    private static FundCardParamDTO refFundcardOrderDTO2FundCardParamDTO(RefFundcardOrderDTO refFundcardOrderDTO, OrderMasterDO orderMasterDO) {
        FundCardParamDTO fundCardParamDTO = new FundCardParamDTO();
        fundCardParamDTO.setFundCardId(RefFundcardOrderTranslator.getLastLevelCardId(refFundcardOrderDTO));
        fundCardParamDTO.setFundCardNo(refFundcardOrderDTO.getCardNo());
        fundCardParamDTO.setUseAmount(orderMasterDO.getForderamounttotal());
        return fundCardParamDTO;
    }

    /**
     * 订单详情转ProductInfoDTO
     *
     * @param orderDetailDO
     * @param goodsReturnInfoDetailVO
     * @return
     */
    private static ProductInfoDTO goodsReturn2ProductInfoDTO(OrderDetailDO orderDetailDO, GoodsReturnInfoDetailVO goodsReturnInfoDetailVO) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProductId(orderDetailDO.getProductSn());
        productInfoDTO.setProductName(orderDetailDO.getFgoodname());
        productInfoDTO.setSuppId(orderDetailDO.getSuppId());
        productInfoDTO.setSuppName(orderDetailDO.getSuppName());
        productInfoDTO.setSuppCode(orderDetailDO.getSuppCode());
        productInfoDTO.setPurchaseAmount(goodsReturnInfoDetailVO.getAmount());
        productInfoDTO.setProductPrice(orderDetailDO.getFbidprice());
        productInfoDTO.setProductQuantity(goodsReturnInfoDetailVO.getQuantity().intValue());
        productInfoDTO.setProductCode(goodsReturnInfoDetailVO.getGoodsCode());
        productInfoDTO.setBrandId(orderDetailDO.getFbrandid());
        productInfoDTO.setBrandName(orderDetailDO.getFbrand());
        productInfoDTO.setFirstLevelCategoryId(Long.valueOf(orderDetailDO.getFirstCategoryId()));
        productInfoDTO.setFirstLevelCategoryName(orderDetailDO.getFirstCategoryName());
        productInfoDTO.setSecondLevelCategoryId(Long.valueOf(orderDetailDO.getSecondCategoryId()));
        productInfoDTO.setSecondLevelCategoryName(orderDetailDO.getSecondCategoryName());
        productInfoDTO.setThirdLevelCategoryId(Long.valueOf(orderDetailDO.getCategoryid()));
        productInfoDTO.setThirdLevelCategoryName(orderDetailDO.getFclassification());
        productInfoDTO.setDirectoryId(orderDetailDO.getCategoryDirectoryId());
        productInfoDTO.setSpec(orderDetailDO.getFspec());
        return productInfoDTO;
    }


    /**
     * 订单详情转ProductInfoDTO
     *
     * @param orderDetailDO
     * @return
     */
    private static ProductInfoDTO OrderDetailDO2ProductInfoDTO(OrderDetailDO orderDetailDO) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProductId(orderDetailDO.getProductSn());
        productInfoDTO.setProductName(orderDetailDO.getFgoodname());
        productInfoDTO.setSuppId(orderDetailDO.getSuppId());
        productInfoDTO.setSuppName(orderDetailDO.getSuppName());
        productInfoDTO.setSuppCode(orderDetailDO.getSuppCode());
        productInfoDTO.setPurchaseAmount(orderDetailDO.getFbidamount());
        productInfoDTO.setProductPrice(orderDetailDO.getFbidprice());
        productInfoDTO.setProductQuantity(orderDetailDO.getFquantity().intValue());
        productInfoDTO.setProductCode(orderDetailDO.getFgoodcode());
        productInfoDTO.setBrandId(orderDetailDO.getFbrandid());
        productInfoDTO.setBrandName(orderDetailDO.getFbrand());
        productInfoDTO.setFirstLevelCategoryId(Long.valueOf(orderDetailDO.getFirstCategoryId()));
        productInfoDTO.setFirstLevelCategoryName(orderDetailDO.getFirstCategoryName());
        productInfoDTO.setSecondLevelCategoryId(Long.valueOf(orderDetailDO.getSecondCategoryId()));
        productInfoDTO.setSecondLevelCategoryName(orderDetailDO.getSecondCategoryName());
        productInfoDTO.setThirdLevelCategoryId(Long.valueOf(orderDetailDO.getCategoryid()));
        productInfoDTO.setThirdLevelCategoryName(orderDetailDO.getFclassification());
        productInfoDTO.setDirectoryId(orderDetailDO.getCategoryDirectoryId());
        productInfoDTO.setSpec(orderDetailDO.getFspec());
        return productInfoDTO;
    }

    /**
     * 订单转订单限额入参
     *
     * @param orderMasterDO
     * @param productInfoDTOS
     * @return
     */
    public static OrderLimitParamDTO orderMaster2OrderLimitParamDTO(OrderMasterDO orderMasterDO, List<ProductInfoDTO> productInfoDTOS, List<FundCardParamDTO> fundCardParamDTOS) {
        OrderLimitParamDTO orderLimitParamDTO = new OrderLimitParamDTO();
        orderLimitParamDTO.setBizId(orderMasterDO.getId());
        String originalOrderNo = OrderCommonUtils.isSplitOrder(orderMasterDO.getForderno()) ? orderMasterDO.getForderno().substring(0, orderMasterDO.getForderno().length() - 1) : orderMasterDO.getForderno();
        orderLimitParamDTO.setBizNo(originalOrderNo);
        orderLimitParamDTO.setDepartmentId(orderMasterDO.getFbuydepartmentid());
        orderLimitParamDTO.setDepartmentName(orderMasterDO.getFbuydepartment());
        orderLimitParamDTO.setBuyerId(orderMasterDO.getFbuyerid());
        orderLimitParamDTO.setBuyerName(orderMasterDO.getFbuyername());
        orderLimitParamDTO.setOrderTime(LocalDateTime.ofInstant(orderMasterDO.getCreateTime().toInstant(), ZoneId.systemDefault()));
        orderLimitParamDTO.setSuppId(orderMasterDO.getFsuppid());
        orderLimitParamDTO.setSuppName(orderMasterDO.getFsuppname());
        // 订单商品信息
        orderLimitParamDTO.setProductInfoDTOS(productInfoDTOS);
        //经费卡信息
        orderLimitParamDTO.setFundCardParamDTOS(fundCardParamDTOS);
        return orderLimitParamDTO;
    }

    /**
     * 生成采购限额管控报错信息
     *
     * @param resultDTO
     * @param orderDetailDOList
     * @return
     */
    public static RiskRuleLimitErrorInfoVO buildErrorInfo(RiskRuleLimitControlResultDTO resultDTO, List<OrderDetailDO> orderDetailDOList) {
        List<RiskRuleLimitControlErrInfoDTO> errorInfoList = resultDTO.getRiskRuleLimitControlErrInfoDTOS();
        List<RiskRuleLimitErrorInfoVO.ProductInfo> disablePurchaseList = errorInfoList.stream()
                .map(errorInfo -> {
                    List<String> productNameList = orderDetailDOList.stream()
                            .map(OrderDetailDO::getFgoodname).collect(Collectors.toList());
                    return new RiskRuleLimitErrorInfoVO.ProductInfo(productNameList, errorInfo.getSuppName(), errorInfo.getErrorNoticeMsg());
                })
                .collect(Collectors.toList());
        RiskRuleLimitErrorInfoVO errorInfoVO = new RiskRuleLimitErrorInfoVO();
        errorInfoVO.setDisablePurchaseList(disablePurchaseList);
        return errorInfoVO;
    }


    /**
     * 生成采购限额管控校验报错信息
     *
     * @param resultDTO
     * @param operateTypeEnum
     * @param orgId
     * @param orderErrInfoListMap
     * @return
     */
    public static RiskRuleLimitCheckErrorInfoVO buildCheckErrorInfo(RiskRuleLimitControlResultDTO resultDTO,
                                                                    LimitControlOperateTypeEnum operateTypeEnum,
                                                                    Integer orgId,
                                                                    Map<String, List<RiskRuleLimitControlErrInfoDTO>> orderErrInfoListMap) {
        RiskRuleLimitCheckErrorInfoVO errorInfoVO = new RiskRuleLimitCheckErrorInfoVO();
        errorInfoVO.setOperateType(operateTypeEnum.getValue());
        errorInfoVO.setValidateNoticeType(resultDTO.getValidateNoticeType());
        errorInfoVO.setPurchaseAmountLimitType(resultDTO.getPurchaseAmountLimitType());
        errorInfoVO.setOrgId(orgId);
        List<RiskRuleLimitControlErrInfoDTO> errorInfoList = resultDTO.getRiskRuleLimitControlErrInfoDTOS();
        Preconditions.isTrue(CollectionUtils.isNotEmpty(errorInfoList), "限额管控校验异常，但RPC调用返回的错误信息为空！");

        // 初始化异常的商品信息
        List<RiskRuleLimitCheckErrorInfoVO.ProductInfo> productInfoList = New.list();
        orderErrInfoListMap.forEach((orderNo, errInfoList) -> {
            // 按订单区分
            for (RiskRuleLimitControlErrInfoDTO errorInfo : errInfoList) {
                RiskRuleLimitCheckErrorInfoVO.ProductInfo productInfo = new RiskRuleLimitCheckErrorInfoVO.ProductInfo();
                productInfo.setRuleId(errorInfo.getRuleId());
                productInfo.setOrderNo(orderNo);
                productInfo.setProductId(errorInfo.getProductId());
                productInfo.setProductName(errorInfo.getProductName());
                productInfo.setSuppName(errorInfo.getSuppName());
                productInfo.setItem(errorInfo.getErrorNoticeMsg());
                productInfo.setAverageProductPrice(errorInfo.getAverageProductPrice());
                productInfo.setProductCategoryName(errorInfo.getProductCategoryName());
                productInfoList.add(productInfo);
            }
        });
        errorInfoVO.setDisablePurchaseList(productInfoList);
        return errorInfoVO;
    }
}
