// ==UserScript==
// @name         钉钉多维表格自动筛选-任务人员(黄有望)
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动在钉钉多维表格中筛选任务人员为"黄有望"
// <AUTHOR>
// @match        https://alidocs.dingtalk.com/i/nodes/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置项
    const CONFIG = {
        userName: '黄有望',
        filterFieldName: '任务人员',
        maxWaitTime: 15000,
        checkInterval: 500,
        retryCount: 3
    };
    
    console.log('钉钉多维表格自动筛选脚本已加载 - 筛选用户:', CONFIG.userName);
    
    // 等待元素出现
    function waitForElement(selector, timeout = CONFIG.maxWaitTime) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) {
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                    return;
                }
                
                setTimeout(check, CONFIG.checkInterval);
            }
            
            check();
        });
    }
    
    // 模拟点击
    function simulateClick(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const events = [
            new MouseEvent('mousedown', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            }),
            new MouseEvent('mouseup', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            }),
            new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            })
        ];
        
        events.forEach(event => element.dispatchEvent(event));
        return true;
    }
    
    // 查找包含特定文本的元素
    function findElementByText(text, tagNames = ['*']) {
        const xpath = tagNames.map(tag => 
            `//${tag}[contains(text(), "${text}")]`
        ).join(' | ');
        
        const result = document.evaluate(
            xpath,
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        );
        
        return result.singleNodeValue;
    }
    
    // 等待iframe加载完成并获取iframe文档
    function waitForIframe() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 20;

            const checkIframe = () => {
                attempts++;
                const iframe = document.querySelector('#wiki-notable-iframe');

                if (iframe && iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                    console.log('iframe文档已完全加载');
                    resolve(iframe.contentDocument);
                    return;
                }

                if (attempts >= maxAttempts) {
                    reject(new Error('iframe加载超时'));
                    return;
                }

                setTimeout(checkIframe, 500);
            };

            checkIframe();
        });
    }

    // 在iframe中查找元素
    function findElementInIframe(iframeDoc, text) {
        const xpath = `//*[contains(text(), "${text}")]`;
        const result = iframeDoc.evaluate(
            xpath,
            iframeDoc,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        );
        return result.singleNodeValue;
    }
    
    // 主要的自动筛选函数
    async function autoFilter() {
        let retryCount = 0;
        
        while (retryCount < CONFIG.retryCount) {
            try {
                console.log(`开始自动筛选 (尝试 ${retryCount + 1}/${CONFIG.retryCount})...`);
                
                // 等待页面完全加载
                await new Promise(resolve => setTimeout(resolve, 4000));
                
                // 等待iframe加载
                const iframeDoc = await waitForIframe();
                console.log('iframe已加载');
                
                // 在iframe中查找"添加条件"按钮
                const addConditionBtn = findElementInIframe(iframeDoc, '添加条件');
                
                if (!addConditionBtn) {
                    throw new Error('未找到"添加条件"按钮');
                }
                
                console.log('找到"添加条件"按钮');
                simulateClick(addConditionBtn);
                
                // 等待字段选择下拉框出现
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 查找并点击"任务人员"选项
                const taskPersonOption = findElementInIframe(iframeDoc, '任务人员');
                
                if (!taskPersonOption) {
                    throw new Error('未找到"任务人员"选项');
                }
                
                console.log('找到"任务人员"选项');
                simulateClick(taskPersonOption);
                
                // 等待条件设置完成
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 查找"请选择"下拉框
                const selectXPath = '//*[contains(text(), "请选择")]';
                const selectResult = iframeDoc.evaluate(
                    selectXPath,
                    iframeDoc,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null
                );
                const selectDropdown = selectResult.singleNodeValue;
                
                if (!selectDropdown) {
                    throw new Error('未找到"请选择"下拉框');
                }
                
                console.log('找到"请选择"下拉框');
                simulateClick(selectDropdown);
                
                // 等待人员选项出现
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 查找并选择"黄有望"
                const userXPath = `//*[contains(text(), "${CONFIG.userName}")]`;
                const userResult = iframeDoc.evaluate(
                    userXPath,
                    iframeDoc,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null
                );
                const userOption = userResult.singleNodeValue;
                
                if (!userOption) {
                    throw new Error(`未找到"${CONFIG.userName}"选项`);
                }
                
                console.log(`找到"${CONFIG.userName}"选项`);
                simulateClick(userOption);
                
                // 等待筛选生效
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                console.log('自动筛选完成！');
                return true;
                
            } catch (error) {
                console.error(`筛选失败 (尝试 ${retryCount + 1}):`, error.message);
                retryCount++;
                
                if (retryCount < CONFIG.retryCount) {
                    console.log(`等待 3 秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            }
        }
        
        console.error('所有重试都失败了');
        return false;
    }
    
    // 检查是否是目标页面
    function isTargetPage() {
        const url = window.location.href;
        return url.includes('alidocs.dingtalk.com/i/nodes/');
    }
    
    // 初始化函数
    function init() {
        if (!isTargetPage()) {
            return;
        }
        
        console.log('检测到钉钉多维表格页面');
        
        // 延迟执行，确保页面完全加载
        setTimeout(() => {
            autoFilter();
        }, 5000);
    }
    
    // 监听页面变化（适用于单页应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            console.log('页面URL变化，重新初始化');
            setTimeout(init, 3000);
        }
    }).observe(document, { subtree: true, childList: true });
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
