// ==UserScript==
// @name         钉钉多维表格自动筛选-黄有望(API版本)
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  通过API直接在钉钉多维表格中筛选任务人员为"黄有望"
// <AUTHOR>
// @match        https://alidocs.dingtalk.com/i/nodes/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置项
    const CONFIG = {
        userName: '黄有望',
        filterFieldName: '任务人员',
        maxWaitTime: 20000,
        checkInterval: 1000,
        retryCount: 5
    };
    
    console.log('🚀 钉钉多维表格自动筛选脚本已加载 - 筛选用户:', CONFIG.userName);
    
    // 等待元素出现
    function waitForElement(selector, timeout = CONFIG.maxWaitTime) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) {
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                    return;
                }
                
                setTimeout(check, CONFIG.checkInterval);
            }
            
            check();
        });
    }
    
    // 等待iframe加载完成
    function waitForIframe() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 30;
            
            const checkIframe = () => {
                attempts++;
                const iframe = document.querySelector('#wiki-notable-iframe');
                
                if (iframe && iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                    console.log('✅ iframe文档已完全加载');
                    resolve(iframe.contentDocument);
                    return;
                }
                
                if (attempts >= maxAttempts) {
                    reject(new Error('iframe加载超时'));
                    return;
                }
                
                setTimeout(checkIframe, 500);
            };
            
            checkIframe();
        });
    }
    
    // 通过API直接添加筛选条件
    async function addFilterViaAPI() {
        try {
            console.log('🔍 尝试通过API添加筛选条件...');
            
            // 等待iframe加载
            const iframeDoc = await waitForIframe();
            const iframeWindow = iframeDoc.defaultView;
            
            // 查找全局配置对象
            let globalConfig = null;
            if (iframeWindow && iframeWindow.__globalConfig) {
                globalConfig = iframeWindow.__globalConfig;
                console.log('✅ 找到全局配置对象');
            }
            
            // 尝试查找视图管理器或数据存储
            const possibleManagers = [];
            for (let key in iframeWindow) {
                if (key.toLowerCase().includes('manager') || 
                    key.toLowerCase().includes('store') ||
                    key.toLowerCase().includes('service')) {
                    try {
                        const obj = iframeWindow[key];
                        if (obj && typeof obj === 'object') {
                            possibleManagers.push({
                                key: key,
                                obj: obj,
                                hasFilter: JSON.stringify(obj).toLowerCase().includes('filter')
                            });
                        }
                    } catch (e) {
                        // 忽略访问错误
                    }
                }
            }
            
            console.log('📋 找到可能的管理器对象:', possibleManagers.length);
            
            // 尝试直接调用筛选API
            const currentUrl = window.location.href;
            const urlParams = new URLSearchParams(window.location.search);
            const sheetId = urlParams.get('sheetId') || 'u9OQr6I';
            const viewId = urlParams.get('viewId') || 'zsCXeDE';
            
            // 构造筛选条件
            const filterCondition = {
                field: CONFIG.filterFieldName,
                operator: 'equals',
                value: CONFIG.userName
            };
            
            console.log('🎯 准备添加筛选条件:', filterCondition);
            
            // 尝试多种API调用方式
            const apiMethods = [
                // 方法1: 直接调用可能的筛选API
                async () => {
                    const response = await fetch(`/nt/api/sheets/${sheetId}/filter`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            viewId: viewId,
                            filters: [filterCondition]
                        })
                    });
                    return response;
                },
                
                // 方法2: 通过视图更新API
                async () => {
                    const response = await fetch(`/nt/api/views/${viewId}/update`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            filters: [filterCondition]
                        })
                    });
                    return response;
                },
                
                // 方法3: 通过iframe内的方法
                async () => {
                    if (iframeWindow && typeof iframeWindow.addFilter === 'function') {
                        return iframeWindow.addFilter(filterCondition);
                    }
                    throw new Error('addFilter method not found');
                }
            ];
            
            // 尝试各种API方法
            for (let i = 0; i < apiMethods.length; i++) {
                try {
                    console.log(`🔄 尝试API方法 ${i + 1}...`);
                    const result = await apiMethods[i]();
                    console.log('✅ API调用成功:', result);
                    return true;
                } catch (error) {
                    console.log(`❌ API方法 ${i + 1} 失败:`, error.message);
                }
            }
            
            console.log('⚠️ 所有API方法都失败，回退到UI操作');
            return false;
            
        } catch (error) {
            console.error('❌ API筛选失败:', error);
            return false;
        }
    }
    
    // 模拟点击操作（备用方案）
    function simulateClick(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const events = [
            new MouseEvent('mousedown', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            }),
            new MouseEvent('mouseup', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            }),
            new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            })
        ];
        
        events.forEach(event => element.dispatchEvent(event));
        return true;
    }
    
    // 在iframe中查找元素
    function findElementInIframe(iframeDoc, text) {
        const xpath = `//*[contains(text(), "${text}")]`;
        const result = iframeDoc.evaluate(
            xpath,
            iframeDoc,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        );
        return result.singleNodeValue;
    }
    
    // UI操作备用方案
    async function addFilterViaUI() {
        try {
            console.log('🖱️ 使用UI操作添加筛选条件...');
            
            const iframeDoc = await waitForIframe();
            
            // 查找"添加条件"按钮
            const addConditionBtn = findElementInIframe(iframeDoc, '添加条件');
            if (!addConditionBtn) {
                throw new Error('未找到"添加条件"按钮');
            }
            
            console.log('✅ 找到"添加条件"按钮');
            simulateClick(addConditionBtn);
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找并点击"任务人员"选项
            const taskPersonOption = findElementInIframe(iframeDoc, '任务人员');
            if (!taskPersonOption) {
                throw new Error('未找到"任务人员"选项');
            }
            
            console.log('✅ 找到"任务人员"选项');
            simulateClick(taskPersonOption);
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找"请选择"下拉框
            const selectDropdown = findElementInIframe(iframeDoc, '请选择');
            if (!selectDropdown) {
                throw new Error('未找到"请选择"下拉框');
            }
            
            console.log('✅ 找到"请选择"下拉框');
            simulateClick(selectDropdown);
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找并选择"黄有望"
            const userOption = findElementInIframe(iframeDoc, CONFIG.userName);
            if (!userOption) {
                throw new Error(`未找到"${CONFIG.userName}"选项`);
            }
            
            console.log(`✅ 找到"${CONFIG.userName}"选项`);
            simulateClick(userOption);
            
            console.log('🎉 UI操作完成！');
            return true;
            
        } catch (error) {
            console.error('❌ UI操作失败:', error);
            return false;
        }
    }
    
    // 主要的自动筛选函数
    async function autoFilter() {
        let retryCount = 0;
        
        while (retryCount < CONFIG.retryCount) {
            try {
                console.log(`🔄 开始自动筛选 (尝试 ${retryCount + 1}/${CONFIG.retryCount})...`);
                
                // 等待页面完全加载
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // 首先尝试API方式
                const apiSuccess = await addFilterViaAPI();
                if (apiSuccess) {
                    console.log('🎉 API筛选成功！');
                    return true;
                }
                
                // API失败，尝试UI操作
                const uiSuccess = await addFilterViaUI();
                if (uiSuccess) {
                    console.log('🎉 UI筛选成功！');
                    return true;
                }
                
                throw new Error('API和UI操作都失败');
                
            } catch (error) {
                console.error(`❌ 筛选失败 (尝试 ${retryCount + 1}):`, error.message);
                retryCount++;
                
                if (retryCount < CONFIG.retryCount) {
                    console.log(`⏳ 等待 3 秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            }
        }
        
        console.error('💥 所有重试都失败了');
        return false;
    }
    
    // 检查是否是目标页面
    function isTargetPage() {
        const url = window.location.href;
        return url.includes('alidocs.dingtalk.com/i/nodes/');
    }
    
    // 初始化函数
    function init() {
        if (!isTargetPage()) {
            return;
        }
        
        console.log('🎯 检测到钉钉多维表格页面');
        
        // 延迟执行，确保页面完全加载
        setTimeout(() => {
            autoFilter();
        }, 3000);
    }
    
    // 监听页面变化（适用于单页应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            console.log('🔄 页面URL变化，重新初始化');
            setTimeout(init, 3000);
        }
    }).observe(document, { subtree: true, childList: true });
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
