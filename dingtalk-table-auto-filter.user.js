// ==UserScript==
// @name         钉钉多维表格自动筛选-任务人员
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动在钉钉多维表格中筛选任务人员为"小鸣"
// <AUTHOR>
// @match        https://alidocs.dingtalk.com/i/nodes/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置项
    const CONFIG = {
        userName: '黄有望',
        filterFieldName: '任务人员',
        maxWaitTime: 15000,
        checkInterval: 500,
        retryCount: 3
    };

    console.log('钉钉多维表格自动筛选脚本已加载');
    
    // 等待元素出现
    function waitForElement(selector, timeout = CONFIG.maxWaitTime) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) { // 确保元素可见
                    resolve(element);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                    return;
                }

                setTimeout(check, CONFIG.checkInterval);
            }

            check();
        });
    }

    // 等待多个选择器中的任一个
    function waitForAnyElement(selectors, timeout = CONFIG.maxWaitTime) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element && element.offsetParent !== null) {
                        resolve({ element, selector });
                        return;
                    }
                }

                if (Date.now() - startTime > timeout) {
                    reject(new Error(`None of the elements found within ${timeout}ms`));
                    return;
                }

                setTimeout(check, CONFIG.checkInterval);
            }

            check();
        });
    }
    
    // 模拟点击
    function simulateClick(element) {
        if (!element) return false;

        const rect = element.getBoundingClientRect();
        const events = [
            new MouseEvent('mousedown', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            }),
            new MouseEvent('mouseup', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            }),
            new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            })
        ];

        events.forEach(event => element.dispatchEvent(event));
        return true;
    }

    // 查找包含特定文本的元素
    function findElementByText(text, tagNames = ['*']) {
        const xpath = tagNames.map(tag =>
            `//${tag}[contains(text(), "${text}")]`
        ).join(' | ');

        const result = document.evaluate(
            xpath,
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        );

        return result.singleNodeValue;
    }
    
    // 主要的自动筛选函数
    async function autoFilter() {
        try {
            console.log('开始自动筛选...');
            
            // 等待页面加载完成
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 常见的筛选按钮选择器（需要根据实际页面调整）
            const filterButtonSelectors = [
                '[data-testid="filter-button"]',
                '.filter-button',
                '[title*="筛选"]',
                '[aria-label*="筛选"]',
                'button[class*="filter"]',
                '.toolbar button:contains("筛选")',
                // 添加更多可能的选择器
            ];
            
            // 查找筛选按钮
            let filterButton;
            try {
                const result = await waitForAnyElement(filterButtonSelectors);
                filterButton = result.element;
                console.log('找到筛选按钮:', result.selector);
            } catch (error) {
                console.log('未找到筛选按钮，尝试其他方法...');
                // 可以在这里添加其他查找筛选功能的方法
                return;
            }
            
            // 点击筛选按钮
            simulateClick(filterButton);
            console.log('已点击筛选按钮');
            
            // 等待筛选面板出现
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找字段选择器或输入框
            const inputSelectors = [
                'input[placeholder*="请输入"]',
                'input[placeholder*="筛选"]',
                '.filter-input input',
                '.search-input input',
                '[data-testid="filter-input"]'
            ];
            
            let inputElement;
            try {
                const result = await waitForAnyElement(inputSelectors);
                inputElement = result.element;
                console.log('找到输入框:', result.selector);
            } catch (error) {
                console.log('未找到输入框');
                return;
            }
            
            // 输入筛选条件
            simulateInput(inputElement, CONFIG.userName);
            console.log('已输入筛选条件:', CONFIG.userName);
            
            // 等待一下，然后查找确认按钮
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const confirmButtonSelectors = [
                'button:contains("确定")',
                'button:contains("确认")',
                'button:contains("应用")',
                '[data-testid="confirm-button"]',
                '.confirm-button',
                'button[class*="confirm"]'
            ];
            
            // 查找并点击确认按钮
            for (const selector of confirmButtonSelectors) {
                const confirmButton = document.querySelector(selector);
                if (confirmButton) {
                    simulateClick(confirmButton);
                    console.log('已点击确认按钮');
                    break;
                }
            }
            
            console.log('自动筛选完成');
            
        } catch (error) {
            console.error('自动筛选失败:', error);
        }
    }
    
    // 检查是否是多维表格页面
    function isTablePage() {
        const url = window.location.href;
        // 根据实际的钉钉多维表格URL模式调整
        return url.includes('dingtalk.com') && 
               (url.includes('table') || url.includes('sheet') || url.includes('alidocs'));
    }
    
    // 页面加载完成后执行
    function init() {
        if (!isTablePage()) {
            return;
        }
        
        console.log('检测到钉钉多维表格页面，准备自动筛选...');
        
        // 延迟执行，确保页面完全加载
        setTimeout(() => {
            autoFilter();
        }, 3000);
    }
    
    // 监听页面变化（适用于单页应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            setTimeout(init, 2000);
        }
    }).observe(document, { subtree: true, childList: true });
    
    // 初始化
    init();
    
})();
