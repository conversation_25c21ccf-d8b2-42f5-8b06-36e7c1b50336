package com.ruijing.order.utils;

import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 转换器通用工具类
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/2 15:28
 **/
public class TranslateUtils {

    /**
     * 转换器，批量
     * @param supplier  型参数组
     * @param function  转换型参函数
     * @param <T>       原型形参
     * @param <R>       结果类型
     * @return          数组结果
     */
    public static <T, R> List<R> toModelList(Supplier<List<T>> supplier,
                                             Function<? super T, ? extends R> function) {
        List<T> prototypeList = supplier.get();
        if (CollectionUtils.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }
        List<R> result = prototypeList.stream().map(t -> TranslateUtils.toModel(t, function)).collect(Collectors.toList());
        return result;
    }

    /**
     * 转换器
     * @param prototype 型参
     * @param function  转换型参函数
     * @param <T>       原型形参
     * @param <R>       结果类型
     * @return          结果
     */
    public static <T, R> R toModel(T prototype,
                                   Function<? super T, ? extends R> function) {
        R r = function.apply(prototype);
        return r;
    }
}
